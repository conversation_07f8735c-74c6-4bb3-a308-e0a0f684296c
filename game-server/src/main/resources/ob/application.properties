### app name ###
spring.application.name=monster
### app environment ###
spring.application.env=ob
### country ###
game.country=en
### game config path ###
game.config.path=gameconf/
### log path ###
logging.path=/var/log/rpground/gameserver
### game config url ###
game.config.url=http://config.gorilla10.com/export/downLoad
### game config reload secret ###
game.config.reload.secret=742a030e6236291c
### log level ###
logging.level.root=info
### gm key ###
game.gm.key=da2224bc39b3a1da9be209ba3bad9d28
### jmx servr port ###
jmx.port=12355
### \u662F\u5426\u8F93\u51FA api \u65E5\u5FD7 ###
game.api.log.open=1
### \u662F\u5426\u4FDD\u5B58access_log ###
game.api.access.log=0
### \u662F\u5426\u4F7F\u7528\u6807\u51C6\u8F93\u51FA\u8BB0\u5F55\u65E5\u5FD7 ###
logging.use.std.output=1
### \u6218\u6597\u670D\u5730\u5740 ###
battleService=${BATTLE_SERVICE_URL:internal-ob-monster-rpg-battle.badguysvc.com}:${BATTLE_SERVICE_PORT:443}
chapterVerify=1
#battleService=************:13666

slg.instance=1

spring.jackson.date-format=yyyy-MM-dd HH:mm:ss
spring.jackson.time-zone=GMT+8
spring.jmx.enabled=false

# netty\u914D\u7F6E
netty.port=8010
netty.maxThreads=1024
netty.max_frame_length=65535

#Okhttp\u914D\u7F6E
ok.http.connect-timeout=30
ok.http.read-timeout=30
ok.http.write-timeout=30
# \u8FDE\u63A5\u6C60\u4E2D\u6574\u4F53\u7684\u7A7A\u95F2\u8FDE\u63A5\u7684\u6700\u5927\u6570\u91CF
ok.http.max-idle-connections=64
# \u8FDE\u63A5\u7A7A\u95F2\u65F6\u95F4\u6700\u591A\u4E3A 50 \u79D2
ok.http.keep-alive-time=60
ok.http.proxy.address=
ok.http.proxy.port=


######### redis config ######################
spring.redis.database=0
spring.redis.host=${REDIS_HOST:127.0.0.1}
spring.redis.port=${REDIS_PORT:6379}
spring.redis.password=
spring.redis.lettuce.pool.max-active=8
spring.redis.lettuce.pool.max-idle=8
spring.redis.lettuce.pool.min-idle=8
spring.redis.lettuce.pool.max-wait=6000
spring.redis.publish.channel=dxx_game

### aws\u914D\u7F6E
aws.profile.name=
aws.credentials.accessKey=
aws.credentials.secretKey=
aws.region.static=eu-central-1
### kinesis\u4F5C\u5F0A\u65E5\u5FD7
aws.kinesis.stream.name.cheat.log=
### kinesis\u8D44\u6E90\u65E5\u5FD7
aws.kinesis.stream.name.resource.log=${KINESIS_RESOURCE_LOG:}
### s3\u6E38\u620F\u914D\u7F6E\u6587\u4EF6\u5730\u5740
aws.s3.game.config.bucket.name=${S3_GAME_CONFIG_BUCKET_NAME:}
### aws dynamodb \u914D\u7F6E
aws.dynamodb.endpoint.url=https://dynamodb.eu-central-1.amazonaws.com
### print dynamod partiql log
aws.dynamodb.partiql.log=true
### open search endpoint
aws.opensearch.domain.endpoint=${OPEN_SEARCH_DOMAIN_ENDPOINT:}
### open search user
aws.opensearch.user=${OPEN_SEARCH_USER:}
### open search password
aws.opensearch.password=${OPEN_SEARCH_PASSWORD:}
### eks pod\u70B9\u7528
aws.opensearch.password_secret_name=${OPEN_SEARCH_PASSWORD_SECRET_NAME:}
### open search guild index name
aws.opensearch.index.guild=${OPEN_SEARCH_INDEX_GUILD:}