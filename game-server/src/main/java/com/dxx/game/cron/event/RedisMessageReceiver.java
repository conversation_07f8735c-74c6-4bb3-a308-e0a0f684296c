package com.dxx.game.cron.event;

import com.dxx.game.common.config.game.event.RefreshConfigEvent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * @author: lsc
 * @createDate: 2024/4/13
 * @description:
 */
@Component
public class RedisMessageReceiver {

    @Autowired
    private ApplicationContext applicationContext;
    @Autowired
    private ApplicationEventPublisher applicationEventPublisher;

    public void onMessage(String message) {
        Map<String, Object> annotatedBeans = applicationContext.getBeansWithAnnotation(SpringBootApplication.class);
        if (annotatedBeans.containsKey("logicCronServerApplication")) {
            if (message.equals("reload_config")) {
                applicationEventPublisher.publishEvent(new RefreshConfigEvent(this));
            }
        }
    }
}
