package com.dxx.game.common.server.handler;

import java.lang.reflect.Method;

import com.dxx.game.common.server.annotation.ApiMethod;
import com.dxx.game.common.server.model.Request;
import com.dxx.game.common.server.model.Response;
import com.dxx.game.common.server.model.Result;
import com.google.protobuf.Message;
import lombok.Getter;

/**
 * 请求处理器
 * <AUTHOR>
 * @since 2019年12月5日
 */
@Getter
public class RequestProcessor {

	/**
	 * 命令号
	 */
	private short command;

	/**
	 * 方法名
	 */
	private String name;

	/**
	 * 方法
	 */
	private Method method;

	/**
	 * 类对象
	 */
	private Object classObj;

	/**
	 * 返回值类型是否是void
	 */
	private boolean isVoid = false;

	/**
	 * 是否禁用redis锁
	 */
	private boolean isDisableRedisLock = false;

	private ApiMethod annotation;


	public RequestProcessor() {

	}

	public RequestProcessor(short command, String name, Method method, Object classObj,
							 boolean disableRedisLock, ApiMethod annotation) {
		this.command = command;
		this.name = name;
		this.method = method;
		this.classObj = classObj;
		this.isVoid = method.getAnnotatedReturnType().getType().equals(Void.TYPE);
		this.isDisableRedisLock = disableRedisLock;
		this.annotation = annotation;
	}

	@SuppressWarnings("unchecked")
	public Result<Message> doProcess(Message requestParams) throws Exception {
		return (Result<Message>) this.method.invoke(classObj, requestParams);
	}

	public void doProcess(Request request, Response response) throws Exception {
		this.method.invoke(classObj, request, response);
	}


	public boolean isVoid() {
		return this.isVoid;
	}
}
