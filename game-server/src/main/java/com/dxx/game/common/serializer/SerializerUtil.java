package com.dxx.game.common.serializer;

import com.dxx.game.common.serializer.code.Codec;
import com.dxx.game.common.serializer.code.kryo.KryoCodec;

/**
 * @Author: pink
 * @Date: 2022/8/30 20:22
 */
public final class SerializerUtil {

    private static final Codec codec = new KryoCodec();

    private SerializerUtil() {
    }

    public static byte[] serialize(Object obj) throws Exception {
        return codec.encode(obj);
    }

    public static Object deserialize(byte[] bytes) throws Exception {
        return codec.decode(bytes);
    }
}
