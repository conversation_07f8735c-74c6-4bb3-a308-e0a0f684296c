package com.dxx.game.common.channel.alipay;

import com.alipay.api.AlipayClient;
import com.alipay.api.DefaultAlipayClient;
import com.alipay.api.domain.AlipayTradeAppPayModel;
import com.alipay.api.internal.util.AlipaySignature;
import com.alipay.api.internal.util.StringUtils;
import com.alipay.api.request.AlipayTradeAppPayRequest;
import com.alipay.api.response.AlipayTradeAppPayResponse;
import com.dxx.game.common.channel.AndroidCallBackService;
import com.dxx.game.common.channel.ChannelService;
import com.dxx.game.common.channel.alipay.model.AliPayConfig;
import com.dxx.game.common.channel.common.config.ChannelConfig;
import com.dxx.game.common.channel.common.consts.AndroidPayCbErrorCode;
import com.dxx.game.common.channel.common.consts.ChannelID;
import com.dxx.game.common.channel.common.model.PayBackParamsModel;
import com.dxx.game.common.channel.common.model.PayCbVo;
import com.dxx.game.common.channel.common.util.PaymentUtils;
import io.netty.handler.codec.http.FullHttpRequest;
import io.netty.handler.codec.http.QueryStringDecoder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.CharEncoding;
import org.apache.commons.codec.Charsets;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.net.URLDecoder;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 支付宝
 * <AUTHOR>
 * @date 2022/4/20 17:04
 */
@Slf4j
@Service
public class AliPayService implements ChannelService {

    @Autowired
    private ChannelConfig channelConfig;
    @Autowired
    private AndroidCallBackService androidCallBackService;

    // 渠道ID
    private static final ChannelID CHANNEL_ID = ChannelID.AliPay;


    /**
     * 创建订单
     */
    public String unifiedOrder(long userId, String packageName, String description, String attach, int amount) {

        try {
            // 测试订单1元钱
            String amt = String.valueOf(amount);
            boolean isWhiteUser = channelConfig.isPayWhiteList(userId);
            if (isWhiteUser) {
                // 测试账号下单为1分钱
                amt = "0.01";
            }

            String cpOrderId = PaymentUtils.createCpOrderId();
            String notifyUrl = channelConfig.getNotifyUrl(CHANNEL_ID);
            attach = URLDecoder.decode(attach, "UTF-8");
            AliPayConfig aliPayConfig = channelConfig.getAliPayConfig();
            AliPayConfig.PackageData packageData = aliPayConfig.getPackages().getOrDefault(packageName, null);
            if (packageData == null) {
                return null;
            }

            AlipayClient alipayClient = new DefaultAlipayClient(aliPayConfig.getServerUrl(),
                    packageData.getAppId(), packageData.getPrivateKey(), aliPayConfig.getFormat(),
                    aliPayConfig.getCharset(), packageData.getPayPublicKey(), aliPayConfig.getSignType());

            AlipayTradeAppPayRequest request = new AlipayTradeAppPayRequest();
            AlipayTradeAppPayModel model = new AlipayTradeAppPayModel();
            model.setBody(description);
            model.setSubject(description);
            model.setOutTradeNo(cpOrderId);
            model.setTimeoutExpress("60m");
            model.setTotalAmount(amt);       // 单位为元
            model.setProductCode("QUICK_MSECURITY_PAY");
            model.setPassbackParams(attach);

            request.setBizModel(model);
            request.setNotifyUrl(notifyUrl);
            AlipayTradeAppPayResponse response = alipayClient.sdkExecute(request);
            if (!response.isSuccess()) {
                return null;
            }
            return response.getBody();
        } catch (Exception e) {
            log.error("payCb unifiedOrder failed, e:", e);
            return null;
        }
    }

    /**
     * 校验订单状态
     * @param request
     * @return
     */
    private PayCbVo checkOrderState(FullHttpRequest request, Map<String, String> params) {
        try {

            String tradeStatus = params.get("trade_status");
            if (StringUtils.isEmpty(tradeStatus) || !tradeStatus.equals("TRADE_SUCCESS")) {
                log.error("payCb trade_status error, params:{}", params);
                return null;
            }

            String appId = params.get("app_id");
            AliPayConfig aliPayConfig = channelConfig.getAliPayConfig();
            String packageName = aliPayConfig.getApps().getOrDefault(appId, null);
            if (StringUtils.isEmpty(packageName)) {
                log.error("payCb appId error, params:{}", params);
                return null;
            }

            AliPayConfig.PackageData packageData = aliPayConfig.getPackages().get(packageName);

            // 验证合法性
            boolean flag = AlipaySignature.rsaCheckV1(params, packageData.getPayPublicKey(),
                    channelConfig.getAliPayConfig().getCharset(), channelConfig.getAliPayConfig().getSignType());
            if (!flag) {
                log.error("payCb check order state failed params:{}", params);
                return null;
            }


            String tradeNo = params.get("trade_no");       // 订单号
            int totalAmount = (int) (Double.parseDouble(params.get("total_amount")) * 100);

            String outTradeNo = params.get("out_trade_no");       // 订单号

            String passBackParams = URLDecoder.decode(params.get("passback_params"), "UTF-8");
            PayBackParamsModel payBackParamsModel = PaymentUtils.formatPassBackParams(passBackParams);

            PayCbVo payCbVo = new PayCbVo();
            payCbVo.setUserId(payBackParamsModel.getUserId());
            payCbVo.setExtraInfo(payBackParamsModel.getExtraInfo());
            payCbVo.setOrderId(tradeNo);
            payCbVo.setCpOrderId(outTradeNo);
            payCbVo.setPreOrderId(payBackParamsModel.getPreOrderId());
            payCbVo.setProductId(payBackParamsModel.getProductId());
            payCbVo.setChannelId(CHANNEL_ID.getId());
            payCbVo.setAmount(totalAmount);
            return payCbVo;
        } catch (Exception e) {
            log.error("payCb rsaCheck failed e:", e);
            return null;
        }
    }

    /**
     * 支付回调接口
     * 成功返回 success 失败返回failure
     */
    @Override
    public Object payCb(FullHttpRequest request) {

        String body = request.content().toString(Charsets.toCharset(CharEncoding.UTF_8));
        QueryStringDecoder queryDecoder = new QueryStringDecoder(body, false);
        Map<String, List<String>> uriAttributes = queryDecoder.parameters();

        Map<String, String> params = new HashMap<>();
        for (Map.Entry<String, List<String>> entry : uriAttributes.entrySet()) {
            String name = entry.getKey();
            List<String> values = entry.getValue();
            String valueStr = "";
            for (int i = 0, len = values.size(); i < len; i ++) {
                valueStr = (i==values.size()-1)?valueStr + values.get(i):valueStr + values.get(i) + ",";
            }

            params.put(name, valueStr);
        }

        // WAIT_BUYER_PAY = 创建交易，等待买家付款
        // TRADE_CLOSED = 未付款交易超时关闭
        // TRADE_SUCCESS = 交易支付成功
        // TRADE_FINISHED = 交易结束，不可退款
        String tradeStatus = params.get("trade_status");
        if (!tradeStatus.equals("TRADE_SUCCESS")) {
            log.error("payCb trade_status error, params:{}", params);
            return "success";
        }

        PayCbVo payCbVo = this.checkOrderState(request, params);
        if (payCbVo == null) {
            return "failure";
        }

        int code = androidCallBackService.doDeliverGoods(payCbVo);
        if (code != AndroidPayCbErrorCode.SUCCESS) {
            log.error("payCb doDeliverGoods failed, body:{}", body);
            return "failure";
        }

        return "success";
    }
}
