package com.dxx.game.common.aws.dynamodb.cache;

import com.dxx.game.common.aws.dynamodb.model.DynamoDBBaseModel;
import com.dxx.game.common.aws.dynamodb.model.DynamoDBModelKeyInfo;
import com.dxx.game.common.aws.dynamodb.model.mapper.DynamoDBMapperRegistry;
import com.dxx.game.common.aws.dynamodb.transaction.DynamoDBTransactionAspectSupport;
import io.netty.util.concurrent.FastThreadLocal;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import software.amazon.awssdk.services.dynamodb.model.AttributeValue;

import java.lang.reflect.Field;
import java.util.HashMap;
import java.util.Map;

/**
 * dynamodb 数据缓存
 * <AUTHOR>
 * @date 2022/5/13 11:53 上午
 */
@SuppressWarnings("unchecked")
public class DynamoDBCacheManager {

    private static final Logger LOGGER = LoggerFactory.getLogger(DynamoDBCacheManager.class);

    private static final FastThreadLocal<Map<String, Object>> caches = new FastThreadLocal<>();
    private static final FastThreadLocal<Map<String, Object>> updateObjs = new FastThreadLocal<>();
    private static final FastThreadLocal<DynamoDBBaseModel> logResourceCache = new FastThreadLocal<>();

    public static <T extends DynamoDBBaseModel> T get(Class<T> object, Object id) {
        if (caches.get() == null) {
            return null;
        }
        String key = DynamoDBBaseModel.getIdentity(object, id);
        if (!caches.get().containsKey(key)) {
            return null;
        }
        return (T) caches.get().get(key);
    }

    public static <T extends DynamoDBBaseModel> void put(T object) {
        if (object == null) {
            return;
        }
        if (caches.get() == null) {
            caches.set(new HashMap<>());
        }
        caches.get().put(object.getIdentity(), object);
    }

    public static <T extends DynamoDBBaseModel> T getUpdateObj(T object) {
        String identity = object.getIdentity();
        if (updateObjs.get() == null) {
            updateObjs.set(new HashMap<>());
        }
        try {
            if (!updateObjs.get().containsKey(identity)) {
                // 从DynamoDBTransactionAspectSupport中获取目标对象类名对应的表信息
                var tableMetadataInfo = DynamoDBTransactionAspectSupport.getTableInfoMap().get(object.getClass().getName());
                // 获取DynamoDB表的主分区键（Partition Key）名称
                var partitionKeyName = tableMetadataInfo.getTableSchema().tableMetadata().primaryPartitionKey();
                // 使用表模式从目标对象提取主分区键的AttributeValue
                var partitionKeyValue = tableMetadataInfo.getTableSchema().attributeValue(object, partitionKeyName);
                // 尝试获取DynamoDB表的主排序键（Sort Key）名称，如果存在
                var sortKeyOption = tableMetadataInfo.getTableSchema().tableMetadata().primarySortKey();
                // 初始化HashMap，用于存储属性名称和AttributeValue映射
                var attributeValuesMap = new HashMap<String, AttributeValue>();
                // 将主分区键及其值放入属性映射HashMap中
                attributeValuesMap.put(partitionKeyName, partitionKeyValue);
                // 如果存在主排序键，则也将其值放入属性映射HashMap中
                sortKeyOption.ifPresent(sortKeyName -> {
                    attributeValuesMap.put(sortKeyName, tableMetadataInfo.getTableSchema().attributeValue(object, sortKeyName));
                });
                // version
                var dynamoDBModelKeyInfo = DynamoDBMapperRegistry.getModelKeyInfo(object.getClass().getName());
                if (dynamoDBModelKeyInfo != null && !StringUtils.isEmpty(dynamoDBModelKeyInfo.getAttributeVersionMethodName())) {
                    String versionName = dynamoDBModelKeyInfo.getAttributeVersionFieldName();
                    attributeValuesMap.put(versionName, tableMetadataInfo.getTableSchema().attributeValue(object, versionName));
                }

                // 利用属性映射构建一个新的Java对象，该对象对应DynamoDB项
                var newItemObject = tableMetadataInfo.getTableSchema().mapToItem(attributeValuesMap);
                // 将原始对象的更新条件复制到新对象中
                newItemObject.setUpdateConditions(object.getUpdateConditions());
                // 将原始对象的更新表达式复制到新对象中
                newItemObject.setUpdateExpressions(object.getUpdateExpressions());
                updateObjs.get().put(identity, newItemObject);
//                DynamoDBModelKeyInfo dynamoDBModelKeyInfo = DynamoDBMapperRegistry.getModelKeyInfo(newObject.getClass().getName());
//                if (dynamoDBModelKeyInfo != null) {
//                    String pkMethodName = dynamoDBModelKeyInfo.getPkMethodName();
//                    String pkFieldName = dynamoDBModelKeyInfo.getPkFieldName();
//                    Object pkValue = object.getClass().getDeclaredField(pkMethodName).invoke(object);
//                    Field pkField = newObject.getClass().getField(pkFieldName);
//                    pkField.setAccessible(true);
//                    pkField.set(newObject, pkValue);
//
//                    if (!StringUtils.isEmpty(dynamoDBModelKeyInfo.getSkMethodName())) {
//                        String skMethodName = dynamoDBModelKeyInfo.getSkMethodName();
//                        String skFieldName = dynamoDBModelKeyInfo.getSkFieldName();
//                        Object skValue = object.getClass().getMethod(skMethodName).invoke(object);
//                        Field skField = newObject.getClass().getField(skFieldName);
//                        skField.setAccessible(true);
//                        skField.set(newObject, skValue);
//                    }
//
//                    if (!StringUtils.isEmpty(dynamoDBModelKeyInfo.getAttributeVersionMethodName())) {
//                        String methodName = dynamoDBModelKeyInfo.getAttributeVersionMethodName();
//                        String fieldName = dynamoDBModelKeyInfo.getAttributeVersionFieldName();
//                        Object value = object.getClass().getMethod(methodName).invoke(object);
//                        Field field = newObject.getClass().getField(fieldName);
//                        field.setAccessible(true);
//                        field.set(newObject, value);
//                    }
//                newObject.setUpdateConditions(object.getUpdateConditions());
//                newObject.setUpdateExpressions(object.getUpdateExpressions());
//                }
            }
        } catch (Exception e) {
            updateObjs.get().put(identity, object);
            LOGGER.error("getUpdateObj newInstance failed, return default object:{}, e:{}", object, e);
        }
        return (T) updateObjs.get().get(identity);
    }


    public static <T extends DynamoDBBaseModel> void putUpdateObj(T object) {
        String identity = object.getIdentity();
        if (updateObjs.get() == null) {
            updateObjs.set(new HashMap<>());
        }
        updateObjs.get().put(identity, object);
    }

    /**
     * 资源日志对象
     * @return
     */
    @SuppressWarnings("unchecked")
    public static <T extends DynamoDBBaseModel> T getLogResourceCache() {
        return (T) logResourceCache.get();
    }

    /**
     * 保存资源日志对象
     * @param object
     * @param <T>
     */
    public static <T extends DynamoDBBaseModel> void setLogResourceCache(T object) {
        logResourceCache.set(object);
    }

    /**
     * 查询结果放入缓存
     * @param cacheKey
     * @param object
     * @param <T>
     */
    public static <T extends DynamoDBBaseModel> void put(String cacheKey, T object) {
        if (object == null) {
            return;
        }
        if (caches.get() == null) {
            caches.set(new HashMap<>());
        }
        caches.get().put(cacheKey, object);
        put(object);
    }

    /**
     * 获取查询结果
     * @param cacheKey
     * @param <T>
     * @return
     */
    public static <T extends DynamoDBBaseModel> T get(String cacheKey) {
        if (caches.get() == null) {
            return null;
        }
        if (!caches.get().containsKey(cacheKey)) {
            return null;
        }
        return (T) caches.get().get(cacheKey);
    }


    public static void clear() {
        caches.remove();
        logResourceCache.remove();
        updateObjs.remove();
    }
}
