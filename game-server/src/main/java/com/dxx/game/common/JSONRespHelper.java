package com.dxx.game.common;

import com.google.api.client.util.Maps;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/8/20 19:15
 */
public class JSONRespHelper {

    public static final int SUCCESS = 0;
    public static final int FAIL = 1;

    public static Map<String, Object> error(int code, String msg) {
        Map<String, Object> result = Maps.newHashMap();
        result.put("code", code);
        result.put("message", msg);
        return result;
    }

    public static Map<String, Object> success(Map<String, Object> data) {
        Map<String, Object> result = Maps.newHashMap();
        result.put("code", SUCCESS);
        result.put("message", "Success");
        result.put("data", data);
        return result;
    }
}
