package com.dxx.game.common.httpclient;

import java.io.IOException;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import okhttp3.Interceptor;
import okhttp3.Request;
import okhttp3.Response;

public class OkHttpRetryIntercepter implements Interceptor {

	private static final Logger logger = LoggerFactory.getLogger(OkHttpRetryIntercepter.class);

	public int maxRetryCount;

	public OkHttpRetryIntercepter(int maxRetryCount) {
		this.maxRetryCount = maxRetryCount;
	}

	@Override
	public Response intercept(Chain chain) throws IOException {
		int retryCount = 0;
		return retry(chain, retryCount);
	}

	@SuppressWarnings("resource")
	private Response retry(Chain chain, int retryCount) {
		Response response = null;
		Request request = chain.request();

		try {
			response = chain.proceed(request);

			while ((response.code() == 408 || response.code() >= 500) && retryCount < maxRetryCount) {
				retryCount ++;
				logger.error("OkHttpClient retry count : {}, url :{}", retryCount, request.url().toString());
				return retry(chain, retryCount);
			}

		} catch (Exception e) {
			while (retryCount < maxRetryCount) {
				retryCount ++;
				logger.error("OkHttpClient retry count : {}, cause : {}, url :{}", retryCount, e.getMessage(), request.url().toString());
				return retry(chain, retryCount);
			}
		}
		return response;
	}
}
