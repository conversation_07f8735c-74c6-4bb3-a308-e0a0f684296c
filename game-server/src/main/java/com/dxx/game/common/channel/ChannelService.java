package com.dxx.game.common.channel;

import io.netty.handler.codec.http.FullHttpRequest;

/**
 * <AUTHOR>
 * @date 2022/4/26 17:01
 */
public interface ChannelService {


    /**
     * 支付回调
     * @param request
     * @return
     */
    Object payCb(FullHttpRequest request);

    /**
     * 验证登录
     * @param verification
     * @return
     */
    default boolean verifyLogin(String accountId, String verification) {
        return false;
    }
}
