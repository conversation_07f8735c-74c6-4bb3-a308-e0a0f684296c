package com.dxx.game.common.channel.dxx;

import com.dxx.game.common.channel.ChannelService;
import com.dxx.game.common.channel.common.config.ChannelConfig;
import io.netty.handler.codec.http.FullHttpRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @authoer: lsc
 * @createDate: 2022/6/28
 * @description:
 */
@Slf4j
@Service
public class DxxService implements ChannelService {

    @Autowired
    private ChannelConfig channelConfig;

    @Override
    public Object payCb(FullHttpRequest request) {
        return null;
    }

    // test key
    @Override
    public boolean verifyLogin(String accountId, String verification) {
        if (channelConfig.isTest() || !channelConfig.getOfficialConfig().isDxxVerifyLogin()) {
            return true;
        }
        if (StringUtils.isEmpty(verification) || !verification.equals(channelConfig.getOfficialConfig().getLoginPassSecret())) {
            return false;
        }
        return true;
    }
}
