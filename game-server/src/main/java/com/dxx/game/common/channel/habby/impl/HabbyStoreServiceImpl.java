package com.dxx.game.common.channel.habby.impl;

import com.alibaba.fastjson.JSONObject;
import com.dxx.game.common.aws.dynamodb.transaction.annotation.DynamoDBTransactional;
import com.dxx.game.common.channel.habby.HabbyStoreService;
import com.dxx.game.common.redis.RedisLock;
import com.dxx.game.config.GameConfigManager;
import com.dxx.game.config.object.HabbyStoreConfig;
import com.dxx.game.dao.dynamodb.repository.*;
import com.dxx.game.dao.dynamodb.repository.gameplay.UserChapterDao;
import com.dxx.game.modules.item.service.ItemService;
import com.dxx.game.modules.log.service.LogService;
import io.netty.handler.codec.http.FullHttpRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2024/12/10
 */
@Slf4j
@Service
public class HabbyStoreServiceImpl implements HabbyStoreService {

    @Resource
    private GameConfigManager gameConfigManager;
    @Resource
    private AccountDao accountDao;
    @Resource
    private UserDao userDao;
    @Resource
    private ShopDao shopDao;
    @Resource
    private UserChapterDao userChapterDao;
    @Resource
    private RedisLock redisLock;
    @Resource
    private LogGmRewardDao logGmRewardDao;
    @Resource
    private ItemDao itemDao;
    @Resource
    private ItemService itemService;
    @Resource
    private LogService logService;

    /**
     * 查询账号
     */
    @Override
    public Map<String, Object> account(FullHttpRequest request) {
/*        try {
            Map<String, String> getParams = HttpRequester.getParams(request);
            if (!getParams.containsKey("gameAccountId") || !getParams.containsKey("sign")) {//游戏账号Id和签名必须存在
                log.error("habbyStoreAccountVerifyError gameAccountId is null, params:{}", getParams);
                return this.error(-1, "params error");
            }
            String sign = getParams.remove("sign");
            boolean flag = this.verifySign(getParams, this.getHabbyStoreSecretKey(), sign);
            if (!flag) {
                log.error("habbyStoreAccountVerifySignFailed, params:{}", getParams);
                return this.error(-1, "verify sign failed");
            }

            String accountKey = getParams.get("gameAccountId");
            Account account = accountDao.getItem(accountKey);
            if (account == null) {
                return this.error(-1, "user not exist");
            }
            List<Long> userIdList = new ArrayList<>(account.getServerUserIdMap().values());
            Map<Long, User> userMap = userDao.getByUserIds(userIdList);//获取玩家所有服的角色
            Map<Long, Shop> shopMap = shopDao.getByUserIds(userIdList);
            Map<Long, UserChapter> userChapterMap = userChapterDao.getByUserIds(userIdList);

            Map<String, Object> result = new HashMap<>();
            result.put("region", "");
            result.put("gameAccountId", accountKey);

            for (Map.Entry<Long, User> entry : userMap.entrySet()) {
                long userId = entry.getValue().getUserId();
                User user = entry.getValue();
                int chapterId = 1;
                UserChapter userChapter = userChapterMap.get(userId);
                if(userChapter!=null){
                    chapterId = userChapter.getChapterId();
                }

                String lastOrderCurrency = "";
                if (shopMap.containsKey(userId)) {
                    //todo
                    //lastOrderCurrency = shopMap.get(userId).getLastOrderCurrency();
                }

                Map<String, Object> userInfo = new HashMap<>();
                userInfo.put("gameUserId", userId);
                userInfo.put("nickName", Optional.ofNullable(user.getNickName()).orElse(String.valueOf(userId)));
                userInfo.put("avatar", String.valueOf(Optional.ofNullable(user.getAvatar()).orElse(1)));
                userInfo.put("level", user.getLevel());
                userInfo.put("lastLoginTime", user.getLoginTimestamp());
                userInfo.put("chapterInfo", String.valueOf(chapterId));
                userInfo.put("currency", Optional.ofNullable(lastOrderCurrency).orElse(""));

                Map<String, Object> userServerList = new HashMap<>();
                userServerList.put("serverId", user.getServerId());
                userServerList.put("userList", userInfo);
                result.put("userServerList", userServerList);
            }
            return this.success(result);
        } catch (Exception e) {
            log.error("habbyStoreAccount error:", e);
            return this.error(-1, e.getMessage());
        }*/
        return this.error(-1, "没开通");
    }

    /**
     * 扣除道具
     */
    @Override
    @DynamoDBTransactional
    public Map<String, Object> deductItem(JSONObject params) {
  /*      try {
            log.info("habbyStoreDeductItem, params:{}", params);

            Map<String, String> postParams = new HashMap<>();
            postParams.put("gameUserId", params.getString("gameUserId"));
            postParams.put("itemId", params.getString("itemId"));
            postParams.put("itemCount", String.valueOf(params.getIntValue("itemCount")));
            postParams.put("transId", params.getString("transId"));
            postParams.put("timestamp", String.valueOf(params.getLongValue("timestamp")));
            String sign = params.getString("sign");

            boolean flag = this.verifySign(postParams, this.getHabbyStoreSecretKey(), sign);
            if (!flag) {
                log.error("habbyStoreDeductItemVerifySignFailed, params:{}", params);
                return this.error(-1, "verify sign failed");
            }

            long userId = params.getLongValue("gameUserId");
            int itemId = params.getIntValue("itemId");
            int itemCount = params.getIntValue("itemCount");
            String transId = params.getString("transId");

            User user = userDao.getItemWithoutCache(userId);
            if (user == null) {
                return this.error(-1, "user not exist");
            }
            RequestContext.setUserId(userId);
            CommonHelper.buildCommonParams(user);
            // 加锁
            if (!redisLock.lock()) {
                return this.error(-1, "get lock failed");
            }

            String uniqueId = "habby_store_" + transId;
            LogGmReward logGmReward = logGmRewardDao.getByUserIdAndUniqueId(userId, uniqueId);
            if (logGmReward != null) {
                log.error("habbyStoreStoreDeductItem transId exist, userId:{}, itemId:{}, itemCount:{}, transId:{}",
                        userId, itemId, itemCount, transId);
                return this.success();
            }
            RequestContext.setCommand((short)4);
            int costCount = Math.abs(itemCount);
            Item item = itemDao.getByItemId(userId, itemId);
            if (item == null) {
                item = itemService.createItem(userId, itemId, 0, 0, 0);
            }
            item.setCount(item.getCount() - costCount);
            itemDao.update(item);

            logService.sendItem(userId, RequestContext.getCommand(), itemId, -costCount, item.getCount(), "from_transId:" + transId);

            logGmReward = new LogGmReward();
            logGmReward.setUserId(userId);
            logGmReward.setUniqueId(uniqueId);
            logGmReward.setTimestamp(DateUtils.getUnixTime());
            logGmRewardDao.insert(logGmReward);

            return this.success();
        } catch (Exception e) {
            log.error("habbyStoreDeductItem error:", e);
            return this.error(-1, e.getMessage());
        }*/
        return this.error(-1,"没开通");
    }

    /**
     * 验证签名是否一致
     */
    private boolean verifySign(Map<String, String> params, String secretKey, String sign) {
        String signature = genSign(params, secretKey);
        if (StringUtils.isEmpty(signature)) {
            return false;
        }
        return signature.equals(sign);
    }

    /**
     * 根据字典排序后，使用密匙加密获得签名
     */
    private String genSign(Map<String, String> params, String secretKey) {
        List<String> keys = new ArrayList<>(params.keySet());
        Collections.sort(keys);

        StringBuilder sb = new StringBuilder();
        for (String key : keys) {
            String value = params.get(key);
            if (sb.length() > 0) {
                sb.append("&");
            }
            sb.append(key).append("=").append(value);
        }

        try {
            Mac hmacSha1 = Mac.getInstance("HmacSHA1");
            SecretKeySpec secretKeySpec = new SecretKeySpec(secretKey.getBytes(), "HmacSHA1");
            hmacSha1.init(secretKeySpec);
            byte[] hash = hmacSha1.doFinal(sb.toString().getBytes());
            return java.util.Base64.getEncoder().encodeToString(hash);
        } catch (Exception e) {
            log.error("habbyId unBind genSign error:", e);
            return null;
        }
    }

    /**
     * 获取验证密匙
     */
    private String getHabbyStoreSecretKey() {
        HabbyStoreConfig habbyStoreConfig = gameConfigManager.getMainConfig().getHabbyStoreConfig();
        if (gameConfigManager.isTest() || gameConfigManager.isDevelop()) {
            return habbyStoreConfig.getSecretTest();
        } else {
            return habbyStoreConfig.getSecretProd();
        }
    }

    private Map<String, Object> success(Object data) {
        Map<String, Object> result = new HashMap<>();
        result.put("code", 0);
        result.put("message", "success");
        result.put("data", data);
        return result;
    }

    private Map<String, Object> success() {
        Map<String, Object> result = new HashMap<>();
        result.put("code", 0);
        result.put("message", "success");
        return result;
    }

    private Map<String, Object> error(int code, String msg) {
        Map<String, Object> result = new HashMap<>();
        result.put("code", code);
        result.put("message", msg);
        result.put("data", null);
        return result;
    }

}
