package com.dxx.game.common.utils;

import com.dxx.game.common.id.NanoId;

/**
 * <AUTHOR>
 * @date 2023/9/1 11:51
 */
public class IdUtils {
    /**
     * 生成nanoId
     */
    public static String nanoId() {
        return NanoId.randomNanoId(null, "abcdefghijklmnopqrstuvwxyz".toCharArray(), 11);
    }

    public static String nanoIdWithNum() {
        return NanoId.randomNanoId(null, "abcdefghijklmnopqrstuvwxyz1234567890".toCharArray(), 18);
    }
}
