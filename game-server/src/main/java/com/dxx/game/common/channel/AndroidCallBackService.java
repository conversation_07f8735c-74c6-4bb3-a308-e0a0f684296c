package com.dxx.game.common.channel;

import com.dxx.game.common.channel.common.model.PayCbVo;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/12/27 9:59
 */
public interface AndroidCallBackService {

    /**
     * 发货
     * @param payCbVo
     * @return
     */
    int doDeliverGoods(PayCbVo payCbVo);

    /**
     * 注销账号
     * @param accountId
     * @param extra
     * @return
     */
    int cancelAccount(String accountId, String extra);

    /**
     * 获取商品价格
     */
    int getIapAmount(String productId);

    Map<String, Object> queryOrder(long userId, Long preOrderId);
}
