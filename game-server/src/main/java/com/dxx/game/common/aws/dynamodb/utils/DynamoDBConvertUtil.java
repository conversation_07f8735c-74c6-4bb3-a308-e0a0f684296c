package com.dxx.game.common.aws.dynamodb.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.springframework.util.CollectionUtils;
import software.amazon.awssdk.services.dynamodb.model.AttributeValue;

import java.math.BigDecimal;
import java.util.*;

/**
 * @authoer: lsc
 * @createDate: 2022/10/29
 * @description:
 */
public class DynamoDBConvertUtil {


    public static AttributeValue buildAttributeValue(Object value) {
        AttributeValue.Builder attributeValueBuilder = AttributeValue.builder();
        if (value instanceof Long) {
            attributeValueBuilder.n(value.toString());
        } else if (value instanceof Integer) {
            attributeValueBuilder.n(value.toString());
        } else if (value instanceof String) {
            attributeValueBuilder.s(value.toString());
        } else if (value instanceof Boolean) {
            attributeValueBuilder.bool(Boolean.valueOf(value.toString()));
        } else if (value instanceof Short) {
            attributeValueBuilder.n(value.toString());
        } else {
            attributeValueBuilder.m(getAttributeMap(value));
        }
        return attributeValueBuilder.build();
    }

    /**
     * AttributeValue 转成 JSONObject
     */
    public static JSONObject getJSONObject(AttributeValue input) {
        JSONObject jsonObject = new JSONObject();
        Map<String, AttributeValue> m = input.m();
        m.forEach((key, value) -> {
            if (value.s() != null) {
                jsonObject.put(key, value.s());
            } else {
                jsonObject.put(key, value.n());
            }
        });
        return jsonObject;
    }

    /**
     * 　解析复杂对象，可以嵌套对象，也可以嵌套集合
     * 如果要新增类型，需要在这里添加
     */
    public static JSONObject getJSONObjectForComplexObject(AttributeValue input) {
        JSONObject jsonObject = new JSONObject();
        Map<String, AttributeValue> m = input.m();
        m.forEach((key, value) -> {
            if (value.s() != null) {
                jsonObject.put(key, value.s());
            } else if (value.n() != null) {
                jsonObject.put(key, value.n());
            } else if (value.bool() != null) {
                jsonObject.put(key, value.bool());
            } else if (value.l() != null && !CollectionUtils.isEmpty(value.l())) {
                jsonObject.put(key, getListForComplexObject(value.l()));
            } else if (value.m() != null && !CollectionUtils.isEmpty(value.m())) {
                jsonObject.put(key, getMapForComplexObject(value.m()));
            }
        });
        return jsonObject;
    }

    /**
     * 　解析List<AttributeValue>
     */
    private static Collection<Object> getListForComplexObject(Collection<AttributeValue> list) {
        List<Object> result = new ArrayList<>();
        list.forEach(v -> {
            if (v.s() != null) {
                result.add(v.s());
            } else if (v.bool() != null) {
                result.add(v.bool());
            } else if (v.n() != null) {
                result.add(v.n());
            } else if (v.m() != null) {
                result.add(getMapForComplexObject(v.m()));
            } else if (v.l() != null) {
                result.add(getListForComplexObject(v.l()));
            }
        });
        return result;
    }

    /**
     * 　解析Map<String, AttributeValue>
     */
    private static HashMap<String, Object> getMapForComplexObject(Map<String, AttributeValue> map) {
        HashMap<String, Object> result = new HashMap<>(10);
        map.forEach((k, v) -> {
            if (v.s() != null) {
                result.put(k, v.s());
            } else if (v.bool() != null) {
                result.put(k, v.bool());
            } else if (v.n() != null) {
                result.put(k, v.n());
            } else if (v.m() != null && v.m().size() != 0) {
                result.put(k, getMapForComplexObject(v.m()));
            } else if (v.l() != null && v.l().size() != 0) {
                result.put(k, getListForComplexObject(v.l()));
            }
        });
        return result;
    }


    /**
     * Object to Map<String, AttributeValue>
     */
    public static Map<String, AttributeValue> getAttributeMap(Object input) {
        Map<String, AttributeValue> map = new HashMap<>(10);
        JSONObject jsonObject = JSONObject.parseObject(JSON.toJSONString(input));
        jsonObject.keySet().forEach(key -> {
            Object value = jsonObject.get(key);
            if (value instanceof Long || value instanceof Integer || value instanceof BigDecimal) {
                map.put(key, AttributeValue.builder().n(value.toString()).build());
            } else {
                map.put(key, AttributeValue.builder().s(jsonObject.getString(key)).build());
            }
        });
        return map;
    }

    public static Map<String, AttributeValue> getAttributeMapForComplexObject(Object input) {
        Map<String, AttributeValue> map = new HashMap<>(10);
        JSONObject jsonObject = JSONObject.parseObject(JSON.toJSONString(input));
        jsonObject.keySet().forEach(key -> {
            Object value = jsonObject.get(key);
            if (value instanceof Long || value instanceof Integer || value instanceof BigDecimal) {
                map.put(key, AttributeValue.builder().n(value.toString()).build());
            } else if (value instanceof String) {
                map.put(key, AttributeValue.builder().s(value.toString()).build());
            } else if (value instanceof Boolean) {
                map.put(key, AttributeValue.builder().bool((Boolean) value).build());
            } else if (value instanceof Collection) {
                //Collection<Object>
                map.put(key, AttributeValue.builder().l(getAttributeListForListObject((Collection) value)).build());
            } else {
                //自定义对象
                map.put(key, AttributeValue.builder().m(getAttributeMapForComplexObject(value)).build());
            }
        });
        return map;
    }

    public static Collection<AttributeValue> getAttributeListForListObject(Collection input) {
        Collection<AttributeValue> list = new ArrayList<>();
        input.forEach(item -> {
            Map<String, AttributeValue> attributeMap = getAttributeMapForComplexObject(item);
            list.add(AttributeValue.builder().m(attributeMap).build());
        });
        return list;
    }
}
