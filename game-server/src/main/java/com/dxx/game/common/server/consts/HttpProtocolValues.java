package com.dxx.game.common.server.consts;

public class HttpProtocolValues {
    public static short  REQ_OBJEC_STREAM_SEEK_START = 4;
    public static int    REQ_MAX_OBJ_LENGTH = (32*1024);	// 32KB
    public static String HTTP_CONTENT_TYPE_JSON	= "Content-type: application/json";

    public static String DXX_TIMESTAMP_HEADER	= "DxxTime";
    public static String DXX_CHECKSUM_HEADER = "DxxCheck";
    public static String DXX_VERSION_HEADER = "DxxVersion";
    public static String GAME_PLATFORM = "GAME_PLATFORM";
}
