package com.dxx.game.common.config.game.converter;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;


/**
 * <AUTHOR>
 *
 */
@Component
public class MatrixIntegerConverter implements FieldConverter {
	
	@Override
	public List<List<Integer>> convert (Field field, Object val) throws Exception {
		List<List<String>> list = JSON.parseObject(val.toString(), new TypeReference<List<List<String>>>(){});
		List<List<Integer>> result = new ArrayList<>();
		for (List<String> value : list) {
			List<Integer> newValue = new ArrayList<>();
			for (String s : value) {
				newValue.add(Integer.parseInt(s));
			}
			result.add(Collections.unmodifiableList(newValue));
		}
		return Collections.unmodifiableList(result);
	}
}
