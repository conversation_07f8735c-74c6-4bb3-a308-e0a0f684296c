package com.dxx.game.common.channel.apple.config;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/5/9 8:48 下午
 */
@Data
public class AppleConfig {

    private String packageName;
    private Pay pay;
    private Account account;
    private Order order;

    @Data
    public static class Pay {
        private List<String> verifyReceiptURLs;
    }

    @Data
    public static class Account {
        private boolean verify;
        private String bundleId;
        private int signatureTimeoutInSecond;
    }

    @Data
    public static class Order {
        private String kid;
        private String iss;
        private String aud;
        private String privateKey;
    }

}
