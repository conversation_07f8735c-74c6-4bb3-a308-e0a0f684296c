package com.dxx.game.common.server.context;


import com.dxx.game.dto.CommonProto;
import io.netty.util.concurrent.FastThreadLocal;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 响应上下文
 * <AUTHOR>
 * @date 2019-12-12 11:52
 */
public class ResponseContext {


    /** 升级奖励结果集 */
    private static FastThreadLocal<Object> levelUpRewardResultSet = new FastThreadLocal<Object>();
    /** 任务红点 */
    private static FastThreadLocal<Boolean> taskRedPoint = new FastThreadLocal<Boolean>();
    /** 任务领取奖励时触发其他完成任务列表 */
    private static FastThreadLocal<Map<Integer, List<Integer>>> taskDoneInfo = new FastThreadLocal<>();
    /** 更新任务之后的任务数据 */
    private static FastThreadLocal<Object> taskData = new FastThreadLocal<>();
    /** 开服嘉年华任务红点 */
    private static FastThreadLocal<Boolean> openServiceCarnivalTaskRedPoint = new FastThreadLocal<>();
    /** 开服嘉年华任务领取奖励/领取活跃度奖励触发其他任务进度 */
    private static FastThreadLocal<List<Integer>> openServiceCarnivalTaskData = new FastThreadLocal<>();
    private static FastThreadLocal<Long> frozeTime = new FastThreadLocal<>();
    /** 公会任务 */
    private static FastThreadLocal<Boolean> guildTaskRedPoint = new FastThreadLocal<>();
    private static FastThreadLocal<List<Integer>> guildTaskData = new FastThreadLocal<>();
    /** 有更新的任务数据 */
    private static FastThreadLocal<List<Object>> updateTaskList = new FastThreadLocal<>();

    /** 开服嘉年华任务红点 */
    private static FastThreadLocal<List<Object>> sevenDayTaskDto = new FastThreadLocal<>();

    private static FastThreadLocal<List<CommonProto.EventTaskDto>> eventTaskDto = new FastThreadLocal<>();

    private static FastThreadLocal<List<Integer>> relicIds = new FastThreadLocal<>();

    public static void setLevelUpRewardResultSet(Object resultSet) {
        levelUpRewardResultSet.set(resultSet);
    }

    public static Object getLevelUpRewardResultSet() {
        return levelUpRewardResultSet.get();
    }

    public static void setTaskRedPoint(boolean value) {
        taskRedPoint.set(value);
    }

    public static Boolean getTaskRedPoint() {
        return taskRedPoint.get();
    }

    public static void setTaskDoneInfo(int taskType, int taskId) {
        if (taskDoneInfo.get() == null) {
            taskDoneInfo.set(new HashMap<>());
        }
        if (taskDoneInfo.get().containsKey(taskType)) {
            taskDoneInfo.get().get(taskType).add(taskId);
        } else {
            List<Integer> taskIds = new ArrayList<>(1);
            taskIds.add(taskId);
            taskDoneInfo.get().put(taskType, taskIds);
        }
    }
    public static List<Object> getUpdateTasks() {
        return updateTaskList.get();
    }
    public static void setTaskData(Object obj) {
        taskData.set(obj);
    }

    public static Object getTaskData() {
        return taskData.get();
    }


    public static Map<Integer, List<Integer>> getTaskDoneInfo() {
        return taskDoneInfo.get();
    }

    public static void setOpenServiceCarnivalTaskRedPoint(boolean value) {
        openServiceCarnivalTaskRedPoint.set(value);
    }

    public static Boolean getOpenServiceCarnivalTaskRedPoint() {
        return openServiceCarnivalTaskRedPoint.get();
    }

    public static void addOpenServiceCarnivalTaskIds(List<Integer> taskIds) {
        if (openServiceCarnivalTaskData.get() == null) {
            openServiceCarnivalTaskData.set(new ArrayList<>());
        }
        openServiceCarnivalTaskData.get().addAll(taskIds);
    }

    public static List<Integer> getOpenServiceCarnivalTaskIds() {
        return openServiceCarnivalTaskData.get();
    }

    public static void setFrozeTime(long time) {
        frozeTime.set(time);
    }

    public static long getFrozeTime() {
        return frozeTime.get();
    }

    public static void setGuildTaskIds(List<Integer> taskIds) {
        if (guildTaskData.get() == null) {
            guildTaskData.set(new ArrayList<>());
        }
        guildTaskData.get().addAll(taskIds);
    }

    public static List<Integer> getGuildTaskIds() {
        return guildTaskData.get();
    }

    public static void setGuildTaskRedPointRedPoint(boolean value) {
        guildTaskRedPoint.set(value);
    }

    public static Boolean getGuildTaskRedPointRedPoint() {
        return guildTaskRedPoint.get();
    }
    public static void addUpdateTask(Object obj) {
        if (updateTaskList.get() == null) {
            updateTaskList.set(new ArrayList<>());
        }
        updateTaskList.get().add(obj);
    }
    public static void addSevenDayTask(List<Object> taskList) {
        if (sevenDayTaskDto.get() == null) {
            sevenDayTaskDto.set(new ArrayList<>());
        }
        sevenDayTaskDto.get().addAll(taskList);
    }

    public static List<Object> getSevenDayTaskDto() {
        return sevenDayTaskDto.get();
    }

    public static void addEventTask(CommonProto.EventTaskDto task) {
        if (eventTaskDto.get() == null) {
            eventTaskDto.set(new ArrayList<>());
        }
        eventTaskDto.get().add(task);
    }

    public static List<CommonProto.EventTaskDto> getEventTaskDto() {
        return eventTaskDto.get();
    }

    public static void addRelicId(int value) {
        if (relicIds.get() == null) {
            relicIds.set(new ArrayList<>());
        }
        relicIds.get().add(value);
    }

    public static List<Integer> getRelicIds() {
        return relicIds.get();
    }

    public static void clear() {
        taskRedPoint.remove();
        levelUpRewardResultSet.remove();
        taskDoneInfo.remove();
        taskData.remove();
        openServiceCarnivalTaskRedPoint.remove();
        openServiceCarnivalTaskData.remove();
        frozeTime.remove();
        guildTaskRedPoint.remove();
        guildTaskData.remove();
        updateTaskList.remove();
        sevenDayTaskDto.remove();
        eventTaskDto.remove();
        relicIds.remove();
    }
}
