package com.dxx.game.consts;

/**
 * redis key
 * <AUTHOR>
 * @date 2019-12-17 18:36
 */
public class RedisKeys {
	public static final String LOCK_CROSS_ARENA_SEASON_END = "cross_arena_season_end";
	public static final String LOCK_GUILD_RACE_SC = "guild_race_sc";
	public static final String LOCK_MQ_TEST = "test_mq_lock";
	public static final String LOCK_TICKER_MIN = "ticker_min";

	/** UserID **/
	public static final String USER_ID_KEY = "user_id_key";

	/** 用户操作ID */
	public static final String USER_TRANSID = "user_trans_id_";

	/** 全服邮件唯一ID string **/
	public static final String SERVER_MAIL_PRIMARY_KEY = "server_mail_primary_key";

	/** 通用唯一ID前缀 + UserID 表分片 **/
	public static final String GENERAL_INCR_ID = "general_incr_id_";

	/** 服务器日志唯一ID **/
	public static final String LOG_GAME_SERVER_PRIMARY_KEY = "log_game_server_primary_key";

	/** 用户信息 **/
	public static final String USER_INFO = "user_info:";

	/** 用户全量信息(包含阵容) **/
	public static final String USER_FULL_INFO = "user_full_info:";

	/** ip to country zset */
	public static final String IPV4_COUNTRY = "ipv4_country";
	public static final String IPV6_COUNTRY = "ipv6_country";

	/** 战报 **/
	public static final String REPORT_PRIMARY_KEY = "report_primary_key";

	/** 战力排行榜 */
	public static final String RANK_POWER_KEY= "r:power";

	/** 工会战 赛季 */
	public static final String GUILD_RACE_SEASON= "gr:s:";
	/** 工会战 阶段 */
	public static final String GUILD_RACE_STAGE= "gr:t:";

	/** 公会战 BASE */
	public static final String GUILD_RACE_BASE= "gr:";
	/** 工会战 工会报名 */
	public static final String GUILD_RACE_APPLY_GUILD= ":ag";
	/** 工会战 玩家报名 */
	public static final String GUILD_RACE_APPLY_USER= ":au";
	/** 工会战 玩家报名列表*/
	public static final String GUILD_RACE_GUILD_APPLY_USER= ":gau";
	/** 工会战 玩家报名顺序 */
	public static final String GUILD_RACE_APPLY_USER_SORT= ":aus";
	/** 工会战 公会信息 */
	public static final String GUILD_RACE_GUILD_INFO= ":gi";
	/** 工会战 出战位 */
	public static final String GUILD_RACE_BATTLE_LIST= ":bl";
	/** 公会战 分组列表 */
	public static final String GUILD_RACE_GROUP_LIST= ":gl";
	/** 公会战 战报 */
	public static final String GUILD_RACE_RECORD= ":rd";
	/** 公会战 排行 */
	public static final String GUILD_RACE_RANK= ":rk";
	/** 公会战 段位 */
	public static final String GUILD_RACE_DAN= "dan";
	/** 公会战 段位 */
	public static final String GUILD_POWER_KEY= "guild-power-";
	/** 工会战 阶段标志*/
	public static final String GUILD_RACE_STAGE_FLAG_KEY= "sf:";
	/** 工会战 组索引 */
	public static final String GUILD_RACE_GROUP_INDEX= ":gri";

	public static final String CROSS_ARENA_BASE = "ca:";
	/** 竞技场 赛季 */
	public static final String CROSS_ARENA_SEASON= CROSS_ARENA_BASE + "s";
	/** 竞技场 分组 */
	public static final String CROSS_ARENA_GROUP= ":g";
	/** 竞技场 分组索引 */
	public static final String CROSS_ARENA_GROUP_INDEX= ":gi";
	/** 竞技场 记录 */
	public static final String CROSS_ARENA_RECORD= ":rd";
	/** 竞技场 分组 */
	public static final String CROSS_ARENA_ROBOT_GEN= ":rge";

	/** 玩家战斗数据 */
	public static final String USER_BATTLE_DATA = "ubdv2:";


	/** 公会boss */
	public static final String GUILD_BOSS_KEY = "gb:";

	/** 矿点 */
	public static final String ORE_KEY = "ore:";

	/** 聊天 */
	public static final String CHAT_FAST_SCENE_KEY= "chat:fast:";

	/** 抓人推荐列表刷新频率 */
	public static final String CONQUER_FAST_KEY= "cq:fast:";

	/** 会长转让频率 */
	public static final String GUILD_TRAN_FAST_KEY= "gd:ts:";


	public static final String SERVER_LIST_ID = "SERVER_LIST_ID_%d";
	public static final String SERVER_LIST_OPEN_TIME = "SERVER_LIST_OPEN_TIME_%d";
	public static final String SERVER_LIST_COUNT = "SERVER_LIST_COUNT_%d";
	public static final String SERVER_LIST_STATUS_KEY = "SERVER_LIST_STATUS_%d";

	/** ab test */
	public static final String AB = "ab:";

	/** 远征 */
	public static final String EXPEDITION_MATCH_KEY = "ExpeditionMatch:%s:%d";

	public static final String LOCK_ORDER_KEY = "lockOrder:%d:%d";
}
