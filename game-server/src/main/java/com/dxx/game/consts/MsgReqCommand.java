package com.dxx.game.consts;

public class MsgReqCommand {

	public static final short DevelopLoginRequest = 9001;
	public static final short DevelopLoginResponse = 9002;
	public static final short DevelopChangeResourceRequest = 9003;
	public static final short DevelopChangeResourceResponse = 9004;
	public static final short DevelopToolsRequest = 9005;
	public static final short DevelopToolsResponse = 9006;
	public static final short ErrorMsg = 9999;
	public static final short UserLoginRequest = 10101;
	public static final short UserLoginResponse = 10102;
	public static final short UserGetInfoRequest = 10103;
	public static final short UserGetInfoResponse = 10104;
	public static final short UserHeartbeatRequest = 10105;
	public static final short UserHeartbeatResponse = 10106;
	public static final short UserUpdateSystemMaskRequest = 10107;
	public static final short UserUpdateSystemMaskResponse = 10108;
	public static final short UserUpdateGuideMaskRequest = 10109;
	public static final short UserUpdateGuideMaskResponse = 10110;
	public static final short UserCancelAccountRequest = 10111;
	public static final short UserCancelAccountResponse = 10112;
	public static final short UserUpdateInfoRequest = 10113;
	public static final short UserUpdateInfoResponse = 10114;
	public static final short UserGetOtherPlayerInfoRequest = 10117;
	public static final short UserGetOtherPlayerInfoResponse = 10118;
	public static final short UserGetCityInfoRequest = 10119;
	public static final short UserGetCityInfoResponse = 10120;
	public static final short UserHeartbeatSyncRequest = 10121;
	public static final short UserHeartbeatSyncResponse = 10122;
	public static final short UserGetBattleReportRequest = 10123;
	public static final short UserGetBattleReportResponse = 10124;
	public static final short UserOpenModelRequest = 10125;
	public static final short UserOpenModelResponse = 10126;
	public static final short UserSetFormationByTypeRequest = 10127;
	public static final short UserSetFormationByTypeResponse = 10128;
	public static final short UserGetFormationByTypeRequest = 10129;
	public static final short UserGetFormationByTypeResponse = 10130;
	public static final short UserTriggerRequest = 10131;
	public static final short UserTriggerResponse = 10132;
	public static final short UserTicketInfoRequest = 10133;
	public static final short UserTicketInfoResponse = 10134;
	public static final short UserExchangeItemRequest = 10135;
	public static final short UserExchangeItemResponse = 10136;
	public static final short UserSyncPowerRequest = 10137;
	public static final short UserSyncPowerResponse = 10138;
	public static final short UserHabbyMailBindRequest = 10139;
	public static final short UserHabbyMailBindResponse = 10140;
	public static final short UserHabbyMailRewardRequest = 10141;
	public static final short UserHabbyMailRewardResponse = 10142;
	public static final short ItemUseRequest = 10201;
	public static final short ItemUseResponse = 10202;
	public static final short MissionGetInfoRequest = 10401;
	public static final short MissionGetInfoResponse = 10402;
	public static final short MissionStartRequest = 10403;
	public static final short MissionStartResponse = 10404;
	public static final short MissionCompleteRequest = 10405;
	public static final short MissionCompleteResponse = 10406;
	public static final short MissionGetHangUpItemsRequest = 10407;
	public static final short MissionGetHangUpItemsResponse = 10408;
	public static final short MissionReceiveHangUpItemsRequest = 10409;
	public static final short MissionReceiveHangUpItemsResponse = 10410;
	public static final short MissionQuickHangUpRequest = 10411;
	public static final short MissionQuickHangUpResponse = 10412;
	public static final short TaskGetInfoRequest = 10501;
	public static final short TaskGetInfoResponse = 10502;
	public static final short TaskRewardDailyRequest = 10503;
	public static final short TaskRewardDailyResponse = 10504;
	public static final short TaskRewardAchieveRequest = 10505;
	public static final short TaskRewardAchieveResponse = 10506;
	public static final short TaskActiveRewardRequest = 10507;
	public static final short TaskActiveRewardResponse = 10508;
	public static final short TaskActiveRewardAllRequest = 10509;
	public static final short TaskActiveRewardAllResponse = 10510;
	public static final short PayInAppPurchaseRequest = 10701;
	public static final short PayInAppPurchaseResponse = 10702;
	public static final short PayPreOrderRequest = 10703;
	public static final short PayPreOrderResponse = 10704;
	public static final short MonthCardGetRewardRequest = 10705;
	public static final short MonthCardGetRewardResponse = 10706;
	public static final short BattlePassRewardRequest = 10707;
	public static final short BattlePassRewardResponse = 10708;
	public static final short BattlePassChangeScoreRequest = 10709;
	public static final short BattlePassChangeScoreResponse = 10710;
	public static final short BattlePassFinalRewardRequest = 10711;
	public static final short BattlePassFinalRewardResponse = 10712;
	public static final short VIPLevelRewardRequest = 10713;
	public static final short VIPLevelRewardResponse = 10714;
	public static final short LevelFundRewardRequest = 10715;
	public static final short LevelFundRewardResponse = 10716;
	public static final short FirstRechargeRewardRequest = 10717;
	public static final short FirstRechargeRewardResponse = 10718;
	public static final short FirstRechargeRewardV1Request = 10719;
	public static final short FirstRechargeRewardV1Response = 10720;
	public static final short MailGetListRequest = 10801;
	public static final short MailGetListResponse = 10802;
	public static final short MailReceiveAwardsRequest = 10803;
	public static final short MailReceiveAwardsResponse = 10804;
	public static final short MailDeleteRequest = 10805;
	public static final short MailDeleteResponse = 10806;
	public static final short HeroUpgradeRequest = 10901;
	public static final short HeroUpgradeResponse = 10902;
	public static final short HeroAdvanceRequest = 10903;
	public static final short HeroAdvanceResponse = 10904;
	public static final short HeroStarRequest = 10905;
	public static final short HeroStarResponse = 10906;
	public static final short HeroResetRequest = 10907;
	public static final short HeroResetResponse = 10908;
	public static final short HeroBookScoreRequest = 10909;
	public static final short HeroBookScoreResponse = 10910;
	public static final short HeroBookRewardRequest = 10911;
	public static final short HeroBookRewardResponse = 10912;
	public static final short HeroReplaceSkinRequest = 10913;
	public static final short HeroReplaceSkinResponse = 10914;
	public static final short HeroBondLevelUpRequest = 10915;
	public static final short HeroBondLevelUpResponse = 10916;
	public static final short HeroLosslessRequest = 10917;
	public static final short HeroLosslessResponse = 10918;
	public static final short ChapterStartRequest = 11011;
	public static final short ChapterStartResponse = 11012;
	public static final short ChapterSaveRequest = 11013;
	public static final short ChapterSaveResponse = 11014;
	public static final short ChapterGetInfoRequest = 11015;
	public static final short ChapterGetInfoResponse = 11016;
	public static final short ChapterFinishRequest = 11017;
	public static final short ChapterFinishResponse = 11018;
	public static final short EliteChapterGetChestReward = 11019;
	public static final short EliteChapterGetChestRewardResponse = 11020;
	public static final short EquipStrengthRequest = 11101;
	public static final short EquipStrengthResponse = 11102;
	public static final short EquipComposeRequest = 11103;
	public static final short EquipComposeResponse = 11104;
	public static final short EquipUpgradeRequest = 11105;
	public static final short EquipUpgradeResponse = 11106;
	public static final short EquipDressRequest = 11107;
	public static final short EquipDressResponse = 11108;
	public static final short EquipLevelResetRequest = 11109;
	public static final short EquipLevelResetResponse = 11110;
	public static final short EquipQualityDownRequest = 11111;
	public static final short EquipQualityDownResponse = 11112;
	public static final short EquipOffRequest = 11113;
	public static final short EquipOffResponse = 11114;
	public static final short EquipReplaceRequest = 11115;
	public static final short EquipReplaceResponse = 11116;
	public static final short ShopGetInfoRequest = 11201;
	public static final short ShopGetInfoResponse = 11202;
	public static final short ShopBuyItemRequest = 11203;
	public static final short ShopBuyItemResponse = 11204;
	public static final short ShopBuyIAPItemRequest = 11205;
	public static final short ShopBuyIAPItemResponse = 11206;
	public static final short ShopDoGachaRequest = 11207;
	public static final short ShopDoGachaResponse = 11208;
	public static final short ShopIntegralGetInfoRequest = 11209;
	public static final short ShopIntegralGetInfoResponse = 11210;
	public static final short ShopIntegralRefreshItemRequest = 11211;
	public static final short ShopIntegralRefreshItemResponse = 11212;
	public static final short ShopIntegralBuyItemRequest = 11213;
	public static final short ShopIntegralBuyItemResponse = 11214;
	public static final short ShopGacheWishRequest = 11215;
	public static final short ShopGacheWishResponse = 11216;
	public static final short ShopBuyTicketsRequest = 11217;
	public static final short ShopBuyTicketsResponse = 11218;
	public static final short ShopFreeIAPItemRequest = 11219;
	public static final short ShopFreeIAPItemResponse = 11220;
	public static final short ShopGetGachaInfoRequest = 11221;
	public static final short ShopGetGachaInfoResponse = 11222;
	public static final short ActivityGetListRequest = 11401;
	public static final short ActivityGetListResponse = 11402;
	public static final short SignInGetInfoRequest = 11501;
	public static final short SignInGetInfoResponse = 11502;
	public static final short SignInDoSignRequest = 11503;
	public static final short SignInDoSignResponse = 11504;
	public static final short SevenDayTaskGetInfoRequest = 11551;
	public static final short SevenDayTaskGetInfoResponse = 11552;
	public static final short SevenDayTaskRewardRequest = 11553;
	public static final short SevenDayTaskRewardResponse = 11554;
	public static final short SevenDayTaskActiveRewardRequest = 11555;
	public static final short SevenDayTaskActiveRewardResponse = 11556;
	public static final short UserGetLastLoginRequest = 11621;
	public static final short UserGetLastLoginResponse = 11622;
	public static final short FindServerListRequest = 11623;
	public static final short FindServerListResponse = 11624;
	public static final short SystemEquipUpgradeRequest = 11701;
	public static final short SystemEquipUpgradeResponse = 11702;
	public static final short SystemEquipResetRequest = 11703;
	public static final short SystemEquipResetResponse = 11704;
	public static final short SystemEquipAdvanceRequest = 11705;
	public static final short SystemEquipAdvanceResponse = 11706;
	public static final short SystemEquipOneKeyUpgradeRequest = 11707;
	public static final short SystemEquipOneKeyUpgradeResponse = 11708;
	public static final short StartMazeRequest = 11801;
	public static final short StartMazeResponse = 11802;
	public static final short MazeRoomStartRequest = 11803;
	public static final short MazeRoomStartResponse = 11804;
	public static final short MazeRoomFinishRequest = 11805;
	public static final short MazeRoomFinishResponse = 11806;
	public static final short MazeRoomOpenRequest = 11807;
	public static final short MazeRoomOpenResponse = 11808;
	public static final short QuitMazeRequest = 11809;
	public static final short QuitMazeResponse = 11810;
	public static final short MazeNextLayerRequest = 11811;
	public static final short MazeNextLayerResponse = 11812;
	public static final short MazeSetFormationDataRequest = 11813;
	public static final short MazeSetFormationDataResponse = 11814;
	public static final short MazeGetInfoRequest = 11815;
	public static final short MazeGetInfoResponse = 11816;
	public static final short PersonalBossBattleRequest = 12001;
	public static final short PersonalBossBattleResponse = 12002;
	public static final short PersonalBossRankRequest = 12003;
	public static final short PersonalBossRankResponse = 12004;
	public static final short PersonalBossInfoRequest = 12005;
	public static final short PersonalBossInfoResponse = 12006;
	public static final short RelicActiveRequest = 12301;
	public static final short RelicActiveResponse = 12302;
	public static final short RelicStrengthRequest = 12303;
	public static final short RelicStrengthResponse = 12304;
	public static final short RelicStarRequest = 12305;
	public static final short RelicStarResponse = 12306;
	public static final short TalentSystemUpgradeRequest = 12401;
	public static final short TalentSystemUpgradeResponse = 12402;
	public static final short CityGoldmineLevelUpRequest = 12601;
	public static final short CityGoldmineLevelUpResponse = 12602;
	public static final short CityGoldmineHangRewardRequest = 12603;
	public static final short CityGoldmineHangRewardResponse = 12604;
	public static final short CityGetChestInfoRequest = 12605;
	public static final short CityGetChestInfoResponse = 12606;
	public static final short CityOpenChestRequest = 12607;
	public static final short CityOpenChestResponse = 12608;
	public static final short CityTakeScoreRewardRequest = 12609;
	public static final short CityTakeScoreRewardResponse = 12610;
	public static final short CityGetInfoRequest = 12613;
	public static final short CityGetInfoResponse = 12614;
	public static final short CityTakeTaxRequest = 12615;
	public static final short CityTakeTaxResponse = 12616;
	public static final short CityGoldmineHangRewardShowRequest = 12617;
	public static final short CityGoldmineHangRewardShowResponse = 12618;
	public static final short InteractListRequest = 12701;
	public static final short InteractListResponse = 12702;
	public static final short SocialPowerRankRequest = 12703;
	public static final short SocialPowerRankResponse = 12704;
	public static final short InteractDetailRequest = 12705;
	public static final short InteractDetailResponse = 12706;
	public static final short ConquerListRequest = 12801;
	public static final short ConquerListResponse = 12802;
	public static final short ConquerBattleRequest = 12803;
	public static final short ConquerBattleResponse = 12804;
	public static final short ConquerRevoltRequest = 12805;
	public static final short ConquerRevoltResponse = 12806;
	public static final short ConquerLootRequest = 12807;
	public static final short ConquerLootResponse = 12808;
	public static final short ConquerPardonRequest = 12809;
	public static final short ConquerPardonResponse = 12810;
	public static final short ConquerRecommendRequest = 12811;
	public static final short ConquerRecommendResponse = 12812;
	public static final short ConquerRecommendRefRequest = 12813;
	public static final short ConquerRecommendRefResponse = 12814;
	public static final short IntegralShopBuyRequest = 12901;
	public static final short IntegralShopBuyResponse = 12902;
	public static final short IntegralShopRefreshRequest = 12903;
	public static final short IntegralShopRefreshResponse = 12904;
	public static final short IntegralShopGetInfoRequest = 12905;
	public static final short IntegralShopGetInfoResponse = 12906;
	public static final short TowerChallengeRequest = 13001;
	public static final short TowerChallengeResponse = 13002;
	public static final short TowerRewardRequest = 13003;
	public static final short TowerRewardResponse = 13004;
	public static final short TowerRankRequest = 13005;
	public static final short TowerRankResponse = 13006;
	public static final short TowerRankIndexRequest = 13007;
	public static final short TowerRankIndexResponse = 13008;
	public static final short CrossArenaGetInfoRequest = 13101;
	public static final short CrossArenaGetInfoResponse = 13102;
	public static final short CrossArenaChallengeListRequest = 13103;
	public static final short CrossArenaChallengeListResponse = 13104;
	public static final short CrossArenaChallengeRequest = 13105;
	public static final short CrossArenaChallengeResponse = 13106;
	public static final short CrossArenaRankRequest = 13107;
	public static final short CrossArenaRankResponse = 13108;
	public static final short CrossArenaRecordRequest = 13109;
	public static final short CrossArenaRecordResponse = 13110;
	public static final short CrossArenaEnterRequest = 13111;
	public static final short CrossArenaEnterResponse = 13112;
	public static final short OreGetInfoRequest = 13201;
	public static final short OreGetInfoResponse = 13202;
	public static final short OreHoldRequest = 13203;
	public static final short OreHoldResponse = 13204;
	public static final short OreRewardRequest = 13205;
	public static final short OreRewardResponse = 13206;
	public static final short OreLogRequest = 13207;
	public static final short OreLogResponse = 13208;
	public static final short OreMessageRequest = 13209;
	public static final short OreMessageResponse = 13210;
	public static final short EventGetListRequest = 13301;
	public static final short EventGetListResponse = 13302;
	public static final short EventGetInfoRequest = 13303;
	public static final short EventGetInfoResponse = 13304;
	public static final short EventGetTaskRequest = 13305;
	public static final short EventGetTaskResponse = 13306;
	public static final short EventTaskRewardRequest = 13307;
	public static final short EventTaskRewardResponse = 13308;
	public static final short EventGetShopRequest = 13309;
	public static final short EventGetShopResponse = 13310;
	public static final short EventShopExchangeRequest = 13311;
	public static final short EventShopExchangeResponse = 13312;
	public static final short EventGetRankRequest = 13313;
	public static final short EventGetRankResponse = 13314;
	public static final short MonopolyOnOpenRequest = 13601;
	public static final short MonopolyOnOpenResponse = 13602;
	public static final short MonopolyRollDiceRequest = 13603;
	public static final short MonopolyRollDiceResponse = 13604;
	public static final short MonopolyAccRewardRequest = 13605;
	public static final short MonopolyAccRewardResponse = 13606;
	public static final short MonopolyBuyDiceRequest = 13607;
	public static final short MonopolyBuyDiceResponse = 13608;
	public static final short MonopolyAllAccRewardRequest = 13609;
	public static final short MonopolyAllAccRewardResponse = 13610;
	public static final short FishingOnOpenRequest = 13801;
	public static final short FishingOnOpenResponse = 13802;
	public static final short FishingCastRodRequest = 13805;
	public static final short FishingCastRodResponse = 13806;
	public static final short FishingReelInRequest = 13807;
	public static final short FishingReelInResponse = 13808;
	public static final short FishingBuyBaitRequest = 13811;
	public static final short FishingBuyBaitResponse = 13812;
	public static final short FishingRebornRequest = 13813;
	public static final short FishingRebornResponse = 13814;
	public static final short FlipOnOpenRequest = 13901;
	public static final short FlipOnOpenResponse = 13902;
	public static final short FlipAccRewardRequest = 13905;
	public static final short FlipAccRewardResponse = 13906;
	public static final short FlipBuyStepRequest = 13909;
	public static final short FlipBuyStepResponse = 13910;
	public static final short FlipShowGridRequest = 13911;
	public static final short FlipShowGridResponse = 13912;
	public static final short FlipRewardGridRequest = 13913;
	public static final short FlipRewardGridResponse = 13914;
	public static final short FlipClueGridRequest = 13915;
	public static final short FlipClueGridResponse = 13916;
	public static final short FlipBombGridRequest = 13917;
	public static final short FlipBombGridResponse = 13918;
	public static final short FlipMapFindSpecialRequest = 13919;
	public static final short FlipMapFindSpecialResponse = 13920;
	public static final short FlipAllAccRewardRequest = 13921;
	public static final short FlipAllAccRewardResponse = 13922;
	public static final short DiveOnOpenRequest = 14301;
	public static final short DiveOnOpenResponse = 14302;
	public static final short DiveBuyItemRequest = 14303;
	public static final short DiveBuyItemResponse = 14304;
	public static final short DiveAccRewardRequest = 14305;
	public static final short DiveAccRewardResponse = 14306;
	public static final short DiveShineRequest = 14307;
	public static final short DiveShineResponse = 14308;
	public static final short DiveUsePropRequest = 14309;
	public static final short DiveUsePropResponse = 14310;
	public static final short DiveAllAccRewardRequest = 14311;
	public static final short DiveAllAccRewardResponse = 14312;
	public static final short StartExpeditionRequest = 14401;
	public static final short StartExpeditionResponse = 14402;
	public static final short ExpeditionBattleRequest = 14403;
	public static final short ExpeditionBattleResponse = 14404;
	public static final short ExpeditionUseItemRequest = 14409;
	public static final short ExpeditionUseItemResponse = 14410;
	public static final short ExpeditionGetRewardRequest = 14411;
	public static final short ExpeditionGetRewardResponse = 14412;
	public static final short ExpeditionInfoRequest = 14413;
	public static final short ExpeditionInfoResponse = 14414;
	public static final short ExpeditionSelectRewardRequest = 14415;
	public static final short ExpeditionSelectRewardResponse = 14416;
	public static final short PowerOnOpenRequest = 14901;
	public static final short PowerOnOpenResponse = 14902;
	public static final short PowerRewardRequest = 14903;
	public static final short PowerRewardResponse = 14904;
	public static final short StartDungeonRequest = 15001;
	public static final short StartDungeonResponse = 15002;
	public static final short DailyRechargeRequest = 15101;
	public static final short DailyRechargeResponse = 15102;
	public static final short TotalConsumtionRequest = 15103;
	public static final short TotalConsumtionResponse = 15104;
	public static final short SlgOperateRequest = 16001;
	public static final short SlgOperateResponse = 16002;
	public static final short SlgApplyRequest = 16003;
	public static final short SlgApplyResponse = 16004;
	public static final short SlgInfoRequest = 16005;
	public static final short SlgInfoResponse = 16006;
	public static final short SlgEnterSceneRequest = 16007;
	public static final short SlgEnterSceneResponse = 16008;
	public static final short SlgUserBattleDtoRequest = 16009;
	public static final short SlgUserBattleDtoResponse = 16010;
	public static final short SlgTeamRebirthRequest = 16011;
	public static final short SlgTeamRebirthResponse = 16012;
	public static final short SlgBacklogListRequest = 16013;
	public static final short SlgBacklogListResponse = 16014;
	public static final short SlgExecutiveBacklogRequest = 16015;
	public static final short SlgExecutiveBacklogResponse = 16016;
	public static final short PowerRequest = 20103;
	public static final short PowerResponse = 20104;
	public static final short RExpeditionCombatReq = 20107;
	public static final short RExpeditionCombatResp = 20108;
	public static final short RChapterStartReq = 20109;
	public static final short RChapterStartResp = 20110;
	public static final short RChapterVerifyReq = 20111;
	public static final short RChapterVerifyResp = 20112;
	public static final short RDungeonCombatReq = 20113;
	public static final short RDungeonCombatResp = 20114;
	public static final short RSlgCombatReq = 20115;
	public static final short RSlgCombatResp = 20116;
	public static final short RSlgPvECombatReq = 20117;
	public static final short RSlgPvECombatResp = 20118;
	public static final short GuildGetInfoRequest = 30101;
	public static final short GuildGetInfoResponse = 30102;
	public static final short GuildCreateRequest = 30103;
	public static final short GuildCreateResponse = 30104;
	public static final short GuildSearchRequest = 30105;
	public static final short GuildSearchResponse = 30106;
	public static final short GuildGetDetailRequest = 30107;
	public static final short GuildGetDetailResponse = 30108;
	public static final short GuildGetMemberListRequest = 30109;
	public static final short GuildGetMemberListResponse = 30110;
	public static final short GuildModifyRequest = 30111;
	public static final short GuildModifyResponse = 30112;
	public static final short GuildDismissRequest = 30113;
	public static final short GuildDismissResponse = 30114;
	public static final short GuildApplyJoinRequest = 30115;
	public static final short GuildApplyJoinResponse = 30116;
	public static final short GuildCancelApplyRequest = 30117;
	public static final short GuildCancelApplyResponse = 30118;
	public static final short GuildAutoJoinRequest = 30119;
	public static final short GuildAutoJoinResponse = 30120;
	public static final short GuildGetApplyListRequest = 30121;
	public static final short GuildGetApplyListResponse = 30122;
	public static final short GuildAgreeJoinRequest = 30123;
	public static final short GuildAgreeJoinResponse = 30124;
	public static final short GuildRefuseJoinRequest = 30125;
	public static final short GuildRefuseJoinResponse = 30126;
	public static final short GuildKickOutRequest = 30127;
	public static final short GuildKickOutResponse = 30128;
	public static final short GuildLeaveRequest = 30129;
	public static final short GuildLeaveResponse = 30130;
	public static final short GuildUpPositionRequest = 30131;
	public static final short GuildUpPositionResponse = 30132;
	public static final short GuildTransferPresidentRequest = 30133;
	public static final short GuildTransferPresidentResponse = 30134;
	public static final short GuildGetFeaturesInfoRequest = 30135;
	public static final short GuildGetFeaturesInfoResponse = 30136;
	public static final short GuildLevelUpRequest = 30137;
	public static final short GuildLevelUpResponse = 30138;
	public static final short GuildSignInRequest = 30141;
	public static final short GuildSignInResponse = 30142;
	public static final short GuildShopBuyRequest = 30151;
	public static final short GuildShopBuyResponse = 30152;
	public static final short GuildShopRefreshRequest = 30153;
	public static final short GuildShopRefreshResponse = 30154;
	public static final short GuildTaskRewardRequest = 30161;
	public static final short GuildTaskRewardResponse = 30162;
	public static final short GuildTaskRefreshRequest = 30163;
	public static final short GuildTaskRefreshResponse = 30164;
	public static final short GuildGetMessageRecordsRequest = 30171;
	public static final short GuildGetMessageRecordsResponse = 30172;
	public static final short GuildBossGetInfoRequest = 30181;
	public static final short GuildBossGetInfoResponse = 30182;
	public static final short GuildBossStartRequest = 30183;
	public static final short GuildBossStartResponse = 30184;
	public static final short GuildBossEndRequest = 30185;
	public static final short GuildBossEndResponse = 30186;
	public static final short GuildBossBuyCntRequest = 30187;
	public static final short GuildBossBuyCntResponse = 30188;
	public static final short GuildBossTaskRewardRequest = 30189;
	public static final short GuildBossTaskRewardResponse = 30190;
	public static final short GuildBossBoxRewardRequest = 30191;
	public static final short GuildBossBoxRewardResponse = 30192;
	public static final short GuildBossGetRankListRequest = 30193;
	public static final short GuildBossGetRankListResponse = 30194;
	public static final short GuildDonationReqItemRequest = 30201;
	public static final short GuildDonationReqItemResponse = 30202;
	public static final short GuildDonationSendItemRequest = 30203;
	public static final short GuildDonationSendItemResponse = 30204;
	public static final short GuildDonationReceiveRequest = 30205;
	public static final short GuildDonationReceiveResponse = 30206;
	public static final short GuildDonationGetRecordsRequest = 30207;
	public static final short GuildDonationGetRecordsResponse = 30208;
	public static final short GuildDonationGetOperationRecordsRequest = 30209;
	public static final short GuildDonationGetOperationRecordsResponse = 30210;
	public static final short GuildBossBattleRequest = 30211;
	public static final short GuildBossBattleResponse = 30212;
	public static final short GuildBossBattleGRankRequest = 30213;
	public static final short GuildBossBattleGRankResponse = 30214;
	public static final short GuildRaceGuildApplyRequest = 30301;
	public static final short GuildRaceGuildApplyResponse = 30302;
	public static final short GuildRaceUserApplyRequest = 30303;
	public static final short GuildRaceUserApplyResponse = 30304;
	public static final short GuildRaceEditSeqRequest = 30305;
	public static final short GuildRaceEditSeqResponse = 30306;
	public static final short GuildRaceInfoRequest = 30307;
	public static final short GuildRaceInfoResponse = 30308;
	public static final short GuildRaceOwnerUserApplyListRequest = 30309;
	public static final short GuildRaceOwnerUserApplyListResponse = 30310;
	public static final short GuildRaceOppUserApplyListRequest = 30311;
	public static final short GuildRaceOppUserApplyListResponse = 30312;
	public static final short GuildRaceUserInfoRequest = 30313;
	public static final short GuildRaceUserInfoResponse = 30314;
	public static final short GuildRaceGuildRecordRequest = 30315;
	public static final short GuildRaceGuildRecordResponse = 30316;
	public static final short GuildRacePVPRecordRequest = 30317;
	public static final short GuildRacePVPRecordResponse = 30318;
	public static final short GuildTechUpgradeRequest = 30501;
	public static final short GuildTechUpgradeResponse = 30502;
	public static final short SocketLoginRequest = 31101;
	public static final short SocketLoginResponse = 31102;
	public static final short SocketJoinGroupRequest = 31103;
	public static final short SocketJoinGroupResponse = 31104;
	public static final short SocketQuitGroupRequest = 31105;
	public static final short SocketQuitGroupResponse = 31106;
	public static final short SocketHeartBeatRequest = 31107;
	public static final short SocketHeartBeatResponse = 31108;
	public static final short SocketLoginRepeatMessage = 31202;
	public static final short SocketReconnectMessage = 31204;
	public static final short SocketPushMessage = 31206;
	public static final short SocketErrorMessage = 31299;
	public static final short ChatTranslateRequest = 32101;
	public static final short ChatTranslateResponse = 32102;
	public static final short ChatGuildRequest = 32103;
	public static final short ChatGuildResponse = 32104;
	public static final short ChatGuildShowItemRequest = 32105;
	public static final short ChatGuildShowItemResponse = 32106;
	public static final short ChatCommonRequest = 32107;
	public static final short ChatCommonResponse = 32108;
	public static final short ChatGetMessageRecordsRequest = 32109;
	public static final short ChatGetMessageRecordsResponse = 32110;
	public static final short ChatSetBlacklistRequest = 32111;
	public static final short ChatSetBlacklistResponse = 32112;
	public static final short ChatSetChapterLimitRequest = 32113;
	public static final short ChatSetChapterLimitResponse = 32114;
	public static final short ChatGetOnlineStatusRequest = 32115;
	public static final short ChatGetOnlineStatusResponse = 32116;


}