package com.dxx.game.consts;

/**
 * 奖励类型
 * <AUTHOR>
 * @date 2019-12-13 15:49
 */
public enum RewardType {

    /**
     * 金币，钻石 .. 等资源
     */
    RESOURCE(0),
    /**
     * 装备
     */
    EQUIP(1),
    /**
     * 英雄
     */
    HERO(2),

    /**
     * 消耗品道具
     */
    ITEM(3),

    /**
     * 随机道具礼包
     */
    ITEM_RAND_GIFT(4),
    /**
     * 自选礼包
     */
    ITEM_OPTIONAL_GIFT(5),
    /**
     * 按时间获取奖励的礼包(挂机礼包)
     */
    ITEM_HUANG_UP_TIME_GIFT(6),
    /**
     * 英雄碎片
     */
    ITEM_HERO_FRAGMENT(8),

    /**
     * 皮肤
     */
    SKIN(20),

    /**
     * 遗物
     */
    RELIC(13),

    /**
     * 通用门票
     */
    TICKET(30),

    /** 公会商店货币 */
    GUILD_SHOP_COIN(41),
    /** 公会经验 */
    GUILD_EXP(42),
    /** 公会活跃度 */
    GUILD_ACTIVE(43),

    /**
     * 默认
     */
    DEFAULT(-1),
    ;
	
	/**
     * 类型值
     */
    private final int value;

    private RewardType(int value) {
        this.value = value;
    }
    
    /**
     * 类型值
     *
     * @return int
     */
    public int getValue() {
        return value;
    }
    
    /**
     * 生成 RewardType
     *
     * @param value 类型值
     * @return RewardType
     */
    public static RewardType valueOf(int value) {
        for (RewardType type : RewardType.values()) {
            if (type.getValue() == value) {
                return type;
            }
        }
        return RewardType.DEFAULT;
    }
}
