package com.dxx.game.rpc;

import java.lang.invoke.MethodHandle;

/**
 * @author: lin
 * @date: 2024/12/27
 */
public class RpcMethod {
    private Class<?> clazz;
    private MethodHandle method;

    public RpcMethod(Class<?> clazz, MethodHandle method) {
        this.clazz = clazz;
        this.method = method;
    }

    public Class<?> getClazz() {
        return clazz;
    }

    public void setClazz(Class<?> clazz) {
        this.clazz = clazz;
    }

    public MethodHandle getMethod() {
        return method;
    }

    public void setMethod(MethodHandle method) {
        this.method = method;
    }
}
