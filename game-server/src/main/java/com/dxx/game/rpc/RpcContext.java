package com.dxx.game.rpc;

import io.netty.util.concurrent.FastThreadLocal;

/**
 * @author: lin
 * @date: 2025/1/6
 */
public class RpcContext {
    /** rpc调用地址 */
    private static FastThreadLocal<String> callNodeId = new FastThreadLocal<>();
    private static FastThreadLocal<String> callServiceId = new FastThreadLocal<>();

    public static void setNodeId(String nodeId) {
        callNodeId.set(nodeId);
    }
    public static String getNodeId() {
        return callNodeId.get();
    }

    public static void setRpcServiceId(String nodeId) {
        callServiceId.set(nodeId);
    }
    public static String getRpcServiceId() {
        return callServiceId.get();
    }

    public static void clear() {
        callNodeId.remove();
        callServiceId.remove();
    }
}
