package com.dxx.game.rpc;

import com.dxx.game.rpc.dto.RpcProto;
import com.dxx.game.rpc.dto.RpcServiceGrpc;
import io.grpc.Deadline;
import io.grpc.ManagedChannel;
import io.grpc.ManagedChannelBuilder;
import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.TimeUnit;

/**
 * @author: lin
 * @date: 2025/1/6
 */
@Slf4j
public class RpcClient {
    private ManagedChannel channel;
    private RpcServiceGrpc.RpcServiceBlockingStub blockingStub;

    public RpcClient(String url) {
        String[] address = url.split(":");
        String host = address[0];
        int port = Integer.parseInt(address[1]);

        if (host.endsWith(".com")) {
            this.channel = ManagedChannelBuilder.forAddress(host, port)
                    .useTransportSecurity()
                    .idleTimeout(1, TimeUnit.MINUTES)
                    .build();
        } else {
            String target = url;
            log.info("grpc target: {}", target);
            this.channel = ManagedChannelBuilder.forTarget(target)
                    .usePlaintext()
                    .idleTimeout(1, TimeUnit.MINUTES)
                    .build();
        }
        this.blockingStub = RpcServiceGrpc.newBlockingStub(this.channel);
    }

    /**
     * TODO
     */
    public void shutdown() throws InterruptedException {
        this.channel.shutdown().awaitTermination(5, TimeUnit.SECONDS);
    }

    public RpcProto.RpcResp call(RpcProto.RpcReq req) {
        return this.blockingStub.withDeadline(Deadline.after(5, TimeUnit.SECONDS)).call(req);
    }
}
