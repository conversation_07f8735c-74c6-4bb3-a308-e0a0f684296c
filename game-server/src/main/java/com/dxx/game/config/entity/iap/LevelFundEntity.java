package com.dxx.game.config.entity.iap;

import java.util.List;
import com.dxx.game.common.config.game.converter.*;
import com.dxx.game.common.config.game.annotation.FieldMeta;
import com.dxx.game.common.config.game.entity.BaseEntity;
import lombok.Getter;
import lombok.ToString;

@Getter
@ToString
public class LevelFundEntity extends BaseEntity {


	/**
	 * id
	 */
	@FieldMeta(index = 0, converter = DefaultFieldConverter.class)
	private int id;
	/**
	 * 描述
	 * 
	 */
	@FieldMeta(index = 1, converter = DefaultFieldConverter.class)
	private String notes;
	/**
	 * 基金奖励组id
	 */
	@FieldMeta(index = 2, converter = DefaultFieldConverter.class)
	private int groupId;
	/**
	 * 参数类型：
	 * 1.主线关卡ID
	 * 8.挑战之塔ID
	 * 14.大迷宫通关次数 
	 * 19.宝箱开启次数
	 * 54.战神挑战难度
	 */
	@FieldMeta(index = 3, converter = DefaultFieldConverter.class)
	private int paramType;
	/**
	 * 是否为累加条件
	 * 0,非累加
	 * 1,累加
	 */
	@FieldMeta(index = 4, converter = DefaultFieldConverter.class)
	private int AccumulationType;
	/**
	 * 当即奖励
	 * itemid,count,showCount|itemid,count,,showCount
	 */
	@FieldMeta(index = 5, converter = MatrixIntegerConverter.class)
	private List<List<Integer>> Products;

}