package com.dxx.game.config.entity.maze;

import java.util.List;
import com.dxx.game.common.config.game.converter.*;
import com.dxx.game.common.config.game.annotation.FieldMeta;
import com.dxx.game.common.config.game.entity.BaseEntity;
import lombok.Getter;
import lombok.ToString;

@Getter
@ToString
public class FlopEntity extends BaseEntity {


	/**
	 * id
	 */
	@FieldMeta(index = 0, converter = DefaultFieldConverter.class)
	private int ID;
	/**
	 * 道具配置
	 * itemId,itemCount,weight
	 */
	@FieldMeta(index = 1, converter = MatrixIntegerConverter.class)
	private List<List<Integer>> Items;
	/**
	 * 所需最小等级
	 */
	@FieldMeta(index = 2, converter = DefaultFieldConverter.class)
	private int MinLevel;
	/**
	 * 所需最大等级
	 */
	@FieldMeta(index = 3, converter = DefaultFieldConverter.class)
	private int MaxLevel;
	/**
	 * 权重
	 */
	@FieldMeta(index = 4, converter = DefaultFieldConverter.class)
	private int Weights;

}