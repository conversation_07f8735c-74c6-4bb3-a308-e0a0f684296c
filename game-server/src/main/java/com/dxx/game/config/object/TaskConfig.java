package com.dxx.game.config.object;

import java.util.concurrent.ConcurrentSkipListMap;
import com.dxx.game.common.config.game.GameConfiguration;
import com.dxx.game.common.config.game.annotation.Resource;
import lombok.Getter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import com.dxx.game.config.entity.task.DailyActiveEntity;
import com.dxx.game.config.entity.task.DailyTaskEntity;
import com.dxx.game.config.entity.task.WeeklyActiveEntity;


@Slf4j
@Getter
@ToString
@Resource(name="Task")
public class TaskConfig extends GameConfiguration<TaskConfig> {
	
    private ConcurrentSkipListMap<Integer, DailyActiveEntity> dailyActive = new ConcurrentSkipListMap<>();
    private ConcurrentSkipListMap<Integer, DailyTaskEntity> dailyTask = new ConcurrentSkipListMap<>();
    private ConcurrentSkipListMap<Integer, WeeklyActiveEntity> weeklyActive = new ConcurrentSkipListMap<>();



    public DailyActiveEntity getDailyActiveEntity(int id) {
		if (!dailyActive.containsKey(id)) {
			log.error("DailyActiveEntity config not found ----> id:{}", id);
		}
	    return dailyActive.get(id);
	}

    public DailyTaskEntity getDailyTaskEntity(int id) {
		if (!dailyTask.containsKey(id)) {
			log.error("DailyTaskEntity config not found ----> id:{}", id);
		}
	    return dailyTask.get(id);
	}

    public WeeklyActiveEntity getWeeklyActiveEntity(int id) {
		if (!weeklyActive.containsKey(id)) {
			log.error("WeeklyActiveEntity config not found ----> id:{}", id);
		}
	    return weeklyActive.get(id);
	}


}
