package com.dxx.game.config.entity.rogueskill;

import java.util.List;
import com.dxx.game.common.config.game.converter.*;
import com.dxx.game.common.config.game.annotation.FieldMeta;
import com.dxx.game.common.config.game.entity.BaseEntity;
import lombok.Getter;
import lombok.ToString;

@Getter
@ToString
public class PoolEntity extends BaseEntity {


	/**
	 * 内容ID
	 */
	@FieldMeta(index = 0, converter = DefaultFieldConverter.class)
	private int id;
	/**
	 * 随机池
	 */
	@FieldMeta(index = 1, converter = MatrixIntegerConverter.class)
	private List<List<Integer>> pool;
	/**
	 * 保底库
	 * 随机库抽完后使用的保底随机库
	 * 0表示不指定保底随机库，仍然pool中随机
	 */
	@FieldMeta(index = 2, converter = DefaultFieldConverter.class)
	private int rollbackPool;

}