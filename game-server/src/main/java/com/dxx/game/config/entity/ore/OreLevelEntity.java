package com.dxx.game.config.entity.ore;

import java.util.List;
import com.dxx.game.common.config.game.converter.*;
import com.dxx.game.common.config.game.annotation.FieldMeta;
import com.dxx.game.common.config.game.entity.BaseEntity;
import lombok.Getter;
import lombok.ToString;

@Getter
@ToString
public class OreLevelEntity extends BaseEntity {


	/**
	 * Id
	 */
	@FieldMeta(index = 0, converter = DefaultFieldConverter.class)
	private int ID;
	/**
	 * 矿点等级
	 */
	@FieldMeta(index = 1, converter = DefaultFieldConverter.class)
	private int Level;
	/**
	 * 占领时间（分钟）
	 */
	@FieldMeta(index = 2, converter = DefaultFieldConverter.class)
	private int OccupationTime;
	/**
	 * 每分钟产出
	 */
	@FieldMeta(index = 3, converter = MatrixIntegerConverter.class)
	private List<List<Integer>> OreProduction;

}