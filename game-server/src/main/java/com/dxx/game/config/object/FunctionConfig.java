package com.dxx.game.config.object;

import java.util.concurrent.ConcurrentSkipListMap;
import com.dxx.game.common.config.game.GameConfiguration;
import com.dxx.game.common.config.game.annotation.Resource;
import lombok.Getter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import com.dxx.game.config.entity.function.FunctionEntity;


@Slf4j
@Getter
@ToString
@Resource(name="Function")
public class FunctionConfig extends GameConfiguration<FunctionConfig> {
	
    private ConcurrentSkipListMap<Integer, FunctionEntity> function = new ConcurrentSkipListMap<>();



    public FunctionEntity getFunctionEntity(int id) {
		if (!function.containsKey(id)) {
			log.error("FunctionEntity config not found ----> id:{}", id);
		}
	    return function.get(id);
	}


}
