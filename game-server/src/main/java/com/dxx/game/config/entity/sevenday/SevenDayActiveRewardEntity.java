package com.dxx.game.config.entity.sevenday;

import java.util.List;
import com.dxx.game.common.config.game.converter.*;
import com.dxx.game.common.config.game.annotation.FieldMeta;
import com.dxx.game.common.config.game.entity.BaseEntity;
import lombok.Getter;
import lombok.ToString;

@Getter
@ToString
public class SevenDayActiveRewardEntity extends BaseEntity {


	/**
	 * ID
	 * 
	 */
	@FieldMeta(index = 0, converter = DefaultFieldConverter.class)
	private int ID;
	/**
	 * 需要的活跃度
	 */
	@FieldMeta(index = 1, converter = DefaultFieldConverter.class)
	private int NeedActive;
	/**
	 * 活跃度奖励
	 * 道具id,道具数量|…
	 */
	@FieldMeta(index = 2, converter = MatrixIntegerConverter.class)
	private List<List<Integer>> Reward;
	/**
	 * 是否
	 * 装备奖励
	 */
	@FieldMeta(index = 3, converter = DefaultFieldConverter.class)
	private int IfEquip;

}