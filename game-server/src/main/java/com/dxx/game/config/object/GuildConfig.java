package com.dxx.game.config.object;

import java.util.concurrent.ConcurrentSkipListMap;
import com.dxx.game.common.config.game.GameConfiguration;
import com.dxx.game.common.config.game.annotation.Resource;
import lombok.Getter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import com.dxx.game.config.entity.guild.GuildBossEntity;
import com.dxx.game.config.entity.guild.GuildBossBoxEntity;
import com.dxx.game.config.entity.guild.GuildBossOpenEntity;
import com.dxx.game.config.entity.guild.GuildBossTaskEntity;
import com.dxx.game.config.entity.guild.GuildConstEntity;
import com.dxx.game.config.entity.guild.GuildDonationEntity;
import com.dxx.game.config.entity.guild.GuildEventEntity;
import com.dxx.game.config.entity.guild.GuildLanguageEntity;
import com.dxx.game.config.entity.guild.GuildLevelEntity;
import com.dxx.game.config.entity.guild.GuildPowerEntity;
import com.dxx.game.config.entity.guild.GuildShopEntity;
import com.dxx.game.config.entity.guild.GuildSignInEntity;
import com.dxx.game.config.entity.guild.GuildStyleEntity;
import com.dxx.game.config.entity.guild.GuildTaskEntity;


@Slf4j
@Getter
@ToString
@Resource(name="Guild")
public class GuildConfig extends GameConfiguration<GuildConfig> {
	
    private ConcurrentSkipListMap<Integer, GuildBossEntity> guildBoss = new ConcurrentSkipListMap<>();
    private ConcurrentSkipListMap<Integer, GuildBossBoxEntity> guildBossBox = new ConcurrentSkipListMap<>();
    private ConcurrentSkipListMap<Integer, GuildBossOpenEntity> guildBossOpen = new ConcurrentSkipListMap<>();
    private ConcurrentSkipListMap<Integer, GuildBossTaskEntity> guildBossTask = new ConcurrentSkipListMap<>();
    private ConcurrentSkipListMap<Integer, GuildConstEntity> guildConst = new ConcurrentSkipListMap<>();
    private ConcurrentSkipListMap<Integer, GuildDonationEntity> guildDonation = new ConcurrentSkipListMap<>();
    private ConcurrentSkipListMap<Integer, GuildEventEntity> guildEvent = new ConcurrentSkipListMap<>();
    private ConcurrentSkipListMap<Integer, GuildLanguageEntity> guildLanguage = new ConcurrentSkipListMap<>();
    private ConcurrentSkipListMap<Integer, GuildLevelEntity> guildLevel = new ConcurrentSkipListMap<>();
    private ConcurrentSkipListMap<Integer, GuildPowerEntity> guildPower = new ConcurrentSkipListMap<>();
    private ConcurrentSkipListMap<Integer, GuildShopEntity> guildShop = new ConcurrentSkipListMap<>();
    private ConcurrentSkipListMap<Integer, GuildSignInEntity> guildSignIn = new ConcurrentSkipListMap<>();
    private ConcurrentSkipListMap<Integer, GuildStyleEntity> guildStyle = new ConcurrentSkipListMap<>();
    private ConcurrentSkipListMap<Integer, GuildTaskEntity> guildTask = new ConcurrentSkipListMap<>();



    public GuildBossEntity getGuildBossEntity(int id) {
		if (!guildBoss.containsKey(id)) {
			log.error("GuildBossEntity config not found ----> id:{}", id);
		}
	    return guildBoss.get(id);
	}

    public GuildBossBoxEntity getGuildBossBoxEntity(int id) {
		if (!guildBossBox.containsKey(id)) {
			log.error("GuildBossBoxEntity config not found ----> id:{}", id);
		}
	    return guildBossBox.get(id);
	}

    public GuildBossOpenEntity getGuildBossOpenEntity(int id) {
		if (!guildBossOpen.containsKey(id)) {
			log.error("GuildBossOpenEntity config not found ----> id:{}", id);
		}
	    return guildBossOpen.get(id);
	}

    public GuildBossTaskEntity getGuildBossTaskEntity(int id) {
		if (!guildBossTask.containsKey(id)) {
			log.error("GuildBossTaskEntity config not found ----> id:{}", id);
		}
	    return guildBossTask.get(id);
	}

    public GuildConstEntity getGuildConstEntity(int id) {
		if (!guildConst.containsKey(id)) {
			log.error("GuildConstEntity config not found ----> id:{}", id);
		}
	    return guildConst.get(id);
	}

    public GuildDonationEntity getGuildDonationEntity(int id) {
		if (!guildDonation.containsKey(id)) {
			log.error("GuildDonationEntity config not found ----> id:{}", id);
		}
	    return guildDonation.get(id);
	}

    public GuildEventEntity getGuildEventEntity(int id) {
		if (!guildEvent.containsKey(id)) {
			log.error("GuildEventEntity config not found ----> id:{}", id);
		}
	    return guildEvent.get(id);
	}

    public GuildLanguageEntity getGuildLanguageEntity(int id) {
		if (!guildLanguage.containsKey(id)) {
			log.error("GuildLanguageEntity config not found ----> id:{}", id);
		}
	    return guildLanguage.get(id);
	}

    public GuildLevelEntity getGuildLevelEntity(int id) {
		if (!guildLevel.containsKey(id)) {
			log.error("GuildLevelEntity config not found ----> id:{}", id);
		}
	    return guildLevel.get(id);
	}

    public GuildPowerEntity getGuildPowerEntity(int id) {
		if (!guildPower.containsKey(id)) {
			log.error("GuildPowerEntity config not found ----> id:{}", id);
		}
	    return guildPower.get(id);
	}

    public GuildShopEntity getGuildShopEntity(int id) {
		if (!guildShop.containsKey(id)) {
			log.error("GuildShopEntity config not found ----> id:{}", id);
		}
	    return guildShop.get(id);
	}

    public GuildSignInEntity getGuildSignInEntity(int id) {
		if (!guildSignIn.containsKey(id)) {
			log.error("GuildSignInEntity config not found ----> id:{}", id);
		}
	    return guildSignIn.get(id);
	}

    public GuildStyleEntity getGuildStyleEntity(int id) {
		if (!guildStyle.containsKey(id)) {
			log.error("GuildStyleEntity config not found ----> id:{}", id);
		}
	    return guildStyle.get(id);
	}

    public GuildTaskEntity getGuildTaskEntity(int id) {
		if (!guildTask.containsKey(id)) {
			log.error("GuildTaskEntity config not found ----> id:{}", id);
		}
	    return guildTask.get(id);
	}


}
