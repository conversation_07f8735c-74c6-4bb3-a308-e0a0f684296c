package com.dxx.game.config.object;

public class OneStoreApiConfig {
	
	private String dev_host;
	private String product_host;
	private String url;
	private int expire_in;
	private String grant_type;
	private String client_id;
	private String client_secret;
	
	public String getUrl() {
		return url;
	}
	public void setUrl(String url) {
		this.url = url;
	}
	public String getDev_host() {
		return dev_host;
	}
	public void setDev_host(String dev_host) {
		this.dev_host = dev_host;
	}
	public String getProduct_host() {
		return product_host;
	}
	public void setProduct_host(String product_host) {
		this.product_host = product_host;
	}
	public int getExpire_in() {
		return expire_in;
	}
	public void setExpire_in(int expire_in) {
		this.expire_in = expire_in;
	}
	public String getGrant_type() {
		return grant_type;
	}
	public void setGrant_type(String grant_type) {
		this.grant_type = grant_type;
	}
	public String getClient_id() {
		return client_id;
	}
	public void setClient_id(String client_id) {
		this.client_id = client_id;
	}
	public String getClient_secret() {
		return client_secret;
	}
	public void setClient_secret(String client_secret) {
		this.client_secret = client_secret;
	}
}
