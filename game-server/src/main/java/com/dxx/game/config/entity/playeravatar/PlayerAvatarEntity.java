package com.dxx.game.config.entity.playeravatar;

import java.util.List;
import com.dxx.game.common.config.game.converter.*;
import com.dxx.game.common.config.game.annotation.FieldMeta;
import com.dxx.game.common.config.game.entity.BaseEntity;
import lombok.Getter;
import lombok.ToString;

@Getter
@ToString
public class PlayerAvatarEntity extends BaseEntity {


	/**
	 * 内容ID
	 */
	@FieldMeta(index = 0, converter = DefaultFieldConverter.class)
	private int id;
	/**
	 * 注释
	 */
	@FieldMeta(index = 1, converter = DefaultFieldConverter.class)
	private String notes;

}