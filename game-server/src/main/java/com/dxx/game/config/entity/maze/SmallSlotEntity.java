package com.dxx.game.config.entity.maze;

import java.util.List;
import com.dxx.game.common.config.game.converter.*;
import com.dxx.game.common.config.game.annotation.FieldMeta;
import com.dxx.game.common.config.game.entity.BaseEntity;
import lombok.Getter;
import lombok.ToString;

@Getter
@ToString
public class SmallSlotEntity extends BaseEntity {


	/**
	 * id
	 */
	@FieldMeta(index = 0, converter = DefaultFieldConverter.class)
	private int id;
	/**
	 * 权重
	 */
	@FieldMeta(index = 1, converter = DefaultFieldConverter.class)
	private int weight;
	/**
	 * 分组
	 */
	@FieldMeta(index = 2, converter = DefaultFieldConverter.class)
	private int group;
	/**
	 * 老虎机类型
	 * 1：属性，参数（Attack%=100）
	 * 2：物品（ID），参数（1001）
	 * 3：技能随机，参数（技能库ID）
	 */
	@FieldMeta(index = 3, converter = DefaultFieldConverter.class)
	private int slotKind;
	/**
	 * 参数
	 */
	@FieldMeta(index = 4, converter = DefaultFieldConverter.class)
	private String param;

}