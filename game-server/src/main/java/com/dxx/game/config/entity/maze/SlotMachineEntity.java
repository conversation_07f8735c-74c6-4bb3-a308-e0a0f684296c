package com.dxx.game.config.entity.maze;

import java.util.List;
import com.dxx.game.common.config.game.converter.*;
import com.dxx.game.common.config.game.annotation.FieldMeta;
import com.dxx.game.common.config.game.entity.BaseEntity;
import lombok.Getter;
import lombok.ToString;

@Getter
@ToString
public class SlotMachineEntity extends BaseEntity {


	/**
	 * id
	 */
	@FieldMeta(index = 0, converter = DefaultFieldConverter.class)
	private int ID;
	/**
	 * 道具配置(itemId:0问号)
	 * itemId,itemCount|...
	 */
	@FieldMeta(index = 1, converter = MatrixIntegerConverter.class)
	private List<List<Integer>> Items;
	/**
	 * 显示道具图片
	 * 图片ID|…
	 */
	@FieldMeta(index = 2, converter = ListStringConverter.class)
	private List<String> showItems;
	/**
	 * 获得3种相同的各道具的概率/万分之
	 */
	@FieldMeta(index = 3, converter = ListIntConverter.class)
	private List<Integer> ThreeProbability;
	/**
	 * 获得2种相同的各道具的概率/万分之
	 */
	@FieldMeta(index = 4, converter = ListIntConverter.class)
	private List<Integer> TwoProbability;
	/**
	 * 没有相同道具的权重
	 */
	@FieldMeta(index = 5, converter = ListIntConverter.class)
	private List<Integer> OneProbability;
	/**
	 * 获得3种时奖励的各道具的数量倍率
	 */
	@FieldMeta(index = 6, converter = ListIntConverter.class)
	private List<Integer> ThreeItemRatio;
	/**
	 * 获得2种时奖励的各道具的数量倍率
	 */
	@FieldMeta(index = 7, converter = ListIntConverter.class)
	private List<Integer> TwoItemRatio;
	/**
	 * 所需最小等级
	 */
	@FieldMeta(index = 8, converter = DefaultFieldConverter.class)
	private int MinLevel;
	/**
	 * 所需最大等级
	 */
	@FieldMeta(index = 9, converter = DefaultFieldConverter.class)
	private int MaxLevel;
	/**
	 * 权重
	 */
	@FieldMeta(index = 10, converter = DefaultFieldConverter.class)
	private int Weights;
	/**
	 * 问号的DropId
	 */
	@FieldMeta(index = 11, converter = DefaultFieldConverter.class)
	private int DropId;

}