package com.dxx.game.config.object;

import java.util.concurrent.ConcurrentSkipListMap;
import com.dxx.game.common.config.game.GameConfiguration;
import com.dxx.game.common.config.game.annotation.Resource;
import lombok.Getter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import com.dxx.game.config.entity.dungeon.DungeonBaseEntity;
import com.dxx.game.config.entity.dungeon.DungeonLevelEntity;


@Slf4j
@Getter
@ToString
@Resource(name="Dungeon")
public class DungeonConfig extends GameConfiguration<DungeonConfig> {
	
    private ConcurrentSkipListMap<Integer, DungeonBaseEntity> dungeonBase = new ConcurrentSkipListMap<>();
    private ConcurrentSkipListMap<Integer, DungeonLevelEntity> dungeonLevel = new ConcurrentSkipListMap<>();



    public DungeonBaseEntity getDungeonBaseEntity(int id) {
		if (!dungeonBase.containsKey(id)) {
			log.error("DungeonBaseEntity config not found ----> id:{}", id);
		}
	    return dungeonBase.get(id);
	}

    public DungeonLevelEntity getDungeonLevelEntity(int id) {
		if (!dungeonLevel.containsKey(id)) {
			log.error("DungeonLevelEntity config not found ----> id:{}", id);
		}
	    return dungeonLevel.get(id);
	}


}
