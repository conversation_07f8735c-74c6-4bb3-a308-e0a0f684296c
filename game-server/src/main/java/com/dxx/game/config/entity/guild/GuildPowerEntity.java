package com.dxx.game.config.entity.guild;

import java.util.List;
import com.dxx.game.common.config.game.converter.*;
import com.dxx.game.common.config.game.annotation.FieldMeta;
import com.dxx.game.common.config.game.entity.BaseEntity;
import lombok.Getter;
import lombok.ToString;

@Getter
@ToString
public class GuildPowerEntity extends BaseEntity {


	/**
	 * Id
	 */
	@FieldMeta(index = 0, converter = DefaultFieldConverter.class)
	private int ID;
	/**
	 * 备注
	 */
	@FieldMeta(index = 1, converter = DefaultFieldConverter.class)
	private String Notes;
	/**
	 * 权限
	 * 1.审批加入
	 * 2.修改公会信息与入会要求
	 * 3.活动相关
	 * 4.任命/取消管理
	 * 5.任命/取消副会长
	 * 6.解散公会
	 * 7.踢人
	 * 8.升级公会
	 * 
	 */
	@FieldMeta(index = 2, converter = ListIntConverter.class)
	private List<Integer> Power;

}