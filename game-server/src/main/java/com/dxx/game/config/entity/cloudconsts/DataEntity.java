package com.dxx.game.config.entity.cloudconsts;

import java.util.List;
import com.dxx.game.common.config.game.converter.*;
import com.dxx.game.common.config.game.annotation.FieldMeta;
import com.dxx.game.common.config.game.entity.BaseEntity;
import lombok.Getter;
import lombok.ToString;

@Getter
@ToString
public class DataEntity extends BaseEntity {


	/**
	 * id
	 */
	@FieldMeta(index = 0, converter = DefaultFieldConverter.class)
	private int id;
	/**
	 * 描述
	 */
	@FieldMeta(index = 1, converter = DefaultFieldConverter.class)
	private String Des;
	/**
	 * 值
	 */
	@FieldMeta(index = 2, converter = ListStringConverter.class)
	private List<String> Vaule;

}