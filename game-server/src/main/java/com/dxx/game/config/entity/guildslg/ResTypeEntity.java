package com.dxx.game.config.entity.guildslg;

import java.util.List;
import com.dxx.game.common.config.game.converter.*;
import com.dxx.game.common.config.game.annotation.FieldMeta;
import com.dxx.game.common.config.game.entity.BaseEntity;
import lombok.Getter;
import lombok.ToString;

@Getter
@ToString
public class ResTypeEntity extends BaseEntity {


	/**
	 * 编号（从10000开始）
	 */
	@FieldMeta(index = 0, converter = DefaultFieldConverter.class)
	private int Id;
	/**
	 * 类型
	 */
	@FieldMeta(index = 1, converter = DefaultFieldConverter.class)
	private int typeName;
	/**
	 * 组id
	 */
	@FieldMeta(index = 2, converter = DefaultFieldConverter.class)
	private int groupId;
	/**
	 * 积分生成数量一次性
	 */
	@FieldMeta(index = 3, converter = DefaultFieldConverter.class)
	private String rePoints;
	/**
	 * 奖励组id（工会成员一份）
	 */
	@FieldMeta(index = 4, converter = DefaultFieldConverter.class)
	private int rewardsGroup;
	/**
	 * 刷新时间分钟
	 */
	@FieldMeta(index = 5, converter = DefaultFieldConverter.class)
	private int refreshTime;
	/**
	 * 每次刷新补充数量
	 */
	@FieldMeta(index = 6, converter = DefaultFieldConverter.class)
	private int refreshNum;
	/**
	 * 数量上限
	 */
	@FieldMeta(index = 7, converter = DefaultFieldConverter.class)
	private int maxNum;

}