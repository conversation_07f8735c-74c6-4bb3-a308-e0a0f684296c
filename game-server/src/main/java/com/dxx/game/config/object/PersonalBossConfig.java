package com.dxx.game.config.object;

import java.util.concurrent.ConcurrentSkipListMap;
import com.dxx.game.common.config.game.GameConfiguration;
import com.dxx.game.common.config.game.annotation.Resource;
import lombok.Getter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import com.dxx.game.config.entity.personalboss.PersonalBossBoxEntity;
import com.dxx.game.config.entity.personalboss.PersonalBossInfoEntity;


@Slf4j
@Getter
@ToString
@Resource(name="PersonalBoss")
public class PersonalBossConfig extends GameConfiguration<PersonalBossConfig> {
	
    private ConcurrentSkipListMap<Integer, PersonalBossBoxEntity> personalBossBox = new ConcurrentSkipListMap<>();
    private ConcurrentSkipListMap<Integer, PersonalBossInfoEntity> personalBossInfo = new ConcurrentSkipListMap<>();



    public PersonalBossBoxEntity getPersonalBossBoxEntity(int id) {
		if (!personalBossBox.containsKey(id)) {
			log.error("PersonalBossBoxEntity config not found ----> id:{}", id);
		}
	    return personalBossBox.get(id);
	}

    public PersonalBossInfoEntity getPersonalBossInfoEntity(int id) {
		if (!personalBossInfo.containsKey(id)) {
			log.error("PersonalBossInfoEntity config not found ----> id:{}", id);
		}
	    return personalBossInfo.get(id);
	}


}
