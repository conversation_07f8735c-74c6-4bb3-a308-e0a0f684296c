package com.dxx.game.config.entity.guild;

import java.util.List;
import com.dxx.game.common.config.game.converter.*;
import com.dxx.game.common.config.game.annotation.FieldMeta;
import com.dxx.game.common.config.game.entity.BaseEntity;
import lombok.Getter;
import lombok.ToString;

@Getter
@ToString
public class GuildLanguageEntity extends BaseEntity {


	/**
	 * Id
	 */
	@FieldMeta(index = 0, converter = DefaultFieldConverter.class)
	private int ID;
	/**
	 * 备注
	 */
	@FieldMeta(index = 1, converter = DefaultFieldConverter.class)
	private String Notes;
	/**
	 * 语言
	 */
	@FieldMeta(index = 2, converter = DefaultFieldConverter.class)
	private String Language;
	/**
	 * 语言代码
	 */
	@FieldMeta(index = 3, converter = DefaultFieldConverter.class)
	private String Code;
	/**
	 * 翻译的语言代码
	 */
	@FieldMeta(index = 4, converter = DefaultFieldConverter.class)
	private String TranslateCode;

}