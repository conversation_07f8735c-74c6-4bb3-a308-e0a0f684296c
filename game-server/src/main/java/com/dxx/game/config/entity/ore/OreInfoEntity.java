package com.dxx.game.config.entity.ore;

import java.util.List;
import com.dxx.game.common.config.game.converter.*;
import com.dxx.game.common.config.game.annotation.FieldMeta;
import com.dxx.game.common.config.game.entity.BaseEntity;
import lombok.Getter;
import lombok.ToString;

@Getter
@ToString
public class OreInfoEntity extends BaseEntity {


	/**
	 * Id
	 */
	@FieldMeta(index = 0, converter = DefaultFieldConverter.class)
	private int ID;
	/**
	 * 解锁条件
	 */
	@FieldMeta(index = 1, converter = DefaultFieldConverter.class)
	private int OreUnlock;
	/**
	 * 矿点id
	 */
	@FieldMeta(index = 2, converter = ListIntConverter.class)
	private List<Integer> oreId;

}