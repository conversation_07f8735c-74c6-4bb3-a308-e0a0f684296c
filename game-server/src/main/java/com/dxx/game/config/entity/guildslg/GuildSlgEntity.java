package com.dxx.game.config.entity.guildslg;

import java.util.List;
import com.dxx.game.common.config.game.converter.*;
import com.dxx.game.common.config.game.annotation.FieldMeta;
import com.dxx.game.common.config.game.entity.BaseEntity;
import lombok.Getter;
import lombok.ToString;

@Getter
@ToString
public class GuildSlgEntity extends BaseEntity {


	/**
	 * 赛季
	 */
	@FieldMeta(index = 0, converter = DefaultFieldConverter.class)
	private int Id;
	/**
	 * 开启时间（天）（相对战服时间）
	 */
	@FieldMeta(index = 1, converter = DefaultFieldConverter.class)
	private int OpenTime;
	/**
	 * 报名时间(小时)
	 */
	@FieldMeta(index = 2, converter = DefaultFieldConverter.class)
	private int stage1;
	/**
	 * 分组时间（小时)
	 */
	@FieldMeta(index = 3, converter = DefaultFieldConverter.class)
	private int stage2;
	/**
	 * 战斗时间(小时)
	 */
	@FieldMeta(index = 4, converter = DefaultFieldConverter.class)
	private int stage3;
	/**
	 * 结算时间(小时)
	 */
	@FieldMeta(index = 5, converter = DefaultFieldConverter.class)
	private int stage4;
	/**
	 * gvg参与战区范围
	 */
	@FieldMeta(index = 6, converter = DefaultFieldConverter.class)
	private int warRange;

}