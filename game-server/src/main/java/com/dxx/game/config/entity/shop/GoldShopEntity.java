package com.dxx.game.config.entity.shop;

import java.util.List;
import com.dxx.game.common.config.game.converter.*;
import com.dxx.game.common.config.game.annotation.FieldMeta;
import com.dxx.game.common.config.game.entity.BaseEntity;
import lombok.Getter;
import lombok.ToString;

@Getter
@ToString
public class GoldShopEntity extends BaseEntity {


	/**
	 * id
	 */
	@FieldMeta(index = 0, converter = DefaultFieldConverter.class)
	private int ID;
	/**
	 * 道具类型
	 * 1金币 4物品
	 */
	@FieldMeta(index = 1, converter = DefaultFieldConverter.class)
	private int ProductType;
	/**
	 * 消耗数量
	 */
	@FieldMeta(index = 2, converter = DefaultFieldConverter.class)
	private float Price;
	/**
	 * 消耗类型 1钻石 2金币
	 */
	@FieldMeta(index = 3, converter = DefaultFieldConverter.class)
	private int type;
	/**
	 * 折扣
	 */
	@FieldMeta(index = 4, converter = DefaultFieldConverter.class)
	private float discount;
	/**
	 * 奖励
	 * (金币奖励用实际消耗来计算)
	 */
	@FieldMeta(index = 5, converter = MatrixIntegerConverter.class)
	private List<List<Integer>> award;
	/**
	 * 描述
	 */
	@FieldMeta(index = 6, converter = DefaultFieldConverter.class)
	private int describe;
	/**
	 * 名字
	 */
	@FieldMeta(index = 7, converter = DefaultFieldConverter.class)
	private int title;
	/**
	 * 折扣描述比例
	 */
	@FieldMeta(index = 8, converter = DefaultFieldConverter.class)
	private float discountDes;

}