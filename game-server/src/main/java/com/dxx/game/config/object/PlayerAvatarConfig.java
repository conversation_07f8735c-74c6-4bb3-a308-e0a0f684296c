package com.dxx.game.config.object;

import java.util.concurrent.ConcurrentSkipListMap;
import com.dxx.game.common.config.game.GameConfiguration;
import com.dxx.game.common.config.game.annotation.Resource;
import lombok.Getter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import com.dxx.game.config.entity.playeravatar.PlayerAvatarEntity;
import com.dxx.game.config.entity.playeravatar.PlayerNameEntity;


@Slf4j
@Getter
@ToString
@Resource(name="PlayerAvatar")
public class PlayerAvatarConfig extends GameConfiguration<PlayerAvatarConfig> {
	
    private ConcurrentSkipListMap<Integer, PlayerAvatarEntity> playerAvatar = new ConcurrentSkipListMap<>();
    private ConcurrentSkipListMap<Integer, PlayerNameEntity> playerName = new ConcurrentSkipListMap<>();



    public PlayerAvatarEntity getPlayerAvatarEntity(int id) {
		if (!playerAvatar.containsKey(id)) {
			log.error("PlayerAvatarEntity config not found ----> id:{}", id);
		}
	    return playerAvatar.get(id);
	}

    public PlayerNameEntity getPlayerNameEntity(int id) {
		if (!playerName.containsKey(id)) {
			log.error("PlayerNameEntity config not found ----> id:{}", id);
		}
	    return playerName.get(id);
	}


}
