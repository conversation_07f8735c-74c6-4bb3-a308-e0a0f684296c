package com.dxx.game.config.entity.crossarena;

import java.util.List;
import com.dxx.game.common.config.game.converter.*;
import com.dxx.game.common.config.game.annotation.FieldMeta;
import com.dxx.game.common.config.game.entity.BaseEntity;
import lombok.Getter;
import lombok.ToString;

@Getter
@ToString
public class CrossArenaRobotEntity extends BaseEntity {


	/**
	 * ID
	 */
	@FieldMeta(index = 0, converter = DefaultFieldConverter.class)
	private int ID;
	/**
	 * 英雄等级区间
	 */
	@FieldMeta(index = 1, converter = ListIntConverter.class)
	private List<Integer> HeroLv;
	/**
	 * 英雄星级区间
	 */
	@FieldMeta(index = 2, converter = ListIntConverter.class)
	private List<Integer> HeroQuality;
	/**
	 * 随机战斗力区间（显示）
	 */
	@FieldMeta(index = 3, converter = ListIntConverter.class)
	private List<Integer> PowerNum;

}