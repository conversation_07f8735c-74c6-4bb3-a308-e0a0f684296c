package com.dxx.game.config.entity.personalboss;

import java.util.List;
import com.dxx.game.common.config.game.converter.*;
import com.dxx.game.common.config.game.annotation.FieldMeta;
import com.dxx.game.common.config.game.entity.BaseEntity;
import lombok.Getter;
import lombok.ToString;

@Getter
@ToString
public class PersonalBossBoxEntity extends BaseEntity {


	/**
	 * Id
	 */
	@FieldMeta(index = 0, converter = DefaultFieldConverter.class)
	private int ID;
	/**
	 * 伤害数值
	 */
	@FieldMeta(index = 1, converter = DefaultFieldConverter.class)
	private long Damage;
	/**
	 * BossId
	 */
	@FieldMeta(index = 2, converter = DefaultFieldConverter.class)
	private int BossId;
	/**
	 * 宝箱奖励奖励
	 */
	@FieldMeta(index = 3, converter = MatrixIntegerConverter.class)
	private List<List<Integer>> Reward;
	/**
	 * 其他奖励
	 * 客户端不需要展示（必须填道具，不需要就填道具+数量0）
	 */
	@FieldMeta(index = 4, converter = MatrixIntegerConverter.class)
	private List<List<Integer>> OtherReward;

}