package com.dxx.game.config.entity.crossarena;

import java.util.List;
import com.dxx.game.common.config.game.converter.*;
import com.dxx.game.common.config.game.annotation.FieldMeta;
import com.dxx.game.common.config.game.entity.BaseEntity;
import lombok.Getter;
import lombok.ToString;

@Getter
@ToString
public class CrossArenaRobotHeroEntity extends BaseEntity {


	/**
	 * Id
	 */
	@FieldMeta(index = 0, converter = DefaultFieldConverter.class)
	private int Id;
	/**
	 * 关卡1点位怪物信息
	 * id
	 */
	@FieldMeta(index = 1, converter = DefaultFieldConverter.class)
	private int MemberData1;
	/**
	 * 关卡2点位怪物信息
	 * id
	 */
	@FieldMeta(index = 2, converter = DefaultFieldConverter.class)
	private int MemberData2;
	/**
	 * 关卡3点位怪物信息
	 * id
	 */
	@FieldMeta(index = 3, converter = DefaultFieldConverter.class)
	private int MemberData3;
	/**
	 * 关卡4点位怪物信息
	 * id
	 */
	@FieldMeta(index = 4, converter = DefaultFieldConverter.class)
	private int MemberData4;
	/**
	 * 关卡5点位怪物信息
	 * id
	 */
	@FieldMeta(index = 5, converter = DefaultFieldConverter.class)
	private int MemberData5;

}