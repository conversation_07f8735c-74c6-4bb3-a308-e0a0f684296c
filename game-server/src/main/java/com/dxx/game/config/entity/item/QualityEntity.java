package com.dxx.game.config.entity.item;

import java.util.List;
import com.dxx.game.common.config.game.converter.*;
import com.dxx.game.common.config.game.annotation.FieldMeta;
import com.dxx.game.common.config.game.entity.BaseEntity;
import lombok.Getter;
import lombok.ToString;

@Getter
@ToString
public class QualityEntity extends BaseEntity {


	/**
	 * 内容ID
	 */
	@FieldMeta(index = 0, converter = DefaultFieldConverter.class)
	private int id;
	/**
	 * 注释
	 */
	@FieldMeta(index = 1, converter = DefaultFieldConverter.class)
	private String notes;
	/**
	 * 底图切片名称
	 */
	@FieldMeta(index = 2, converter = DefaultFieldConverter.class)
	private String basebgName;
	/**
	 * 角标名称
	 */
	@FieldMeta(index = 3, converter = DefaultFieldConverter.class)
	private String bandName;
	/**
	 * 名称多语言
	 * （用于道具时）
	 */
	@FieldMeta(index = 4, converter = DefaultFieldConverter.class)
	private int nameID;
	/**
	 * 名称多语言
	 * （用于角色时）
	 */
	@FieldMeta(index = 5, converter = DefaultFieldConverter.class)
	private int nameID2;
	/**
	 * 统一品质颜色
	 */
	@FieldMeta(index = 6, converter = DefaultFieldConverter.class)
	private String colorNum;

}