package com.dxx.game.config.entity.personalboss;

import java.util.List;
import com.dxx.game.common.config.game.converter.*;
import com.dxx.game.common.config.game.annotation.FieldMeta;
import com.dxx.game.common.config.game.entity.BaseEntity;
import lombok.Getter;
import lombok.ToString;

@Getter
@ToString
public class PersonalBossInfoEntity extends BaseEntity {


	/**
	 * Id
	 */
	@FieldMeta(index = 0, converter = DefaultFieldConverter.class)
	private int ID;
	/**
	 * bossId配置
	 * 引用guildBoss表ID
	 */
	@FieldMeta(index = 1, converter = ListIntConverter.class)
	private List<Integer> BossId;

}