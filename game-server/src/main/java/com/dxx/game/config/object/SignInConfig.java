package com.dxx.game.config.object;

import java.util.concurrent.ConcurrentSkipListMap;
import com.dxx.game.common.config.game.GameConfiguration;
import com.dxx.game.common.config.game.annotation.Resource;
import lombok.Getter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import com.dxx.game.config.entity.signin.SignInEntity;


@Slf4j
@Getter
@ToString
@Resource(name="SignIn")
public class SignInConfig extends GameConfiguration<SignInConfig> {
	
    private ConcurrentSkipListMap<Integer, SignInEntity> signIn = new ConcurrentSkipListMap<>();



    public SignInEntity getSignInEntity(int id) {
		if (!signIn.containsKey(id)) {
			log.error("SignInEntity config not found ----> id:{}", id);
		}
	    return signIn.get(id);
	}


}
