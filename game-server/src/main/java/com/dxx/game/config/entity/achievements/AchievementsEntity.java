package com.dxx.game.config.entity.achievements;

import java.util.List;
import com.dxx.game.common.config.game.converter.*;
import com.dxx.game.common.config.game.annotation.FieldMeta;
import com.dxx.game.common.config.game.entity.BaseEntity;
import lombok.Getter;
import lombok.ToString;

@Getter
@ToString
public class AchievementsEntity extends BaseEntity {


	/**
	 * ID
	 * 新加后续成就不要打乱id,在后面成组添加即可
	 */
	@FieldMeta(index = 0, converter = DefaultFieldConverter.class)
	private int ID;
	/**
	 * 成就类型
	 * 
	 */
	@FieldMeta(index = 1, converter = DefaultFieldConverter.class)
	private int AchievementsType;
	/**
	 * 成就等级
	 * (同类型成就,先完成上一级成就才显示出下一级成绩.成就进度会记录到下一级.)
	 */
	@FieldMeta(index = 2, converter = DefaultFieldConverter.class)
	private int AchievementsLevel;
	/**
	 * 是否为累加条件
	 * 0,非累加
	 * 1,累加
	 */
	@FieldMeta(index = 3, converter = DefaultFieldConverter.class)
	private int AccumulationType;
	/**
	 * 成就要求
	 */
	@FieldMeta(index = 4, converter = DefaultFieldConverter.class)
	private int AchievementsNeed;
	/**
	 * 成就描述
	 */
	@FieldMeta(index = 5, converter = DefaultFieldConverter.class)
	private int AchievementsDescribe;
	/**
	 * 成就奖励
	 * 道具类型,道具id,道具数量|…
	 */
	@FieldMeta(index = 6, converter = MatrixIntegerConverter.class)
	private List<List<Integer>> Reward;
	/**
	 * Function中ID
	 * 0为默认解锁
	 * -1为不开放
	 */
	@FieldMeta(index = 7, converter = DefaultFieldConverter.class)
	private int UnlockNeed;

}