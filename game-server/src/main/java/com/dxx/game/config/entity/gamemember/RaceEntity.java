package com.dxx.game.config.entity.gamemember;

import java.util.List;
import com.dxx.game.common.config.game.converter.*;
import com.dxx.game.common.config.game.annotation.FieldMeta;
import com.dxx.game.common.config.game.entity.BaseEntity;
import lombok.Getter;
import lombok.ToString;

@Getter
@ToString
public class RaceEntity extends BaseEntity {


	/**
	 * 内容ID
	 */
	@FieldMeta(index = 0, converter = DefaultFieldConverter.class)
	private int id;
	/**
	 * 注释
	 */
	@FieldMeta(index = 1, converter = DefaultFieldConverter.class)
	private String notes;
	/**
	 * 克制种族
	 */
	@FieldMeta(index = 2, converter = DefaultFieldConverter.class)
	private int restrain;
	/**
	 * 种族背景图（英雄详细信息界面背景）
	 */
	@FieldMeta(index = 3, converter = DefaultFieldConverter.class)
	private int raceBGatlasID;
	/**
	 * 种族羁绊
	 */
	@FieldMeta(index = 4, converter = DefaultFieldConverter.class)
	private String raceFetter;
	/**
	 * 种族名称
	 */
	@FieldMeta(index = 5, converter = DefaultFieldConverter.class)
	private int title;
	/**
	 * 种族描述
	 */
	@FieldMeta(index = 6, converter = DefaultFieldConverter.class)
	private int racedes;
	/**
	 * 种族羁绊描述
	 */
	@FieldMeta(index = 7, converter = DefaultFieldConverter.class)
	private int racefetterdes;
	/**
	 * 显示顺序
	 * （在筛选和羁绊界面的显示顺序，填0表示不显示）
	 */
	@FieldMeta(index = 8, converter = DefaultFieldConverter.class)
	private int displayOrder;

}