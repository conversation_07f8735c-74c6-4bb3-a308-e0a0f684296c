package com.dxx.game.config.entity.guild;

import java.util.List;
import com.dxx.game.common.config.game.converter.*;
import com.dxx.game.common.config.game.annotation.FieldMeta;
import com.dxx.game.common.config.game.entity.BaseEntity;
import lombok.Getter;
import lombok.ToString;

@Getter
@ToString
public class GuildBossOpenEntity extends BaseEntity {


	/**
	 * Id
	 */
	@FieldMeta(index = 0, converter = DefaultFieldConverter.class)
	private int ID;
	/**
	 * 开启时间段
	 * 开始时间|结束时间
	 */
	@FieldMeta(index = 1, converter = ListIntConverter.class)
	private List<Integer> OpenTime;
	/**
	 * bossId配置
	 * 引用guildBoss表ID
	 */
	@FieldMeta(index = 2, converter = ListIntConverter.class)
	private List<Integer> BossId;
	/**
	 * 击杀boss宝箱奖励配置
	 * 引用guildBossBox表ID
	 */
	@FieldMeta(index = 3, converter = ListIntConverter.class)
	private List<Integer> KillBossBoxId;

}