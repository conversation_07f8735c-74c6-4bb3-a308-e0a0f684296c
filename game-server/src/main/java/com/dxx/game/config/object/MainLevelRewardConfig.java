package com.dxx.game.config.object;

import java.util.concurrent.ConcurrentSkipListMap;
import com.dxx.game.common.config.game.GameConfiguration;
import com.dxx.game.common.config.game.annotation.Resource;
import lombok.Getter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import com.dxx.game.config.entity.mainlevelreward.AFKrewardEntity;
import com.dxx.game.config.entity.mainlevelreward.MainLevelChestEntity;


@Slf4j
@Getter
@ToString
@Resource(name="MainLevelReward")
public class MainLevelRewardConfig extends GameConfiguration<MainLevelRewardConfig> {
	
    private ConcurrentSkipListMap<Integer, AFKrewardEntity> aFKreward = new ConcurrentSkipListMap<>();
    private ConcurrentSkipListMap<Integer, MainLevelChestEntity> mainLevelChest = new ConcurrentSkipListMap<>();



    public AFKrewardEntity getAFKrewardEntity(int id) {
		if (!aFKreward.containsKey(id)) {
			log.error("AFKrewardEntity config not found ----> id:{}", id);
		}
	    return aFKreward.get(id);
	}

    public MainLevelChestEntity getMainLevelChestEntity(int id) {
		if (!mainLevelChest.containsKey(id)) {
			log.error("MainLevelChestEntity config not found ----> id:{}", id);
		}
	    return mainLevelChest.get(id);
	}


}
