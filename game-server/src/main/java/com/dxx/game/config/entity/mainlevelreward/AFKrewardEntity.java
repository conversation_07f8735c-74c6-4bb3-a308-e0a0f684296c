package com.dxx.game.config.entity.mainlevelreward;

import java.util.List;
import com.dxx.game.common.config.game.converter.*;
import com.dxx.game.common.config.game.annotation.FieldMeta;
import com.dxx.game.common.config.game.entity.BaseEntity;
import lombok.Getter;
import lombok.ToString;

@Getter
@ToString
public class AFKrewardEntity extends BaseEntity {


	/**
	 * id
	 */
	@FieldMeta(index = 0, converter = DefaultFieldConverter.class)
	private int ID;
	/**
	 * 奖励等级
	 */
	@FieldMeta(index = 1, converter = DefaultFieldConverter.class)
	private int RewardLevel;
	/**
	 * 要求的战役章节
	 */
	@FieldMeta(index = 2, converter = DefaultFieldConverter.class)
	private int RequiredLevel;
	/**
	 * 挂机金币奖励
	 */
	@FieldMeta(index = 3, converter = DefaultFieldConverter.class)
	private int HangGold;
	/**
	 * 挂机粉尘奖励
	 */
	@FieldMeta(index = 4, converter = DefaultFieldConverter.class)
	private int HangDust;
	/**
	 * 挂机英雄经验奖励
	 */
	@FieldMeta(index = 5, converter = DefaultFieldConverter.class)
	private int HangHeroExp;
	/**
	 * 挂机钻石奖励
	 */
	@FieldMeta(index = 6, converter = DefaultFieldConverter.class)
	private int HangDiamond;
	/**
	 * 挂机奖品奖励
	 */
	@FieldMeta(index = 7, converter = DefaultFieldConverter.class)
	private int HangRewards;

}