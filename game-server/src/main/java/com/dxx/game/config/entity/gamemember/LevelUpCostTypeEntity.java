package com.dxx.game.config.entity.gamemember;

import java.util.List;
import com.dxx.game.common.config.game.converter.*;
import com.dxx.game.common.config.game.annotation.FieldMeta;
import com.dxx.game.common.config.game.entity.BaseEntity;
import lombok.Getter;
import lombok.ToString;

@Getter
@ToString
public class LevelUpCostTypeEntity extends BaseEntity {


	/**
	 * 内容ID
	 */
	@FieldMeta(index = 0, converter = DefaultFieldConverter.class)
	private int id;
	/**
	 * 注释
	 */
	@FieldMeta(index = 1, converter = DefaultFieldConverter.class)
	private String notes;
	/**
	 * 类型
	 */
	@FieldMeta(index = 2, converter = DefaultFieldConverter.class)
	private int type;
	/**
	 * 等级
	 */
	@FieldMeta(index = 3, converter = DefaultFieldConverter.class)
	private int level;
	/**
	 * 下一等级ID 满级为0
	 */
	@FieldMeta(index = 4, converter = DefaultFieldConverter.class)
	private int nextID;
	/**
	 * 升至下一级的消耗
	 */
	@FieldMeta(index = 5, converter = MatrixIntegerConverter.class)
	private List<List<Integer>> levelupCost;

}