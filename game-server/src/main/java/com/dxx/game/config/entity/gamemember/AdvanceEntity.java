package com.dxx.game.config.entity.gamemember;

import java.util.List;
import com.dxx.game.common.config.game.converter.*;
import com.dxx.game.common.config.game.annotation.FieldMeta;
import com.dxx.game.common.config.game.entity.BaseEntity;
import lombok.Getter;
import lombok.ToString;

@Getter
@ToString
public class AdvanceEntity extends BaseEntity {


	/**
	 * 内容ID
	 */
	@FieldMeta(index = 0, converter = DefaultFieldConverter.class)
	private int id;
	/**
	 * 注释
	 */
	@FieldMeta(index = 1, converter = DefaultFieldConverter.class)
	private String notes;
	/**
	 * 最大等级
	 */
	@FieldMeta(index = 2, converter = DefaultFieldConverter.class)
	private int maxLevel;
	/**
	 * 下一阶
	 */
	@FieldMeta(index = 3, converter = DefaultFieldConverter.class)
	private int nextID;
	/**
	 * 升至下一阶段消耗
	 */
	@FieldMeta(index = 4, converter = MatrixIntegerConverter.class)
	private List<List<Integer>> advanceUpCost;
	/**
	 * 当前阶段攻击累计提升百分比
	 */
	@FieldMeta(index = 5, converter = DefaultFieldConverter.class)
	private float AllAttack;
	/**
	 * 当前阶段血量累计提升百分比
	 */
	@FieldMeta(index = 6, converter = DefaultFieldConverter.class)
	private float AllHPMax;
	/**
	 * 当前阶段防御累计提升百分比
	 */
	@FieldMeta(index = 7, converter = DefaultFieldConverter.class)
	private float AllDefence;

}