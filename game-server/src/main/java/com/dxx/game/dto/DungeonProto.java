// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: dungeon.proto

package com.dxx.game.dto;

public final class DungeonProto {
  private DungeonProto() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface StartDungeonRequestOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Proto.Dungeon.StartDungeonRequest)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return Whether the commonParams field is set.
     */
    boolean hasCommonParams();
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return The commonParams.
     */
    com.dxx.game.dto.CommonProto.CommonParams getCommonParams();
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     */
    com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder();

    /**
     * <pre>
     *副本ID
     * </pre>
     *
     * <code>int32 dungeonId = 2;</code>
     * @return The dungeonId.
     */
    int getDungeonId();

    /**
     * <pre>
     *副本等级
     * </pre>
     *
     * <code>int32 levelId = 3;</code>
     * @return The levelId.
     */
    int getLevelId();

    /**
     * <pre>
     *0普通战斗1扫荡
     * </pre>
     *
     * <code>int32 optionType = 4;</code>
     * @return The optionType.
     */
    int getOptionType();
  }
  /**
   * <pre>
   *CMD PackageId=15001 副本战斗
   * </pre>
   *
   * Protobuf type {@code Proto.Dungeon.StartDungeonRequest}
   */
  public static final class StartDungeonRequest extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:Proto.Dungeon.StartDungeonRequest)
      StartDungeonRequestOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use StartDungeonRequest.newBuilder() to construct.
    private StartDungeonRequest(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private StartDungeonRequest() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new StartDungeonRequest();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private StartDungeonRequest(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.dxx.game.dto.CommonProto.CommonParams.Builder subBuilder = null;
              if (commonParams_ != null) {
                subBuilder = commonParams_.toBuilder();
              }
              commonParams_ = input.readMessage(com.dxx.game.dto.CommonProto.CommonParams.parser(), extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(commonParams_);
                commonParams_ = subBuilder.buildPartial();
              }

              break;
            }
            case 16: {

              dungeonId_ = input.readInt32();
              break;
            }
            case 24: {

              levelId_ = input.readInt32();
              break;
            }
            case 32: {

              optionType_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.DungeonProto.internal_static_Proto_Dungeon_StartDungeonRequest_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.DungeonProto.internal_static_Proto_Dungeon_StartDungeonRequest_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.DungeonProto.StartDungeonRequest.class, com.dxx.game.dto.DungeonProto.StartDungeonRequest.Builder.class);
    }

    public static final int COMMONPARAMS_FIELD_NUMBER = 1;
    private com.dxx.game.dto.CommonProto.CommonParams commonParams_;
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return Whether the commonParams field is set.
     */
    @java.lang.Override
    public boolean hasCommonParams() {
      return commonParams_ != null;
    }
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return The commonParams.
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonParams getCommonParams() {
      return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
    }
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder() {
      return getCommonParams();
    }

    public static final int DUNGEONID_FIELD_NUMBER = 2;
    private int dungeonId_;
    /**
     * <pre>
     *副本ID
     * </pre>
     *
     * <code>int32 dungeonId = 2;</code>
     * @return The dungeonId.
     */
    @java.lang.Override
    public int getDungeonId() {
      return dungeonId_;
    }

    public static final int LEVELID_FIELD_NUMBER = 3;
    private int levelId_;
    /**
     * <pre>
     *副本等级
     * </pre>
     *
     * <code>int32 levelId = 3;</code>
     * @return The levelId.
     */
    @java.lang.Override
    public int getLevelId() {
      return levelId_;
    }

    public static final int OPTIONTYPE_FIELD_NUMBER = 4;
    private int optionType_;
    /**
     * <pre>
     *0普通战斗1扫荡
     * </pre>
     *
     * <code>int32 optionType = 4;</code>
     * @return The optionType.
     */
    @java.lang.Override
    public int getOptionType() {
      return optionType_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (commonParams_ != null) {
        output.writeMessage(1, getCommonParams());
      }
      if (dungeonId_ != 0) {
        output.writeInt32(2, dungeonId_);
      }
      if (levelId_ != 0) {
        output.writeInt32(3, levelId_);
      }
      if (optionType_ != 0) {
        output.writeInt32(4, optionType_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (commonParams_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getCommonParams());
      }
      if (dungeonId_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, dungeonId_);
      }
      if (levelId_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, levelId_);
      }
      if (optionType_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(4, optionType_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.dxx.game.dto.DungeonProto.StartDungeonRequest)) {
        return super.equals(obj);
      }
      com.dxx.game.dto.DungeonProto.StartDungeonRequest other = (com.dxx.game.dto.DungeonProto.StartDungeonRequest) obj;

      if (hasCommonParams() != other.hasCommonParams()) return false;
      if (hasCommonParams()) {
        if (!getCommonParams()
            .equals(other.getCommonParams())) return false;
      }
      if (getDungeonId()
          != other.getDungeonId()) return false;
      if (getLevelId()
          != other.getLevelId()) return false;
      if (getOptionType()
          != other.getOptionType()) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasCommonParams()) {
        hash = (37 * hash) + COMMONPARAMS_FIELD_NUMBER;
        hash = (53 * hash) + getCommonParams().hashCode();
      }
      hash = (37 * hash) + DUNGEONID_FIELD_NUMBER;
      hash = (53 * hash) + getDungeonId();
      hash = (37 * hash) + LEVELID_FIELD_NUMBER;
      hash = (53 * hash) + getLevelId();
      hash = (37 * hash) + OPTIONTYPE_FIELD_NUMBER;
      hash = (53 * hash) + getOptionType();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.dxx.game.dto.DungeonProto.StartDungeonRequest parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.DungeonProto.StartDungeonRequest parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.DungeonProto.StartDungeonRequest parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.DungeonProto.StartDungeonRequest parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.DungeonProto.StartDungeonRequest parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.DungeonProto.StartDungeonRequest parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.DungeonProto.StartDungeonRequest parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.DungeonProto.StartDungeonRequest parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.DungeonProto.StartDungeonRequest parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.DungeonProto.StartDungeonRequest parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.DungeonProto.StartDungeonRequest parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.DungeonProto.StartDungeonRequest parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.dxx.game.dto.DungeonProto.StartDungeonRequest prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     *CMD PackageId=15001 副本战斗
     * </pre>
     *
     * Protobuf type {@code Proto.Dungeon.StartDungeonRequest}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Proto.Dungeon.StartDungeonRequest)
        com.dxx.game.dto.DungeonProto.StartDungeonRequestOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.dxx.game.dto.DungeonProto.internal_static_Proto_Dungeon_StartDungeonRequest_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.dxx.game.dto.DungeonProto.internal_static_Proto_Dungeon_StartDungeonRequest_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.dxx.game.dto.DungeonProto.StartDungeonRequest.class, com.dxx.game.dto.DungeonProto.StartDungeonRequest.Builder.class);
      }

      // Construct using com.dxx.game.dto.DungeonProto.StartDungeonRequest.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (commonParamsBuilder_ == null) {
          commonParams_ = null;
        } else {
          commonParams_ = null;
          commonParamsBuilder_ = null;
        }
        dungeonId_ = 0;

        levelId_ = 0;

        optionType_ = 0;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.dxx.game.dto.DungeonProto.internal_static_Proto_Dungeon_StartDungeonRequest_descriptor;
      }

      @java.lang.Override
      public com.dxx.game.dto.DungeonProto.StartDungeonRequest getDefaultInstanceForType() {
        return com.dxx.game.dto.DungeonProto.StartDungeonRequest.getDefaultInstance();
      }

      @java.lang.Override
      public com.dxx.game.dto.DungeonProto.StartDungeonRequest build() {
        com.dxx.game.dto.DungeonProto.StartDungeonRequest result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.dxx.game.dto.DungeonProto.StartDungeonRequest buildPartial() {
        com.dxx.game.dto.DungeonProto.StartDungeonRequest result = new com.dxx.game.dto.DungeonProto.StartDungeonRequest(this);
        if (commonParamsBuilder_ == null) {
          result.commonParams_ = commonParams_;
        } else {
          result.commonParams_ = commonParamsBuilder_.build();
        }
        result.dungeonId_ = dungeonId_;
        result.levelId_ = levelId_;
        result.optionType_ = optionType_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.dxx.game.dto.DungeonProto.StartDungeonRequest) {
          return mergeFrom((com.dxx.game.dto.DungeonProto.StartDungeonRequest)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.dxx.game.dto.DungeonProto.StartDungeonRequest other) {
        if (other == com.dxx.game.dto.DungeonProto.StartDungeonRequest.getDefaultInstance()) return this;
        if (other.hasCommonParams()) {
          mergeCommonParams(other.getCommonParams());
        }
        if (other.getDungeonId() != 0) {
          setDungeonId(other.getDungeonId());
        }
        if (other.getLevelId() != 0) {
          setLevelId(other.getLevelId());
        }
        if (other.getOptionType() != 0) {
          setOptionType(other.getOptionType());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.dxx.game.dto.DungeonProto.StartDungeonRequest parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.dxx.game.dto.DungeonProto.StartDungeonRequest) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private com.dxx.game.dto.CommonProto.CommonParams commonParams_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder> commonParamsBuilder_;
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       * @return Whether the commonParams field is set.
       */
      public boolean hasCommonParams() {
        return commonParamsBuilder_ != null || commonParams_ != null;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       * @return The commonParams.
       */
      public com.dxx.game.dto.CommonProto.CommonParams getCommonParams() {
        if (commonParamsBuilder_ == null) {
          return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
        } else {
          return commonParamsBuilder_.getMessage();
        }
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder setCommonParams(com.dxx.game.dto.CommonProto.CommonParams value) {
        if (commonParamsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          commonParams_ = value;
          onChanged();
        } else {
          commonParamsBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder setCommonParams(
          com.dxx.game.dto.CommonProto.CommonParams.Builder builderForValue) {
        if (commonParamsBuilder_ == null) {
          commonParams_ = builderForValue.build();
          onChanged();
        } else {
          commonParamsBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder mergeCommonParams(com.dxx.game.dto.CommonProto.CommonParams value) {
        if (commonParamsBuilder_ == null) {
          if (commonParams_ != null) {
            commonParams_ =
              com.dxx.game.dto.CommonProto.CommonParams.newBuilder(commonParams_).mergeFrom(value).buildPartial();
          } else {
            commonParams_ = value;
          }
          onChanged();
        } else {
          commonParamsBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder clearCommonParams() {
        if (commonParamsBuilder_ == null) {
          commonParams_ = null;
          onChanged();
        } else {
          commonParams_ = null;
          commonParamsBuilder_ = null;
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonParams.Builder getCommonParamsBuilder() {
        
        onChanged();
        return getCommonParamsFieldBuilder().getBuilder();
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder() {
        if (commonParamsBuilder_ != null) {
          return commonParamsBuilder_.getMessageOrBuilder();
        } else {
          return commonParams_ == null ?
              com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
        }
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder> 
          getCommonParamsFieldBuilder() {
        if (commonParamsBuilder_ == null) {
          commonParamsBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder>(
                  getCommonParams(),
                  getParentForChildren(),
                  isClean());
          commonParams_ = null;
        }
        return commonParamsBuilder_;
      }

      private int dungeonId_ ;
      /**
       * <pre>
       *副本ID
       * </pre>
       *
       * <code>int32 dungeonId = 2;</code>
       * @return The dungeonId.
       */
      @java.lang.Override
      public int getDungeonId() {
        return dungeonId_;
      }
      /**
       * <pre>
       *副本ID
       * </pre>
       *
       * <code>int32 dungeonId = 2;</code>
       * @param value The dungeonId to set.
       * @return This builder for chaining.
       */
      public Builder setDungeonId(int value) {
        
        dungeonId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *副本ID
       * </pre>
       *
       * <code>int32 dungeonId = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearDungeonId() {
        
        dungeonId_ = 0;
        onChanged();
        return this;
      }

      private int levelId_ ;
      /**
       * <pre>
       *副本等级
       * </pre>
       *
       * <code>int32 levelId = 3;</code>
       * @return The levelId.
       */
      @java.lang.Override
      public int getLevelId() {
        return levelId_;
      }
      /**
       * <pre>
       *副本等级
       * </pre>
       *
       * <code>int32 levelId = 3;</code>
       * @param value The levelId to set.
       * @return This builder for chaining.
       */
      public Builder setLevelId(int value) {
        
        levelId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *副本等级
       * </pre>
       *
       * <code>int32 levelId = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearLevelId() {
        
        levelId_ = 0;
        onChanged();
        return this;
      }

      private int optionType_ ;
      /**
       * <pre>
       *0普通战斗1扫荡
       * </pre>
       *
       * <code>int32 optionType = 4;</code>
       * @return The optionType.
       */
      @java.lang.Override
      public int getOptionType() {
        return optionType_;
      }
      /**
       * <pre>
       *0普通战斗1扫荡
       * </pre>
       *
       * <code>int32 optionType = 4;</code>
       * @param value The optionType to set.
       * @return This builder for chaining.
       */
      public Builder setOptionType(int value) {
        
        optionType_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *0普通战斗1扫荡
       * </pre>
       *
       * <code>int32 optionType = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearOptionType() {
        
        optionType_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:Proto.Dungeon.StartDungeonRequest)
    }

    // @@protoc_insertion_point(class_scope:Proto.Dungeon.StartDungeonRequest)
    private static final com.dxx.game.dto.DungeonProto.StartDungeonRequest DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.dxx.game.dto.DungeonProto.StartDungeonRequest();
    }

    public static com.dxx.game.dto.DungeonProto.StartDungeonRequest getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<StartDungeonRequest>
        PARSER = new com.google.protobuf.AbstractParser<StartDungeonRequest>() {
      @java.lang.Override
      public StartDungeonRequest parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new StartDungeonRequest(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<StartDungeonRequest> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<StartDungeonRequest> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.dxx.game.dto.DungeonProto.StartDungeonRequest getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface StartDungeonResponseOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Proto.Dungeon.StartDungeonResponse)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int32 code = 1;</code>
     * @return The code.
     */
    int getCode();

    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     * @return Whether the commonData field is set.
     */
    boolean hasCommonData();
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     * @return The commonData.
     */
    com.dxx.game.dto.CommonProto.CommonData getCommonData();
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     */
    com.dxx.game.dto.CommonProto.CommonDataOrBuilder getCommonDataOrBuilder();

    /**
     * <pre>
     *副本ID
     * </pre>
     *
     * <code>int32 dungeonId = 3;</code>
     * @return The dungeonId.
     */
    int getDungeonId();

    /**
     * <pre>
     *副本等级
     * </pre>
     *
     * <code>int32 levelId = 4;</code>
     * @return The levelId.
     */
    int getLevelId();

    /**
     * <pre>
     *0普通战斗1扫荡
     * </pre>
     *
     * <code>int32 optionType = 5;</code>
     * @return The optionType.
     */
    int getOptionType();

    /**
     * <code>.Proto.Common.DungeonInfo info = 6;</code>
     * @return Whether the info field is set.
     */
    boolean hasInfo();
    /**
     * <code>.Proto.Common.DungeonInfo info = 6;</code>
     * @return The info.
     */
    com.dxx.game.dto.CommonProto.DungeonInfo getInfo();
    /**
     * <code>.Proto.Common.DungeonInfo info = 6;</code>
     */
    com.dxx.game.dto.CommonProto.DungeonInfoOrBuilder getInfoOrBuilder();

    /**
     * <pre>
     * 战斗信息
     * </pre>
     *
     * <code>.Proto.Common.DungeonBattleReqDto battleReqInfo = 7;</code>
     * @return Whether the battleReqInfo field is set.
     */
    boolean hasBattleReqInfo();
    /**
     * <pre>
     * 战斗信息
     * </pre>
     *
     * <code>.Proto.Common.DungeonBattleReqDto battleReqInfo = 7;</code>
     * @return The battleReqInfo.
     */
    com.dxx.game.dto.CommonProto.DungeonBattleReqDto getBattleReqInfo();
    /**
     * <pre>
     * 战斗信息
     * </pre>
     *
     * <code>.Proto.Common.DungeonBattleReqDto battleReqInfo = 7;</code>
     */
    com.dxx.game.dto.CommonProto.DungeonBattleReqDtoOrBuilder getBattleReqInfoOrBuilder();

    /**
     * <pre>
     * 战斗信息
     * </pre>
     *
     * <code>.Proto.Common.DungeonBattleRespDto battleResqInfo = 8;</code>
     * @return Whether the battleResqInfo field is set.
     */
    boolean hasBattleResqInfo();
    /**
     * <pre>
     * 战斗信息
     * </pre>
     *
     * <code>.Proto.Common.DungeonBattleRespDto battleResqInfo = 8;</code>
     * @return The battleResqInfo.
     */
    com.dxx.game.dto.CommonProto.DungeonBattleRespDto getBattleResqInfo();
    /**
     * <pre>
     * 战斗信息
     * </pre>
     *
     * <code>.Proto.Common.DungeonBattleRespDto battleResqInfo = 8;</code>
     */
    com.dxx.game.dto.CommonProto.DungeonBattleRespDtoOrBuilder getBattleResqInfoOrBuilder();
  }
  /**
   * <pre>
   *CMD PackageId=15002
   * </pre>
   *
   * Protobuf type {@code Proto.Dungeon.StartDungeonResponse}
   */
  public static final class StartDungeonResponse extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:Proto.Dungeon.StartDungeonResponse)
      StartDungeonResponseOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use StartDungeonResponse.newBuilder() to construct.
    private StartDungeonResponse(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private StartDungeonResponse() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new StartDungeonResponse();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private StartDungeonResponse(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              code_ = input.readInt32();
              break;
            }
            case 18: {
              com.dxx.game.dto.CommonProto.CommonData.Builder subBuilder = null;
              if (commonData_ != null) {
                subBuilder = commonData_.toBuilder();
              }
              commonData_ = input.readMessage(com.dxx.game.dto.CommonProto.CommonData.parser(), extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(commonData_);
                commonData_ = subBuilder.buildPartial();
              }

              break;
            }
            case 24: {

              dungeonId_ = input.readInt32();
              break;
            }
            case 32: {

              levelId_ = input.readInt32();
              break;
            }
            case 40: {

              optionType_ = input.readInt32();
              break;
            }
            case 50: {
              com.dxx.game.dto.CommonProto.DungeonInfo.Builder subBuilder = null;
              if (info_ != null) {
                subBuilder = info_.toBuilder();
              }
              info_ = input.readMessage(com.dxx.game.dto.CommonProto.DungeonInfo.parser(), extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(info_);
                info_ = subBuilder.buildPartial();
              }

              break;
            }
            case 58: {
              com.dxx.game.dto.CommonProto.DungeonBattleReqDto.Builder subBuilder = null;
              if (battleReqInfo_ != null) {
                subBuilder = battleReqInfo_.toBuilder();
              }
              battleReqInfo_ = input.readMessage(com.dxx.game.dto.CommonProto.DungeonBattleReqDto.parser(), extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(battleReqInfo_);
                battleReqInfo_ = subBuilder.buildPartial();
              }

              break;
            }
            case 66: {
              com.dxx.game.dto.CommonProto.DungeonBattleRespDto.Builder subBuilder = null;
              if (battleResqInfo_ != null) {
                subBuilder = battleResqInfo_.toBuilder();
              }
              battleResqInfo_ = input.readMessage(com.dxx.game.dto.CommonProto.DungeonBattleRespDto.parser(), extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(battleResqInfo_);
                battleResqInfo_ = subBuilder.buildPartial();
              }

              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.DungeonProto.internal_static_Proto_Dungeon_StartDungeonResponse_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.DungeonProto.internal_static_Proto_Dungeon_StartDungeonResponse_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.DungeonProto.StartDungeonResponse.class, com.dxx.game.dto.DungeonProto.StartDungeonResponse.Builder.class);
    }

    public static final int CODE_FIELD_NUMBER = 1;
    private int code_;
    /**
     * <code>int32 code = 1;</code>
     * @return The code.
     */
    @java.lang.Override
    public int getCode() {
      return code_;
    }

    public static final int COMMONDATA_FIELD_NUMBER = 2;
    private com.dxx.game.dto.CommonProto.CommonData commonData_;
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     * @return Whether the commonData field is set.
     */
    @java.lang.Override
    public boolean hasCommonData() {
      return commonData_ != null;
    }
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     * @return The commonData.
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonData getCommonData() {
      return commonData_ == null ? com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance() : commonData_;
    }
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonDataOrBuilder getCommonDataOrBuilder() {
      return getCommonData();
    }

    public static final int DUNGEONID_FIELD_NUMBER = 3;
    private int dungeonId_;
    /**
     * <pre>
     *副本ID
     * </pre>
     *
     * <code>int32 dungeonId = 3;</code>
     * @return The dungeonId.
     */
    @java.lang.Override
    public int getDungeonId() {
      return dungeonId_;
    }

    public static final int LEVELID_FIELD_NUMBER = 4;
    private int levelId_;
    /**
     * <pre>
     *副本等级
     * </pre>
     *
     * <code>int32 levelId = 4;</code>
     * @return The levelId.
     */
    @java.lang.Override
    public int getLevelId() {
      return levelId_;
    }

    public static final int OPTIONTYPE_FIELD_NUMBER = 5;
    private int optionType_;
    /**
     * <pre>
     *0普通战斗1扫荡
     * </pre>
     *
     * <code>int32 optionType = 5;</code>
     * @return The optionType.
     */
    @java.lang.Override
    public int getOptionType() {
      return optionType_;
    }

    public static final int INFO_FIELD_NUMBER = 6;
    private com.dxx.game.dto.CommonProto.DungeonInfo info_;
    /**
     * <code>.Proto.Common.DungeonInfo info = 6;</code>
     * @return Whether the info field is set.
     */
    @java.lang.Override
    public boolean hasInfo() {
      return info_ != null;
    }
    /**
     * <code>.Proto.Common.DungeonInfo info = 6;</code>
     * @return The info.
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.DungeonInfo getInfo() {
      return info_ == null ? com.dxx.game.dto.CommonProto.DungeonInfo.getDefaultInstance() : info_;
    }
    /**
     * <code>.Proto.Common.DungeonInfo info = 6;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.DungeonInfoOrBuilder getInfoOrBuilder() {
      return getInfo();
    }

    public static final int BATTLEREQINFO_FIELD_NUMBER = 7;
    private com.dxx.game.dto.CommonProto.DungeonBattleReqDto battleReqInfo_;
    /**
     * <pre>
     * 战斗信息
     * </pre>
     *
     * <code>.Proto.Common.DungeonBattleReqDto battleReqInfo = 7;</code>
     * @return Whether the battleReqInfo field is set.
     */
    @java.lang.Override
    public boolean hasBattleReqInfo() {
      return battleReqInfo_ != null;
    }
    /**
     * <pre>
     * 战斗信息
     * </pre>
     *
     * <code>.Proto.Common.DungeonBattleReqDto battleReqInfo = 7;</code>
     * @return The battleReqInfo.
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.DungeonBattleReqDto getBattleReqInfo() {
      return battleReqInfo_ == null ? com.dxx.game.dto.CommonProto.DungeonBattleReqDto.getDefaultInstance() : battleReqInfo_;
    }
    /**
     * <pre>
     * 战斗信息
     * </pre>
     *
     * <code>.Proto.Common.DungeonBattleReqDto battleReqInfo = 7;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.DungeonBattleReqDtoOrBuilder getBattleReqInfoOrBuilder() {
      return getBattleReqInfo();
    }

    public static final int BATTLERESQINFO_FIELD_NUMBER = 8;
    private com.dxx.game.dto.CommonProto.DungeonBattleRespDto battleResqInfo_;
    /**
     * <pre>
     * 战斗信息
     * </pre>
     *
     * <code>.Proto.Common.DungeonBattleRespDto battleResqInfo = 8;</code>
     * @return Whether the battleResqInfo field is set.
     */
    @java.lang.Override
    public boolean hasBattleResqInfo() {
      return battleResqInfo_ != null;
    }
    /**
     * <pre>
     * 战斗信息
     * </pre>
     *
     * <code>.Proto.Common.DungeonBattleRespDto battleResqInfo = 8;</code>
     * @return The battleResqInfo.
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.DungeonBattleRespDto getBattleResqInfo() {
      return battleResqInfo_ == null ? com.dxx.game.dto.CommonProto.DungeonBattleRespDto.getDefaultInstance() : battleResqInfo_;
    }
    /**
     * <pre>
     * 战斗信息
     * </pre>
     *
     * <code>.Proto.Common.DungeonBattleRespDto battleResqInfo = 8;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.DungeonBattleRespDtoOrBuilder getBattleResqInfoOrBuilder() {
      return getBattleResqInfo();
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (code_ != 0) {
        output.writeInt32(1, code_);
      }
      if (commonData_ != null) {
        output.writeMessage(2, getCommonData());
      }
      if (dungeonId_ != 0) {
        output.writeInt32(3, dungeonId_);
      }
      if (levelId_ != 0) {
        output.writeInt32(4, levelId_);
      }
      if (optionType_ != 0) {
        output.writeInt32(5, optionType_);
      }
      if (info_ != null) {
        output.writeMessage(6, getInfo());
      }
      if (battleReqInfo_ != null) {
        output.writeMessage(7, getBattleReqInfo());
      }
      if (battleResqInfo_ != null) {
        output.writeMessage(8, getBattleResqInfo());
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (code_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, code_);
      }
      if (commonData_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, getCommonData());
      }
      if (dungeonId_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, dungeonId_);
      }
      if (levelId_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(4, levelId_);
      }
      if (optionType_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(5, optionType_);
      }
      if (info_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(6, getInfo());
      }
      if (battleReqInfo_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(7, getBattleReqInfo());
      }
      if (battleResqInfo_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(8, getBattleResqInfo());
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.dxx.game.dto.DungeonProto.StartDungeonResponse)) {
        return super.equals(obj);
      }
      com.dxx.game.dto.DungeonProto.StartDungeonResponse other = (com.dxx.game.dto.DungeonProto.StartDungeonResponse) obj;

      if (getCode()
          != other.getCode()) return false;
      if (hasCommonData() != other.hasCommonData()) return false;
      if (hasCommonData()) {
        if (!getCommonData()
            .equals(other.getCommonData())) return false;
      }
      if (getDungeonId()
          != other.getDungeonId()) return false;
      if (getLevelId()
          != other.getLevelId()) return false;
      if (getOptionType()
          != other.getOptionType()) return false;
      if (hasInfo() != other.hasInfo()) return false;
      if (hasInfo()) {
        if (!getInfo()
            .equals(other.getInfo())) return false;
      }
      if (hasBattleReqInfo() != other.hasBattleReqInfo()) return false;
      if (hasBattleReqInfo()) {
        if (!getBattleReqInfo()
            .equals(other.getBattleReqInfo())) return false;
      }
      if (hasBattleResqInfo() != other.hasBattleResqInfo()) return false;
      if (hasBattleResqInfo()) {
        if (!getBattleResqInfo()
            .equals(other.getBattleResqInfo())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + CODE_FIELD_NUMBER;
      hash = (53 * hash) + getCode();
      if (hasCommonData()) {
        hash = (37 * hash) + COMMONDATA_FIELD_NUMBER;
        hash = (53 * hash) + getCommonData().hashCode();
      }
      hash = (37 * hash) + DUNGEONID_FIELD_NUMBER;
      hash = (53 * hash) + getDungeonId();
      hash = (37 * hash) + LEVELID_FIELD_NUMBER;
      hash = (53 * hash) + getLevelId();
      hash = (37 * hash) + OPTIONTYPE_FIELD_NUMBER;
      hash = (53 * hash) + getOptionType();
      if (hasInfo()) {
        hash = (37 * hash) + INFO_FIELD_NUMBER;
        hash = (53 * hash) + getInfo().hashCode();
      }
      if (hasBattleReqInfo()) {
        hash = (37 * hash) + BATTLEREQINFO_FIELD_NUMBER;
        hash = (53 * hash) + getBattleReqInfo().hashCode();
      }
      if (hasBattleResqInfo()) {
        hash = (37 * hash) + BATTLERESQINFO_FIELD_NUMBER;
        hash = (53 * hash) + getBattleResqInfo().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.dxx.game.dto.DungeonProto.StartDungeonResponse parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.DungeonProto.StartDungeonResponse parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.DungeonProto.StartDungeonResponse parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.DungeonProto.StartDungeonResponse parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.DungeonProto.StartDungeonResponse parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.DungeonProto.StartDungeonResponse parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.DungeonProto.StartDungeonResponse parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.DungeonProto.StartDungeonResponse parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.DungeonProto.StartDungeonResponse parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.DungeonProto.StartDungeonResponse parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.DungeonProto.StartDungeonResponse parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.DungeonProto.StartDungeonResponse parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.dxx.game.dto.DungeonProto.StartDungeonResponse prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     *CMD PackageId=15002
     * </pre>
     *
     * Protobuf type {@code Proto.Dungeon.StartDungeonResponse}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Proto.Dungeon.StartDungeonResponse)
        com.dxx.game.dto.DungeonProto.StartDungeonResponseOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.dxx.game.dto.DungeonProto.internal_static_Proto_Dungeon_StartDungeonResponse_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.dxx.game.dto.DungeonProto.internal_static_Proto_Dungeon_StartDungeonResponse_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.dxx.game.dto.DungeonProto.StartDungeonResponse.class, com.dxx.game.dto.DungeonProto.StartDungeonResponse.Builder.class);
      }

      // Construct using com.dxx.game.dto.DungeonProto.StartDungeonResponse.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        code_ = 0;

        if (commonDataBuilder_ == null) {
          commonData_ = null;
        } else {
          commonData_ = null;
          commonDataBuilder_ = null;
        }
        dungeonId_ = 0;

        levelId_ = 0;

        optionType_ = 0;

        if (infoBuilder_ == null) {
          info_ = null;
        } else {
          info_ = null;
          infoBuilder_ = null;
        }
        if (battleReqInfoBuilder_ == null) {
          battleReqInfo_ = null;
        } else {
          battleReqInfo_ = null;
          battleReqInfoBuilder_ = null;
        }
        if (battleResqInfoBuilder_ == null) {
          battleResqInfo_ = null;
        } else {
          battleResqInfo_ = null;
          battleResqInfoBuilder_ = null;
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.dxx.game.dto.DungeonProto.internal_static_Proto_Dungeon_StartDungeonResponse_descriptor;
      }

      @java.lang.Override
      public com.dxx.game.dto.DungeonProto.StartDungeonResponse getDefaultInstanceForType() {
        return com.dxx.game.dto.DungeonProto.StartDungeonResponse.getDefaultInstance();
      }

      @java.lang.Override
      public com.dxx.game.dto.DungeonProto.StartDungeonResponse build() {
        com.dxx.game.dto.DungeonProto.StartDungeonResponse result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.dxx.game.dto.DungeonProto.StartDungeonResponse buildPartial() {
        com.dxx.game.dto.DungeonProto.StartDungeonResponse result = new com.dxx.game.dto.DungeonProto.StartDungeonResponse(this);
        result.code_ = code_;
        if (commonDataBuilder_ == null) {
          result.commonData_ = commonData_;
        } else {
          result.commonData_ = commonDataBuilder_.build();
        }
        result.dungeonId_ = dungeonId_;
        result.levelId_ = levelId_;
        result.optionType_ = optionType_;
        if (infoBuilder_ == null) {
          result.info_ = info_;
        } else {
          result.info_ = infoBuilder_.build();
        }
        if (battleReqInfoBuilder_ == null) {
          result.battleReqInfo_ = battleReqInfo_;
        } else {
          result.battleReqInfo_ = battleReqInfoBuilder_.build();
        }
        if (battleResqInfoBuilder_ == null) {
          result.battleResqInfo_ = battleResqInfo_;
        } else {
          result.battleResqInfo_ = battleResqInfoBuilder_.build();
        }
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.dxx.game.dto.DungeonProto.StartDungeonResponse) {
          return mergeFrom((com.dxx.game.dto.DungeonProto.StartDungeonResponse)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.dxx.game.dto.DungeonProto.StartDungeonResponse other) {
        if (other == com.dxx.game.dto.DungeonProto.StartDungeonResponse.getDefaultInstance()) return this;
        if (other.getCode() != 0) {
          setCode(other.getCode());
        }
        if (other.hasCommonData()) {
          mergeCommonData(other.getCommonData());
        }
        if (other.getDungeonId() != 0) {
          setDungeonId(other.getDungeonId());
        }
        if (other.getLevelId() != 0) {
          setLevelId(other.getLevelId());
        }
        if (other.getOptionType() != 0) {
          setOptionType(other.getOptionType());
        }
        if (other.hasInfo()) {
          mergeInfo(other.getInfo());
        }
        if (other.hasBattleReqInfo()) {
          mergeBattleReqInfo(other.getBattleReqInfo());
        }
        if (other.hasBattleResqInfo()) {
          mergeBattleResqInfo(other.getBattleResqInfo());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.dxx.game.dto.DungeonProto.StartDungeonResponse parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.dxx.game.dto.DungeonProto.StartDungeonResponse) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private int code_ ;
      /**
       * <code>int32 code = 1;</code>
       * @return The code.
       */
      @java.lang.Override
      public int getCode() {
        return code_;
      }
      /**
       * <code>int32 code = 1;</code>
       * @param value The code to set.
       * @return This builder for chaining.
       */
      public Builder setCode(int value) {
        
        code_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 code = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearCode() {
        
        code_ = 0;
        onChanged();
        return this;
      }

      private com.dxx.game.dto.CommonProto.CommonData commonData_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.CommonProto.CommonData, com.dxx.game.dto.CommonProto.CommonData.Builder, com.dxx.game.dto.CommonProto.CommonDataOrBuilder> commonDataBuilder_;
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       * @return Whether the commonData field is set.
       */
      public boolean hasCommonData() {
        return commonDataBuilder_ != null || commonData_ != null;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       * @return The commonData.
       */
      public com.dxx.game.dto.CommonProto.CommonData getCommonData() {
        if (commonDataBuilder_ == null) {
          return commonData_ == null ? com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance() : commonData_;
        } else {
          return commonDataBuilder_.getMessage();
        }
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public Builder setCommonData(com.dxx.game.dto.CommonProto.CommonData value) {
        if (commonDataBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          commonData_ = value;
          onChanged();
        } else {
          commonDataBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public Builder setCommonData(
          com.dxx.game.dto.CommonProto.CommonData.Builder builderForValue) {
        if (commonDataBuilder_ == null) {
          commonData_ = builderForValue.build();
          onChanged();
        } else {
          commonDataBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public Builder mergeCommonData(com.dxx.game.dto.CommonProto.CommonData value) {
        if (commonDataBuilder_ == null) {
          if (commonData_ != null) {
            commonData_ =
              com.dxx.game.dto.CommonProto.CommonData.newBuilder(commonData_).mergeFrom(value).buildPartial();
          } else {
            commonData_ = value;
          }
          onChanged();
        } else {
          commonDataBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public Builder clearCommonData() {
        if (commonDataBuilder_ == null) {
          commonData_ = null;
          onChanged();
        } else {
          commonData_ = null;
          commonDataBuilder_ = null;
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonData.Builder getCommonDataBuilder() {
        
        onChanged();
        return getCommonDataFieldBuilder().getBuilder();
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonDataOrBuilder getCommonDataOrBuilder() {
        if (commonDataBuilder_ != null) {
          return commonDataBuilder_.getMessageOrBuilder();
        } else {
          return commonData_ == null ?
              com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance() : commonData_;
        }
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.CommonProto.CommonData, com.dxx.game.dto.CommonProto.CommonData.Builder, com.dxx.game.dto.CommonProto.CommonDataOrBuilder> 
          getCommonDataFieldBuilder() {
        if (commonDataBuilder_ == null) {
          commonDataBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.dxx.game.dto.CommonProto.CommonData, com.dxx.game.dto.CommonProto.CommonData.Builder, com.dxx.game.dto.CommonProto.CommonDataOrBuilder>(
                  getCommonData(),
                  getParentForChildren(),
                  isClean());
          commonData_ = null;
        }
        return commonDataBuilder_;
      }

      private int dungeonId_ ;
      /**
       * <pre>
       *副本ID
       * </pre>
       *
       * <code>int32 dungeonId = 3;</code>
       * @return The dungeonId.
       */
      @java.lang.Override
      public int getDungeonId() {
        return dungeonId_;
      }
      /**
       * <pre>
       *副本ID
       * </pre>
       *
       * <code>int32 dungeonId = 3;</code>
       * @param value The dungeonId to set.
       * @return This builder for chaining.
       */
      public Builder setDungeonId(int value) {
        
        dungeonId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *副本ID
       * </pre>
       *
       * <code>int32 dungeonId = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearDungeonId() {
        
        dungeonId_ = 0;
        onChanged();
        return this;
      }

      private int levelId_ ;
      /**
       * <pre>
       *副本等级
       * </pre>
       *
       * <code>int32 levelId = 4;</code>
       * @return The levelId.
       */
      @java.lang.Override
      public int getLevelId() {
        return levelId_;
      }
      /**
       * <pre>
       *副本等级
       * </pre>
       *
       * <code>int32 levelId = 4;</code>
       * @param value The levelId to set.
       * @return This builder for chaining.
       */
      public Builder setLevelId(int value) {
        
        levelId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *副本等级
       * </pre>
       *
       * <code>int32 levelId = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearLevelId() {
        
        levelId_ = 0;
        onChanged();
        return this;
      }

      private int optionType_ ;
      /**
       * <pre>
       *0普通战斗1扫荡
       * </pre>
       *
       * <code>int32 optionType = 5;</code>
       * @return The optionType.
       */
      @java.lang.Override
      public int getOptionType() {
        return optionType_;
      }
      /**
       * <pre>
       *0普通战斗1扫荡
       * </pre>
       *
       * <code>int32 optionType = 5;</code>
       * @param value The optionType to set.
       * @return This builder for chaining.
       */
      public Builder setOptionType(int value) {
        
        optionType_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *0普通战斗1扫荡
       * </pre>
       *
       * <code>int32 optionType = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearOptionType() {
        
        optionType_ = 0;
        onChanged();
        return this;
      }

      private com.dxx.game.dto.CommonProto.DungeonInfo info_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.CommonProto.DungeonInfo, com.dxx.game.dto.CommonProto.DungeonInfo.Builder, com.dxx.game.dto.CommonProto.DungeonInfoOrBuilder> infoBuilder_;
      /**
       * <code>.Proto.Common.DungeonInfo info = 6;</code>
       * @return Whether the info field is set.
       */
      public boolean hasInfo() {
        return infoBuilder_ != null || info_ != null;
      }
      /**
       * <code>.Proto.Common.DungeonInfo info = 6;</code>
       * @return The info.
       */
      public com.dxx.game.dto.CommonProto.DungeonInfo getInfo() {
        if (infoBuilder_ == null) {
          return info_ == null ? com.dxx.game.dto.CommonProto.DungeonInfo.getDefaultInstance() : info_;
        } else {
          return infoBuilder_.getMessage();
        }
      }
      /**
       * <code>.Proto.Common.DungeonInfo info = 6;</code>
       */
      public Builder setInfo(com.dxx.game.dto.CommonProto.DungeonInfo value) {
        if (infoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          info_ = value;
          onChanged();
        } else {
          infoBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <code>.Proto.Common.DungeonInfo info = 6;</code>
       */
      public Builder setInfo(
          com.dxx.game.dto.CommonProto.DungeonInfo.Builder builderForValue) {
        if (infoBuilder_ == null) {
          info_ = builderForValue.build();
          onChanged();
        } else {
          infoBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <code>.Proto.Common.DungeonInfo info = 6;</code>
       */
      public Builder mergeInfo(com.dxx.game.dto.CommonProto.DungeonInfo value) {
        if (infoBuilder_ == null) {
          if (info_ != null) {
            info_ =
              com.dxx.game.dto.CommonProto.DungeonInfo.newBuilder(info_).mergeFrom(value).buildPartial();
          } else {
            info_ = value;
          }
          onChanged();
        } else {
          infoBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <code>.Proto.Common.DungeonInfo info = 6;</code>
       */
      public Builder clearInfo() {
        if (infoBuilder_ == null) {
          info_ = null;
          onChanged();
        } else {
          info_ = null;
          infoBuilder_ = null;
        }

        return this;
      }
      /**
       * <code>.Proto.Common.DungeonInfo info = 6;</code>
       */
      public com.dxx.game.dto.CommonProto.DungeonInfo.Builder getInfoBuilder() {
        
        onChanged();
        return getInfoFieldBuilder().getBuilder();
      }
      /**
       * <code>.Proto.Common.DungeonInfo info = 6;</code>
       */
      public com.dxx.game.dto.CommonProto.DungeonInfoOrBuilder getInfoOrBuilder() {
        if (infoBuilder_ != null) {
          return infoBuilder_.getMessageOrBuilder();
        } else {
          return info_ == null ?
              com.dxx.game.dto.CommonProto.DungeonInfo.getDefaultInstance() : info_;
        }
      }
      /**
       * <code>.Proto.Common.DungeonInfo info = 6;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.CommonProto.DungeonInfo, com.dxx.game.dto.CommonProto.DungeonInfo.Builder, com.dxx.game.dto.CommonProto.DungeonInfoOrBuilder> 
          getInfoFieldBuilder() {
        if (infoBuilder_ == null) {
          infoBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.dxx.game.dto.CommonProto.DungeonInfo, com.dxx.game.dto.CommonProto.DungeonInfo.Builder, com.dxx.game.dto.CommonProto.DungeonInfoOrBuilder>(
                  getInfo(),
                  getParentForChildren(),
                  isClean());
          info_ = null;
        }
        return infoBuilder_;
      }

      private com.dxx.game.dto.CommonProto.DungeonBattleReqDto battleReqInfo_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.CommonProto.DungeonBattleReqDto, com.dxx.game.dto.CommonProto.DungeonBattleReqDto.Builder, com.dxx.game.dto.CommonProto.DungeonBattleReqDtoOrBuilder> battleReqInfoBuilder_;
      /**
       * <pre>
       * 战斗信息
       * </pre>
       *
       * <code>.Proto.Common.DungeonBattleReqDto battleReqInfo = 7;</code>
       * @return Whether the battleReqInfo field is set.
       */
      public boolean hasBattleReqInfo() {
        return battleReqInfoBuilder_ != null || battleReqInfo_ != null;
      }
      /**
       * <pre>
       * 战斗信息
       * </pre>
       *
       * <code>.Proto.Common.DungeonBattleReqDto battleReqInfo = 7;</code>
       * @return The battleReqInfo.
       */
      public com.dxx.game.dto.CommonProto.DungeonBattleReqDto getBattleReqInfo() {
        if (battleReqInfoBuilder_ == null) {
          return battleReqInfo_ == null ? com.dxx.game.dto.CommonProto.DungeonBattleReqDto.getDefaultInstance() : battleReqInfo_;
        } else {
          return battleReqInfoBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 战斗信息
       * </pre>
       *
       * <code>.Proto.Common.DungeonBattleReqDto battleReqInfo = 7;</code>
       */
      public Builder setBattleReqInfo(com.dxx.game.dto.CommonProto.DungeonBattleReqDto value) {
        if (battleReqInfoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          battleReqInfo_ = value;
          onChanged();
        } else {
          battleReqInfoBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <pre>
       * 战斗信息
       * </pre>
       *
       * <code>.Proto.Common.DungeonBattleReqDto battleReqInfo = 7;</code>
       */
      public Builder setBattleReqInfo(
          com.dxx.game.dto.CommonProto.DungeonBattleReqDto.Builder builderForValue) {
        if (battleReqInfoBuilder_ == null) {
          battleReqInfo_ = builderForValue.build();
          onChanged();
        } else {
          battleReqInfoBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <pre>
       * 战斗信息
       * </pre>
       *
       * <code>.Proto.Common.DungeonBattleReqDto battleReqInfo = 7;</code>
       */
      public Builder mergeBattleReqInfo(com.dxx.game.dto.CommonProto.DungeonBattleReqDto value) {
        if (battleReqInfoBuilder_ == null) {
          if (battleReqInfo_ != null) {
            battleReqInfo_ =
              com.dxx.game.dto.CommonProto.DungeonBattleReqDto.newBuilder(battleReqInfo_).mergeFrom(value).buildPartial();
          } else {
            battleReqInfo_ = value;
          }
          onChanged();
        } else {
          battleReqInfoBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <pre>
       * 战斗信息
       * </pre>
       *
       * <code>.Proto.Common.DungeonBattleReqDto battleReqInfo = 7;</code>
       */
      public Builder clearBattleReqInfo() {
        if (battleReqInfoBuilder_ == null) {
          battleReqInfo_ = null;
          onChanged();
        } else {
          battleReqInfo_ = null;
          battleReqInfoBuilder_ = null;
        }

        return this;
      }
      /**
       * <pre>
       * 战斗信息
       * </pre>
       *
       * <code>.Proto.Common.DungeonBattleReqDto battleReqInfo = 7;</code>
       */
      public com.dxx.game.dto.CommonProto.DungeonBattleReqDto.Builder getBattleReqInfoBuilder() {
        
        onChanged();
        return getBattleReqInfoFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 战斗信息
       * </pre>
       *
       * <code>.Proto.Common.DungeonBattleReqDto battleReqInfo = 7;</code>
       */
      public com.dxx.game.dto.CommonProto.DungeonBattleReqDtoOrBuilder getBattleReqInfoOrBuilder() {
        if (battleReqInfoBuilder_ != null) {
          return battleReqInfoBuilder_.getMessageOrBuilder();
        } else {
          return battleReqInfo_ == null ?
              com.dxx.game.dto.CommonProto.DungeonBattleReqDto.getDefaultInstance() : battleReqInfo_;
        }
      }
      /**
       * <pre>
       * 战斗信息
       * </pre>
       *
       * <code>.Proto.Common.DungeonBattleReqDto battleReqInfo = 7;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.CommonProto.DungeonBattleReqDto, com.dxx.game.dto.CommonProto.DungeonBattleReqDto.Builder, com.dxx.game.dto.CommonProto.DungeonBattleReqDtoOrBuilder> 
          getBattleReqInfoFieldBuilder() {
        if (battleReqInfoBuilder_ == null) {
          battleReqInfoBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.dxx.game.dto.CommonProto.DungeonBattleReqDto, com.dxx.game.dto.CommonProto.DungeonBattleReqDto.Builder, com.dxx.game.dto.CommonProto.DungeonBattleReqDtoOrBuilder>(
                  getBattleReqInfo(),
                  getParentForChildren(),
                  isClean());
          battleReqInfo_ = null;
        }
        return battleReqInfoBuilder_;
      }

      private com.dxx.game.dto.CommonProto.DungeonBattleRespDto battleResqInfo_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.CommonProto.DungeonBattleRespDto, com.dxx.game.dto.CommonProto.DungeonBattleRespDto.Builder, com.dxx.game.dto.CommonProto.DungeonBattleRespDtoOrBuilder> battleResqInfoBuilder_;
      /**
       * <pre>
       * 战斗信息
       * </pre>
       *
       * <code>.Proto.Common.DungeonBattleRespDto battleResqInfo = 8;</code>
       * @return Whether the battleResqInfo field is set.
       */
      public boolean hasBattleResqInfo() {
        return battleResqInfoBuilder_ != null || battleResqInfo_ != null;
      }
      /**
       * <pre>
       * 战斗信息
       * </pre>
       *
       * <code>.Proto.Common.DungeonBattleRespDto battleResqInfo = 8;</code>
       * @return The battleResqInfo.
       */
      public com.dxx.game.dto.CommonProto.DungeonBattleRespDto getBattleResqInfo() {
        if (battleResqInfoBuilder_ == null) {
          return battleResqInfo_ == null ? com.dxx.game.dto.CommonProto.DungeonBattleRespDto.getDefaultInstance() : battleResqInfo_;
        } else {
          return battleResqInfoBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 战斗信息
       * </pre>
       *
       * <code>.Proto.Common.DungeonBattleRespDto battleResqInfo = 8;</code>
       */
      public Builder setBattleResqInfo(com.dxx.game.dto.CommonProto.DungeonBattleRespDto value) {
        if (battleResqInfoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          battleResqInfo_ = value;
          onChanged();
        } else {
          battleResqInfoBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <pre>
       * 战斗信息
       * </pre>
       *
       * <code>.Proto.Common.DungeonBattleRespDto battleResqInfo = 8;</code>
       */
      public Builder setBattleResqInfo(
          com.dxx.game.dto.CommonProto.DungeonBattleRespDto.Builder builderForValue) {
        if (battleResqInfoBuilder_ == null) {
          battleResqInfo_ = builderForValue.build();
          onChanged();
        } else {
          battleResqInfoBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <pre>
       * 战斗信息
       * </pre>
       *
       * <code>.Proto.Common.DungeonBattleRespDto battleResqInfo = 8;</code>
       */
      public Builder mergeBattleResqInfo(com.dxx.game.dto.CommonProto.DungeonBattleRespDto value) {
        if (battleResqInfoBuilder_ == null) {
          if (battleResqInfo_ != null) {
            battleResqInfo_ =
              com.dxx.game.dto.CommonProto.DungeonBattleRespDto.newBuilder(battleResqInfo_).mergeFrom(value).buildPartial();
          } else {
            battleResqInfo_ = value;
          }
          onChanged();
        } else {
          battleResqInfoBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <pre>
       * 战斗信息
       * </pre>
       *
       * <code>.Proto.Common.DungeonBattleRespDto battleResqInfo = 8;</code>
       */
      public Builder clearBattleResqInfo() {
        if (battleResqInfoBuilder_ == null) {
          battleResqInfo_ = null;
          onChanged();
        } else {
          battleResqInfo_ = null;
          battleResqInfoBuilder_ = null;
        }

        return this;
      }
      /**
       * <pre>
       * 战斗信息
       * </pre>
       *
       * <code>.Proto.Common.DungeonBattleRespDto battleResqInfo = 8;</code>
       */
      public com.dxx.game.dto.CommonProto.DungeonBattleRespDto.Builder getBattleResqInfoBuilder() {
        
        onChanged();
        return getBattleResqInfoFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 战斗信息
       * </pre>
       *
       * <code>.Proto.Common.DungeonBattleRespDto battleResqInfo = 8;</code>
       */
      public com.dxx.game.dto.CommonProto.DungeonBattleRespDtoOrBuilder getBattleResqInfoOrBuilder() {
        if (battleResqInfoBuilder_ != null) {
          return battleResqInfoBuilder_.getMessageOrBuilder();
        } else {
          return battleResqInfo_ == null ?
              com.dxx.game.dto.CommonProto.DungeonBattleRespDto.getDefaultInstance() : battleResqInfo_;
        }
      }
      /**
       * <pre>
       * 战斗信息
       * </pre>
       *
       * <code>.Proto.Common.DungeonBattleRespDto battleResqInfo = 8;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.CommonProto.DungeonBattleRespDto, com.dxx.game.dto.CommonProto.DungeonBattleRespDto.Builder, com.dxx.game.dto.CommonProto.DungeonBattleRespDtoOrBuilder> 
          getBattleResqInfoFieldBuilder() {
        if (battleResqInfoBuilder_ == null) {
          battleResqInfoBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.dxx.game.dto.CommonProto.DungeonBattleRespDto, com.dxx.game.dto.CommonProto.DungeonBattleRespDto.Builder, com.dxx.game.dto.CommonProto.DungeonBattleRespDtoOrBuilder>(
                  getBattleResqInfo(),
                  getParentForChildren(),
                  isClean());
          battleResqInfo_ = null;
        }
        return battleResqInfoBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:Proto.Dungeon.StartDungeonResponse)
    }

    // @@protoc_insertion_point(class_scope:Proto.Dungeon.StartDungeonResponse)
    private static final com.dxx.game.dto.DungeonProto.StartDungeonResponse DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.dxx.game.dto.DungeonProto.StartDungeonResponse();
    }

    public static com.dxx.game.dto.DungeonProto.StartDungeonResponse getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<StartDungeonResponse>
        PARSER = new com.google.protobuf.AbstractParser<StartDungeonResponse>() {
      @java.lang.Override
      public StartDungeonResponse parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new StartDungeonResponse(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<StartDungeonResponse> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<StartDungeonResponse> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.dxx.game.dto.DungeonProto.StartDungeonResponse getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Dungeon_StartDungeonRequest_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_Proto_Dungeon_StartDungeonRequest_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Dungeon_StartDungeonResponse_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_Proto_Dungeon_StartDungeonResponse_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\rdungeon.proto\022\rProto.Dungeon\032\014common.p" +
      "roto\"\177\n\023StartDungeonRequest\0220\n\014commonPar" +
      "ams\030\001 \001(\0132\032.Proto.Common.CommonParams\022\021\n" +
      "\tdungeonId\030\002 \001(\005\022\017\n\007levelId\030\003 \001(\005\022\022\n\nopt" +
      "ionType\030\004 \001(\005\"\251\002\n\024StartDungeonResponse\022\014" +
      "\n\004code\030\001 \001(\005\022,\n\ncommonData\030\002 \001(\0132\030.Proto" +
      ".Common.CommonData\022\021\n\tdungeonId\030\003 \001(\005\022\017\n" +
      "\007levelId\030\004 \001(\005\022\022\n\noptionType\030\005 \001(\005\022\'\n\004in" +
      "fo\030\006 \001(\0132\031.Proto.Common.DungeonInfo\0228\n\rb" +
      "attleReqInfo\030\007 \001(\0132!.Proto.Common.Dungeo" +
      "nBattleReqDto\022:\n\016battleResqInfo\030\010 \001(\0132\"." +
      "Proto.Common.DungeonBattleRespDtoB \n\020com" +
      ".dxx.game.dtoB\014DungeonProtob\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.dxx.game.dto.CommonProto.getDescriptor(),
        });
    internal_static_Proto_Dungeon_StartDungeonRequest_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_Proto_Dungeon_StartDungeonRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_Proto_Dungeon_StartDungeonRequest_descriptor,
        new java.lang.String[] { "CommonParams", "DungeonId", "LevelId", "OptionType", });
    internal_static_Proto_Dungeon_StartDungeonResponse_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_Proto_Dungeon_StartDungeonResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_Proto_Dungeon_StartDungeonResponse_descriptor,
        new java.lang.String[] { "Code", "CommonData", "DungeonId", "LevelId", "OptionType", "Info", "BattleReqInfo", "BattleResqInfo", });
    com.dxx.game.dto.CommonProto.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
