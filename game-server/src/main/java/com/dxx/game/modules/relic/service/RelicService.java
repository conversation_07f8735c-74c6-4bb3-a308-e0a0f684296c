package com.dxx.game.modules.relic.service;

import com.dxx.game.common.server.model.Result;
import com.dxx.game.dao.dynamodb.model.UserExtend;
import com.dxx.game.dto.CommonProto;
import com.dxx.game.dto.RelicProto;

import java.util.List;

public interface RelicService {
    /**
     * 遗物激活
     */
    Result<RelicProto.RelicActiveResponse> relicActive(RelicProto.RelicActiveRequest params);

    /**
     * 遗物强化
     */
    Result<RelicProto.RelicStrengthResponse> relicStrength(RelicProto.RelicStrengthRequest params);

    /**
     * 遗物升星
     */
    Result<RelicProto.RelicStarResponse> relicStar(RelicProto.RelicStarRequest params);

    /**
     * 遗物特殊效果-额外增加快速挂机次数
     */
    int getRelicQuickHangupCount();

    /**
     * 遗物特殊效果-额外增加免费的竞技场门票
     */
    int getRelicArenaFreeCount();

    /**
     * 遗物特殊效果-额外增加挂机获得的金币奖励 (万分位)
     */
    int getRelicCoinsAdd();

    /**
     * 遗物特殊效果-额外增加挂机获得的英雄经验奖励 (万分位)
     */
    int getRelicHeroExpAdd();

    /**
     * 遗物特殊效果-额外增加挂机获得的粉尘奖励 (万分位)
     */
    int getRelicDustAdd();

    /**
     * 遗物特殊效果-悬赏栏任务数量
     */
    int getRelicDispatchCount();

    /**
     * 遗物特殊效果-公会首领挑战次数
     */
    int getRelicGuildBossCount();

    /**
     * 遗物特殊效果-解锁竞技场跳过战斗
     */
    int getUnlockArenaSkip();

    /**
     * 遗物特殊效果-解锁竞技场跳过战斗
     */
    int getUnlockDispatchUp();

    /**
     * 遗物特殊效果-解锁主线战斗4倍速
     */
    int getUnlockMain4Speed();

    /**
     * 遗物特殊效果-快速锻炼次数
     */
    int getSpeedTrain();

    UserExtend.RelicModel createRelic(UserExtend userExtend, int relicId);

    List<CommonProto.RelicDto> getRelicList(UserExtend userExtend);
}
