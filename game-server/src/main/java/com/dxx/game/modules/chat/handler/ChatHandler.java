package com.dxx.game.modules.chat.handler;

import com.dxx.game.common.server.annotation.ApiHandler;
import com.dxx.game.common.server.annotation.ApiMethod;
import com.dxx.game.common.server.model.Result;
import com.dxx.game.consts.MsgReqCommand;
import com.dxx.game.dto.ChatProto.*;
import com.dxx.game.modules.chat.service.ChatService;
import com.google.protobuf.Message;
import org.springframework.beans.factory.annotation.Autowired;

@ApiHandler
public class ChatHandler {
	
	@Autowired
	private ChatService chatService;

	@ApiMethod(command = MsgReqCommand.ChatTranslateRequest, name = "聊天-翻译")
	public Result<ChatTranslateResponse> translate(Message msg) {
		ChatTranslateRequest params = (ChatTranslateRequest)msg;
		return chatService.translateAction(params);
	}

	@ApiMethod(command = MsgReqCommand.ChatGuildShowItemRequest, name = "聊天-展示物品")
	public Result<ChatGuildShowItemResponse> chatGuildShowItem(Message msg) {
		ChatGuildShowItemRequest params = (ChatGuildShowItemRequest)msg;
		return chatService.chatGuildShowItemAction(params);
	}

	@ApiMethod(command = MsgReqCommand.ChatGetMessageRecordsRequest, name = "聊天-记录")
	public Result<ChatGetMessageRecordsResponse> chatRecord(Message msg) {
		ChatGetMessageRecordsRequest params = (ChatGetMessageRecordsRequest)msg;
		return chatService.chatRecord(params);
	}

	@ApiMethod(command = MsgReqCommand.ChatSetBlacklistRequest, name = "聊天-设置黑名单")
	public Result<ChatSetBlacklistResponse> chatSetBlacklist(Message msg) {
		ChatSetBlacklistRequest params = (ChatSetBlacklistRequest)msg;
		return chatService.chatSetBlacklist(params);
	}

	@ApiMethod(command = MsgReqCommand.ChatSetChapterLimitRequest, name = "聊天-设置最小章节")
	public Result<ChatSetChapterLimitResponse> chatSetChapterLimit(Message msg) {
		ChatSetChapterLimitRequest params = (ChatSetChapterLimitRequest)msg;
		return chatService.chatSetChapterLimit(params);
	}

	@ApiMethod(command = MsgReqCommand.ChatGetOnlineStatusRequest, name = "聊天-获取在线状态")
	public Result<ChatGetOnlineStatusResponse> getOnlineStatus(Message msg) {
		ChatGetOnlineStatusRequest params = (ChatGetOnlineStatusRequest)msg;
		return chatService.getOnlineStatus(params);
	}
}