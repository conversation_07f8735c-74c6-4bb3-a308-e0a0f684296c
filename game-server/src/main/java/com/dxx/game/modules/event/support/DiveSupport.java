package com.dxx.game.modules.event.support;

import com.amazonaws.xray.spring.aop.XRayEnabled;
import com.dxx.game.common.utils.CollectionUtils;
import com.dxx.game.common.utils.RandomUtil;
import com.dxx.game.config.GameConfigManager;
import com.dxx.game.config.entity.event.EventEntity;
import com.dxx.game.config.entity.eventdive.*;
import com.dxx.game.dao.dynamodb.model.event.Dive;
import com.dxx.game.dao.dynamodb.repository.ItemDao;
import com.dxx.game.dto.CommonProto;
import com.dxx.game.dto.DiveProto;
import com.dxx.game.modules.common.service.RandomService;
import com.dxx.game.modules.common.support.CommonHelper;
import com.dxx.game.modules.event.consts.EDiveGrid;
import com.dxx.game.modules.reward.service.DropService;
import com.google.common.collect.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.ImmutableTriple;
import org.apache.commons.lang3.tuple.Triple;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 潜水
 * <AUTHOR>
 * @date 2023/8/31 11:25
 */
@Component
@Slf4j
@XRayEnabled
public class DiveSupport {

    public static final int WIDTH = 6;

    public static final int SCREEN_LINE = 8;

    public static final int BUFF_LINE = 15 ;

    // 回调
    private static final int DYNAMIC_COMMON = 1;
    private static final int DYNAMIC_STRONG = 2;
    private static final int DYNAMIC_WEAK = 3;

    /** 水母道具  */
    public static final int SPECIAL_ID_DIVE_ITEM = 1;
    /** 手电筒道具 */
    public static final int SPECIAL_ID_DIVE_PROP_A = 2;
    /** 炸弹道具 */
    public static final int SPECIAL_ID_DIVE_PROP_B = 3;
    /** 连续奖励格 */
    public static final int SPECIAL_ID_CONTINUOUS_REWARDS = 4;
    /** 海藻格子 */
    public static final int SPECIAL_ID_VAREC = 5;
    /** 鲨鱼格子 */
    public static final int SPECIAL_ID_SHARK = 6;
    /** 贝壳A */
    public static final int SPECIAL_ID_SHELL_A = 7;
    /** 贝壳B */
    public static final int SPECIAL_ID_SHELL_B = 8;
    /** 特殊奖励1 */
    public static final int SPECIAL_SP_REWARD_1 = 9;

    public static final int SHINE_TYPE_PROP_A = 1;
    public static final int SHINE_TYPE_PROP_B = 2;
    /** 奖励格 */
    private static final List<Integer> specialIds = Lists.newArrayList(
            SPECIAL_ID_DIVE_ITEM,
            SPECIAL_ID_DIVE_PROP_A,
            SPECIAL_ID_DIVE_PROP_B,
            SPECIAL_ID_CONTINUOUS_REWARDS,
            SPECIAL_ID_VAREC,
            SPECIAL_ID_SHARK,
            SPECIAL_ID_SHELL_A,
            SPECIAL_ID_SHELL_B,
            SPECIAL_SP_REWARD_1);

    public static final int[] LEFT = {-1, 0};
    public static final int[] RIGHT = {1, 0};
    public static final int[] UP = {0, -1};
    public static final int[] DOWN = {0, 1};
    public static final int[] LEFT_UP = {-1, -1};
    public static final int[] RIGHT_UP = {1, -1};
    public static final int[] LEFT_DOWN = {-1, 1};
    public static final int[] RIGHT_DOWN = {1, 1};
    public static final int[][] DIRECTIONS = {LEFT, RIGHT, UP, DOWN, LEFT_UP, RIGHT_UP, LEFT_DOWN, RIGHT_DOWN};
    public static final int[][] LIGHT_DIRECTIONS = {UP, RIGHT, DOWN, LEFT};
    public static final int[][] BOMB_DIRECTIONS = {{0 ,0}, UP, RIGHT_UP, RIGHT, RIGHT_DOWN, DOWN, LEFT_DOWN, LEFT, LEFT_UP, {0, -2}, {2, 0}, {0, 2}, {-2, 0}};

    public static final Set<Integer> LightTypes = Sets.newHashSet(
            EDiveGrid.ePass.getType(),
            EDiveGrid.eContinuousRewards.getType(),
            EDiveGrid.eVarec.getType(),
            EDiveGrid.eVarecCenter.getType()
    );

    private static DiveSupport instance;

    @Autowired
    private GameConfigManager gameConfigManager;
    @Autowired
    private RandomService randomService;
    @Autowired
    private ItemDao itemDao;
    @Autowired
    private DropService dropService;

    @PostConstruct
    private void init() {
        instance = this;
    }

    /**
     * 初始化第一张地图
     */
    public void initStartMap(Dive model, DiveBaseEntity baseEntity) {
        // 初始地图配置
        DiveInitialGridEntity initialGridEntity = getInitialGridEntity();
        List<Integer> gridCfgList = initialGridEntity.getGrid();

        // 初始化格子
        initGridByCfg(model, gridCfgList, baseEntity, "init" + initialGridEntity.getID());
    }

    private void initGridByCfg(Dive model, List<Integer> cfgList, DiveBaseEntity baseEntity, String cfg) {
        if (cfgList == null || cfgList.isEmpty())
            return;

        List<Dive.DiveLine> lineList = model.getLineList();

        // 起始深度
        int curDepth = lineList.isEmpty() ? 0 : Iterables.getLast(lineList).getDepeth();

        // 泡泡不放回抽取
        List<List<Integer>> specialWeightPool = getSpecialWeightPool(model);

        // 格子配置
        List<List<Integer>> lineCfgList = CollectionUtils.splitList(cfgList, WIDTH);
        for (int i = 0; i < lineCfgList.size(); i++) {
            List<Integer> line = lineCfgList.get(i);

            curDepth++;

            Dive.DiveLine diveLine = new Dive.DiveLine();
            diveLine.setDepeth(curDepth);
            diveLine.setGridList(Lists.newArrayList());
//            diveLine.setCfg(cfg);
            lineList.add(diveLine);

            for (int j = 0; j < line.size(); j++) {
                int gridType = line.get(j);

                Dive.DiveGrid diveGrid = new Dive.DiveGrid();

                // 通路-1
                if (gridType == 0)
                    gridType = EDiveGrid.ePass.getType();

                diveGrid.setType(gridType);
//                diveGrid.setIdx(curDepth * WIDTH + j);

                Triple<Integer, Integer, Dive.DiveGrid> triple = ImmutableTriple.of(curDepth, j, diveGrid);

                if (gridType == EDiveGrid.ePop.getType()) {
                    randomPopReward(model, triple, specialWeightPool, baseEntity);
                } else if (gridType == EDiveGrid.eIce.getType()) {
                    diveGrid.setValue(2);
                }

                diveLine.getGridList().add(diveGrid);
            }
        }

        // 容错填充
        handelFill(model);

        // 海藻
        handleVarec(model);
    }

    private void handleVarec(Dive model) {
        DiveSpeicalEntity varecSpecialEntity = gameConfigManager.getEventDiveConfig().getDiveSpeicalEntity(SPECIAL_ID_VAREC);

        // 处理海藻
        for (int i = 0; i < model.getLineList().size(); i++) {
            Dive.DiveLine diveLine = model.getLineList().get(i);
            for (int idx = 0; idx < diveLine.getGridList().size(); idx++) {
                Dive.DiveGrid diveGrid = diveLine.getGridList().get(idx);
                if (diveGrid.getType() == EDiveGrid.eVarecCenter.getType()) {
                    if (!isValidVarecCenter(diveLine.getDepeth(), idx, model)) {
                        diveGrid.setType(EDiveGrid.ePop.getType());
                        diveGrid.setValue(null);
                        continue;
                    }

                    if (diveGrid.getStatus() != null)
                        continue;

                    List<int[]> direPool = Lists.newArrayList(DIRECTIONS);
                    Collections.shuffle(direPool);

                    int rewardCnt = 0;

                    for (int[] dir : direPool) {
                        int depth = diveLine.getDepeth() + dir[1];
                        int index = idx + dir[0];
                        Dive.DiveGrid varecGrid = getDiveGrid(model, depth, index);
                        varecGrid.setType(EDiveGrid.eVarec.getType());
                        varecGrid.setValue(null);
                        varecGrid.setReward(null);

                        if (rewardCnt < diveGrid.getValue()) {
                            List<Integer> reward = randomVarecReward(model, varecSpecialEntity);
                            varecGrid.setReward(reward);
                            rewardCnt++;
                        }
                    }

                    diveGrid.setStatus(1);
                }
            }
        }
    }

    private List<Integer> randomContinuousParam(Dive model, DiveSpeicalEntity varecSpecialEntity) {
        if (model.getContinuousPLog() == null) {
            model.setContinuousPLog(Maps.newHashMap());
        }

        return randomByLog(varecSpecialEntity.getParameter(), model.getContinuousPLog());
    }

    private List<Integer> randomVaracParam(Dive model, DiveSpeicalEntity varecSpecialEntity) {
        if (model.getVarecPLog() == null) {
            model.setVarecPLog(Maps.newHashMap());
        }

        return randomByLog(varecSpecialEntity.getParameter(), model.getVarecPLog());
    }

    private List<Integer> randomVarecReward(Dive model, DiveSpeicalEntity varecSpecialEntity) {
        if (model.getVarecLog() == null) {
            model.setVarecLog(Maps.newHashMap());
        }

        return randomByLog(varecSpecialEntity.getReward(), model.getVarecLog());
    }

    /**
     * 海藻中心必须不能靠边
     */
    private boolean isValidVarecCenter(int depth, int index, Dive model) {
        if (index <= 0 || index >= WIDTH - 1)
            return false;

        List<Dive.DiveLine> lineList = model.getLineList();

        int fistLineDepth = lineList.get(0).getDepeth();
        int lastLineDepth = lineList.get(lineList.size() - 1).getDepeth();

        if (depth <= fistLineDepth || depth >= lastLineDepth)
            return false;

        return true;
    }

    /**
     * 随机泡泡奖励
     */
    private void randomPopReward(Dive model, Triple<Integer, Integer, Dive.DiveGrid> triple, List<List<Integer>> specialWeightPool, DiveBaseEntity baseEntity) {
        Dive.DiveGrid diveGrid = triple.getRight();
        if (diveGrid.getType() != EDiveGrid.ePop.getType())
            return;
        if (specialWeightPool.isEmpty())
            return;
        if (!popRewardHit(model, baseEntity))
            return;

        int specialId = randomSpecialId(model, specialWeightPool);
        DiveSpeicalEntity speicalEntity = gameConfigManager.getEventDiveConfig().getDiveSpeicalEntity(specialId);

        switch (specialId) {
            case SPECIAL_ID_DIVE_ITEM:
                diveGrid.setType(EDiveGrid.eDiveItemPop.getType());
                break;
            case SPECIAL_ID_DIVE_PROP_A:
                diveGrid.setType(EDiveGrid.ePropAPop.getType());
                break;
            case SPECIAL_ID_DIVE_PROP_B:
                diveGrid.setType(EDiveGrid.ePropBPop.getType());
                break;
            case SPECIAL_ID_CONTINUOUS_REWARDS:
                diveGrid.setType(EDiveGrid.eContinuousRewards.getType());
                int cnt = randomContinuousParam(model, speicalEntity).get(0);
                diveGrid.setValue(cnt);
                break;
            case SPECIAL_ID_VAREC:
                // 检查海藻重叠
                if (checkVarecCover(model, triple)) {
                    return;
                }
                diveGrid.setType(EDiveGrid.eVarecCenter.getType());
                int varecRewardCnt = randomVaracParam(model, speicalEntity).get(0);
                diveGrid.setValue(varecRewardCnt);
                break;
            case SPECIAL_ID_SHARK:
                diveGrid.setShark(speicalEntity.getParameter().get(0).get(0).intValue());
                break;
            case SPECIAL_ID_SHELL_A:
                diveGrid.setType(EDiveGrid.eShellAPop.getType());
                break;
            case SPECIAL_ID_SHELL_B:
                diveGrid.setType(EDiveGrid.eShellBPop.getType());
                break;
            case SPECIAL_SP_REWARD_1:
                diveGrid.setType(EDiveGrid.eSPReward1Pop.getType());
                List<Integer> reward = randomService.randConfig(speicalEntity.getReward());
                int itemId = reward.get(0);
                int count = reward.get(1);
                diveGrid.setReward(Lists.newArrayList(itemId, count));
                break;
            default:
                break;
        }

    }

    private boolean checkVarecCover(Dive model, Triple<Integer, Integer, Dive.DiveGrid> triple) {
        Dive.DiveGrid diveGrid = triple.getRight();
//        int idx = diveGrid.getIdx();
//        int gridDepth = getDepth(idx);

        int gridDepth = triple.getLeft();

        // 上下两行不能存在海藻中心
        int[] checkDepth = new int[]{-2, -1 ,0, 1, 2};
        for (int addDepth : checkDepth) {
            int depth = gridDepth + addDepth;
            Dive.DiveLine diveLine = getDiveLine(model, depth);
            if (null == diveLine)
                continue;
            for (int index = 0; index < diveLine.getGridList().size(); index++) {
                Dive.DiveGrid checkGrid = diveLine.getGridList().get(index);
                if (checkGrid.getType() != EDiveGrid.eVarecCenter.getType())
                    continue;
                if (depth == gridDepth && index == triple.getMiddle())
                    continue;

                return true;
            }
        }

        return false;
    }

    public DiveProto.DiveGridDto buildDiveGridDto(Triple<Integer, Integer, Dive.DiveGrid> triple, DiveBaseEntity baseEntity) {
        Dive.DiveGrid diveGrid = triple.getRight();
        DiveProto.DiveGridDto.Builder builder = DiveProto.DiveGridDto.newBuilder();
        builder.setType(diveGrid.getType());

        EDiveGrid eDiveGrid = diveGrid.getEDiveGridType();

        if (diveGrid.getShark() != null)
            builder.setShark(diveGrid.getShark());
        if (diveGrid.getType() == EDiveGrid.eVarecCenter.getType()) {
            Integer varecRewardNum = diveGrid.getValue();
            if (varecRewardNum != null) {
                builder.setVarecRewardNum(varecRewardNum);
            }
        }
        if (diveGrid.getType() == EDiveGrid.eIce.getType()) {
            if (diveGrid.getValue() != null)
                builder.setIce(diveGrid.getValue());
        } else if (diveGrid.getType() == EDiveGrid.eVarec.getType()) {
            builder.setVarecClick(diveGrid.getValue() != null);
        } else if (diveGrid.getType() == EDiveGrid.eContinuousRewards.getType()) {
            builder.setRwardNum(diveGrid.getValue() == null ? 0 : diveGrid.getValue());
        }

        if (diveGrid.getReward() != null) {
            builder.setRewardDto(CommonHelper.buildRewardDto(diveGrid.getReward()));
        } else {
            List<Integer> reward = getSpecialItemReward(eDiveGrid, baseEntity);
            if (reward != null) {
                builder.setRewardDto(CommonHelper.buildRewardDto(reward));
            }
        }

        int depth = triple.getLeft();
        int index = triple.getMiddle();

        builder.setDepth(depth);
        builder.setIndex(index);

        return builder.build();
    }

    public List<Integer> getSpecialItemReward(EDiveGrid eDiveGrid, DiveBaseEntity baseEntity) {
        if (eDiveGrid == null)
            return null;

        int specialId = eDiveGrid.getSpeicalId();
        if (specialId <= 0)
            return null;

        DiveSpeicalEntity speicalEntity = gameConfigManager.getEventDiveConfig().getDiveSpeicalEntity(specialId);
        if (speicalEntity != null) {
            if (specialId == SPECIAL_ID_DIVE_ITEM) {
                return Lists.newArrayList(baseEntity.getDiveItem(), 1);
            } else if (specialId == SPECIAL_ID_DIVE_PROP_A) {
                return Lists.newArrayList(baseEntity.getDivePropA(), 1);
            } else if (specialId == SPECIAL_ID_DIVE_PROP_B) {
                return Lists.newArrayList(baseEntity.getDivePropB(), 1);
            } else if (specialId == SPECIAL_ID_SHELL_A) {
                return Lists.newArrayList(getShellAItem(baseEntity), 1);
            } else if (specialId == SPECIAL_ID_SHELL_B) {
                return Lists.newArrayList(getShellBItem(baseEntity), 1);
            }
        }

        return null;
    }

    public int getShellAItem(DiveBaseEntity baseEntity) {
        return baseEntity.getExchangeItem().get(0);
    }

    public int getShellBItem(DiveBaseEntity baseEntity) {
        return baseEntity.getExchangeItem().get(1);
    }

    public List<DiveProto.DiveLineDto> buildLineDtos(Dive model, DiveBaseEntity baseEntity) {
        List<DiveProto.DiveLineDto> result = Lists.newArrayList();

        DiveProto.DiveLineDto.Builder lineBuilder = DiveProto.DiveLineDto.newBuilder();
        DiveProto.DiveGridDto.Builder gridBuilder = DiveProto.DiveGridDto.newBuilder();

        for (Dive.DiveLine line : model.getLineList()) {
            int lineDepth = line.getDepeth();
            if (isScreenUpOut(model, lineDepth) || isScreenDownOut(model, lineDepth))
                continue;

            lineBuilder.clear();
            lineBuilder.setDepth(line.getDepeth());

            for (int index = 0; index < DiveSupport.WIDTH; index++) {
                Dive.DiveGrid grid = line.getGridList().get(index);
                gridBuilder.clear();

                EDiveGrid eDiveGrid = grid.getEDiveGridType();
                switch (eDiveGrid) {
                    case eDiveItemPop:
                        gridBuilder.setRewardDto(CommonHelper.buildRewardDto(Lists.newArrayList(baseEntity.getDiveItem(), 1)));
                        break;
                    case ePropAPop:
                        gridBuilder.setRewardDto(CommonHelper.buildRewardDto(Lists.newArrayList(baseEntity.getDivePropA(), 1)));
                        break;
                    case ePropBPop:
                        gridBuilder.setRewardDto(CommonHelper.buildRewardDto(Lists.newArrayList(baseEntity.getDivePropB(), 1)));
                        break;
                    case eShellAPop:
                        gridBuilder.setRewardDto(CommonHelper.buildRewardDto(Lists.newArrayList(baseEntity.getExchangeItem().get(0), 1)));
                        break;
                    case eShellBPop:
                        gridBuilder.setRewardDto(CommonHelper.buildRewardDto(Lists.newArrayList(baseEntity.getExchangeItem().get(1), 1)));
                        break;
                    case eSPReward1Pop:
                        gridBuilder.setRewardDto(CommonHelper.buildRewardDto(grid.getReward()));
                        break;
                }

                gridBuilder.setType(grid.getType());
                if (grid.getShark() != null && grid.getShark() > 0) {
                    gridBuilder.setShark(grid.getShark());
                }

                if (grid.getType() == EDiveGrid.eVarecCenter.getType()) {
                    Integer varecRewardCnt = grid.getValue();
                    if (varecRewardCnt != null && varecRewardCnt > 0)
                        gridBuilder.setVarecRewardNum(varecRewardCnt);
                }

                if (grid.getType() == EDiveGrid.eIce.getType()) {
                    if (grid.getValue() != null)
                        gridBuilder.setIce(grid.getValue());
                } else if (grid.getType() == EDiveGrid.eVarec.getType()) {
                    gridBuilder.setVarecClick(grid.getValue() != null);
                } else if (grid.getType() == EDiveGrid.eContinuousRewards.getType()) {
                    gridBuilder.setRwardNum(grid.getValue() == null ? 0 : grid.getValue());
                }

                gridBuilder.setDepth(line.getDepeth());
                gridBuilder.setIndex(index);

                boolean isLight = isLight(model, line.getDepeth(), index);
                gridBuilder.setLight(isLight ? 1 : 0);

                lineBuilder.addGridDtos(gridBuilder.build());
            }

            result.add(lineBuilder.build());
        }

        return result;
    }

    /**
     * 潜水配置信息
     * @param baseEntity
     * @return
     */
    public DiveProto.DiveConfigDto buildConfigDto(DiveBaseEntity baseEntity) {
        DiveProto.DiveConfigDto.Builder builder = DiveProto.DiveConfigDto.newBuilder();
        builder.setDiveItemId(baseEntity.getDiveItem());
        builder.setDivePrice(baseEntity.getDivePrice());
        builder.setDivePropA(baseEntity.getDivePropA());
        builder.setDivePropB(baseEntity.getDivePropB());
        builder.setDivePrice1(baseEntity.getDivePrice1());
        builder.setDivePrice2(baseEntity.getDivePrice2());
        builder.addAllExchangeItem(baseEntity.getExchangeItem());

        return builder.build();
    }

    /**
     * 个人累计奖励信息
     */
    public List<DiveProto.DiveAccRewardDto> buildUserAccRewardDtoList(Dive model, DiveBaseEntity baseEntity) {
        List<DiveProto.DiveAccRewardDto> result = Lists.newArrayList();

        DiveProto.DiveAccRewardDto.Builder builder = DiveProto.DiveAccRewardDto.newBuilder();
        for (List<Integer> list : baseEntity.getBigReward()) {
            builder.clear();
            int need = list.get(0);
            int dropId = list.get(1);

            List<List<Integer>> reward = dropService.dropRewardsConfig(dropId);
            List<CommonProto.RewardDto> rewardDtos = CommonHelper.buildRewardDtoList(reward);
            int state = model.getAcc().contains(need) ? 1 : (model.getDepth() >= need ? 2 : 0);

            builder.setNeed(need);
            builder.addAllRewardDto(rewardDtos);
            builder.setState(state);

            result.add(builder.build());
        }

        return result;
    }

    public DiveBaseEntity getDiveBaseEntity(int eventId) {
        EventEntity eventEntity = gameConfigManager.getEventConfig().getEventEntity(eventId);
        if(eventEntity == null) {
            log.error("cant find event config, eventId={}", eventId);
            return null;
        }

        int subType = eventEntity.getSubType1();
        return gameConfigManager.getEventDiveConfig().getDiveBaseEntity(subType);
    }

    public DiveInitialGridEntity getInitialGridEntity() {
        return Iterables.getFirst(gameConfigManager.getEventDiveConfig().getDiveInitialGrid().values(), null);
    }

    private int randomSpecialId(Dive model, List<List<Integer>> pool) {
        if (pool.isEmpty()) {
            pool.clear();
            pool.addAll(getSpecialWeightPool(model));
        }

        List<Integer> select = randomService.randConfig(pool);
        int specialId = select.get(0);

        model.getSpecialLog().put(specialId, model.getSpecialLog().getOrDefault(specialId, 0) + 1);

        return specialId;
    }

    /**
     * 泡泡奖励种类不放回抽取库
     */
    private List<List<Integer>> getSpecialWeightPool(Dive model) {
        List<List<Integer>> pool = Lists.newArrayList();

        for (int specialId : specialIds) {
            DiveSpeicalEntity specialEntity = gameConfigManager.getEventDiveConfig().getDiveSpeicalEntity(specialId);
            int logCount = model.getSpecialLog().getOrDefault(specialId, 0);
            int cfgCount = specialEntity.getNumber();
            int weight = Math.max(0, cfgCount - logCount);
            if (weight > 0) {
                pool.add(Lists.newArrayList(specialId, weight));
            }
        }

        // 重置
        if (pool.isEmpty()) {
            model.getSpecialLog().clear();
            for (int specialId : specialIds) {
                DiveSpeicalEntity specialEntity = gameConfigManager.getEventDiveConfig().getDiveSpeicalEntity(specialId);
                int cfgCount = specialEntity.getNumber();
                int weight = Math.max(0, cfgCount);
                if (weight > 0) {
                    pool.add(Lists.newArrayList(specialId, weight));
                }
            }
        }

        return pool;
    }

    private boolean isLight(int curDepth, int depth, int index, Dive model) {
        if (depth <= 1)
            return true;

        int screenFirstLineDepth = getScreenFirstLineDepth(curDepth);
        Dive.DiveGrid cur = getDiveGrid(model, depth, index);
        if (depth == screenFirstLineDepth && LightTypes.contains(cur.getType())) {
            return true;
        }

        // 上下左右必须有一个是通路
        for (int[] dire : LIGHT_DIRECTIONS) {
            int dirDepth = depth + dire[1];
            if (isScreenOut(curDepth, dirDepth))
                continue;
            int dirIndex = index + dire[0];
            Dive.DiveGrid grid = getDiveGrid(model, dirDepth, dirIndex);
            if (grid != null && LightTypes.contains(grid.getType())) {
                if (dirDepth == 1 || aroundPass(model, dirDepth, dirIndex, depth, index, curDepth)) {
                    return true;
                }
            }
        }

        return false;
    }

    public boolean isLight(Dive model, int depth, int index) {
        return isLight(model.getDepth(), depth, index, model);
    }

    private boolean aroundPass(Dive model, int depth, int index, int excludeDepth, int excludeIndex, int curDepth) {
        for (int[] dire : LIGHT_DIRECTIONS) {
            int dirDepth = depth + dire[1];
            if (isScreenOut(curDepth, dirDepth))
                continue;
            int dirIndex = index + dire[0];
//            if (dirDepth == excludeDepth && dirIndex == excludeIndex)
//                continue;
            Dive.DiveGrid grid = getDiveGrid(model, dirDepth, dirIndex);
            if (grid != null && LightTypes.contains(grid.getType())) {
                return true;
            }
        }

        return false;
    }

    public Dive.DiveGrid getDiveGrid(Dive model, int depth, int index) {
        Dive.DiveLine diveLine = getDiveLine(model, depth);
        if (null != diveLine && isValidIndex(index)) {
            return diveLine.getGridList().get(index);
        }

        return null;
    }

    public Dive.DiveLine getDiveLine(Dive model, int depth) {
        for (int i = 0; i < model.getLineList().size(); i++) {
            Dive.DiveLine line = model.getLineList().get(i);
            if (line.getDepeth() == depth)
                return line;
        }

        return null;
    }

    /**
     * 鲨鱼逃窜
     */
    public Triple<Integer, Integer, Dive.DiveGrid> findSharkGrid(Dive model, int depth, int index, Dive.DiveGrid diveGrid) {
        List<int[]> direPool = Lists.newArrayList(DIRECTIONS);
        Collections.shuffle(direPool);

        for (int[] dire : direPool) {
            int dirDepth = depth + dire[1];
            int dirIndex = index + dire[0];
            Dive.DiveGrid sharkGrid = getDiveGrid(model, dirDepth, dirIndex);
            if (sharkGrid.getType() == EDiveGrid.ePop.getType() || sharkGrid.getType() == EDiveGrid.eIce.getType()) {
                sharkGrid.setShark(diveGrid.getShark());
                return ImmutableTriple.of(dirDepth, dirIndex, sharkGrid);
            }
        }

        return null;
    }

    private List<Integer> randomByLog(List<List<Integer>> config, Map<Integer, Integer> log) {
        List<List<Integer>> pool = Lists.newArrayList();

        for (List<Integer> cfg : config) {
            int itemId = Iterables.getFirst(cfg, 0);
            int weight = Iterables.getLast(cfg);
            int logCount = log.getOrDefault(itemId, 0);
            weight = Math.max(0, weight - logCount);
            if (weight > 0) {
                List<Integer> newList = Lists.newArrayList(cfg);
                newList.set(newList.size() - 1, weight);
                pool.add(newList);
            }
        }

        if (pool.isEmpty()) {
            log.clear();
            for (List<Integer> cfg : config) {
                int weight = Iterables.getLast(cfg);
                if (weight > 0) {
                    pool.add(Lists.newArrayList(cfg));
                }
            }
        }

        List<Integer> select = randomService.randConfig(pool);
        int itemId = select.get(0);
        log.put(itemId, log.getOrDefault(itemId, 0) + 1);

        List<Integer> result = Lists.newArrayList(select);
        result.remove(select.size() - 1);

        return result;
    }

    /**
     * 随机连续奖励
     */
    public List<Integer> randomContinousReward(Dive model) {
        DiveSpeicalEntity speicalEntity = gameConfigManager.getEventDiveConfig().getDiveSpeicalEntity(SPECIAL_ID_CONTINUOUS_REWARDS);
        if (model.getContinuousLog() == null) {
            model.setContinuousLog(Maps.newHashMap());
        }

        return randomByLog(speicalEntity.getReward(), model.getContinuousLog());
    }

    /**
     * 向下填充行
     */
    public void buffLine(Dive model, DiveBaseEntity baseEntity) {
        if (!needBuffLine(model))
            return;

        // 插入行
        int insertCount = randomInsertCount(model, baseEntity);
        int insertDynamic = getDynamicByDepth(model);
        List<DiveInsertGridEntity> insertPool = getInsertGridPool(insertDynamic);
        for (int i = 0; i < insertCount; i++) {
            DiveInsertGridEntity insertGridEntity = RandomUtil.randomElement(insertPool);
            initGridByCfg(model, insertGridEntity.getGrid(), baseEntity, "i" + insertGridEntity.getID());
        }

        if (!needBuffLine(model))
            return;

        // 小块
        int tryCount = 0;
        while (needBuffLine(model) && tryCount < 5) {
            tryCount++;

            DiveSmallGridEntity smallGridEntity = randomSmallGrid(model);

            initGridByCfg(model, smallGridEntity.getGrid(), baseEntity, "s" + smallGridEntity.getID());
        }
    }

    private boolean needBuffLine(Dive model) {
        return model.getLineList().size() < BUFF_LINE;
    }

    public boolean handelFill(Dive model) {
        boolean dirty = false;
        for (Dive.DiveLine diveLine : model.getLineList()) {
            int gridSize = diveLine.getGridList().size();
            if (gridSize < WIDTH) {
                int fillCnt = WIDTH - gridSize;
                for (int i = 0; i < fillCnt; i++) {
                    Dive.DiveGrid grid = new Dive.DiveGrid();
                    grid.setType(EDiveGrid.ePop.getType());
                    diveLine.getGridList().add(grid);
                    dirty = true;
                }
            }

            // 整行箱子的情况
            boolean continueBox = true;
            for (Dive.DiveGrid diveGrid : diveLine.getGridList()) {
                if (diveGrid.getType() != EDiveGrid.eContinuousRewards.getType()) {
                    continueBox = false;
                    break;
                }
            }
            if (continueBox && !CollectionUtils.isNullOrEmpty(diveLine.getGridList())) {
                Dive.DiveGrid diveGrid = diveLine.getGridList().get(0);
                diveGrid.setType(EDiveGrid.ePop.getType());
                diveGrid.setValue(null);
                dirty = true;
            }
        }

        return dirty;
    }

    private List<DiveInsertGridEntity> getInsertGridPool(int type) {
        return gameConfigManager.getEventDiveConfig().getDiveInsertGrid().values().stream().filter(e -> e.getType() == type).collect(Collectors.toList());
    }

    private DiveDynamicEntity getDynamicEntityByConsume(Dive model) {
        // 累计消耗水母数量
        int consumeCnt = model.getConsumeCnt();

        Collection<DiveDynamicEntity> entities = gameConfigManager.getEventDiveConfig().getDiveDynamic().values();
        for (DiveDynamicEntity dynamicEntity : entities) {
            int min = dynamicEntity.getComsumRange().get(0);
            int max = dynamicEntity.getComsumRange().get(1);
            if (consumeCnt < min || consumeCnt > max)
                continue;

            return  dynamicEntity;
        }

        return Iterables.getLast(entities);
    }

    private int getDynamicByDepth(Dive model) {
        DiveDynamicEntity dynamicEntity = getDynamicEntityByConsume(model);
        // 累计深度
        int depth = model.getDepth();
        if (depth < dynamicEntity.getMinDepth()) {
            return DYNAMIC_STRONG; // 补强
        }

        if (depth > dynamicEntity.getMaxDepth()) {
            return DYNAMIC_WEAK;    // 补弱
        }

        return DYNAMIC_COMMON;
    }

    private int getDynamicByRewardCount(Dive model) {
        DiveDynamicEntity dynamicEntity = getDynamicEntityByConsume(model);
        // 累计获取泡泡奖励
        int rewardAcc = model.getRewardAcc();
        if (rewardAcc < dynamicEntity.getMinItem()) {
            return DYNAMIC_STRONG; // 补强
        }

        if (rewardAcc > dynamicEntity.getMaxItem()) {
            return DYNAMIC_WEAK;    // 补弱
        }

        return DYNAMIC_COMMON;
    }

    private boolean popRewardHit(Dive model, DiveBaseEntity baseEntity) {
        // 百分比
        int hit = 0;
        List<Integer> rewarProb = baseEntity.getRewardProb();
        int dynamic = getDynamicByRewardCount(model);
        switch (dynamic) {
            case DYNAMIC_COMMON:
                hit = rewarProb.get(0);
                break;
            case DYNAMIC_STRONG:
                hit = rewarProb.get(1);
                break;
            case DYNAMIC_WEAK:
                hit = rewarProb.get(2);
                break;
            default:
                break;
        }

        if (hit <= 0)
            return false;

        int selectProb = RandomUtil.nextInt(101);
        return selectProb <= hit;
    }

    private int randomInsertCount(Dive model, DiveBaseEntity baseEntity) {
        List<Integer> generateGap = null;
        int dynamic = getDynamicByDepth(model);
        switch (dynamic) {
            case DYNAMIC_COMMON:
                generateGap = baseEntity.getGenerateGap().get(0);
                break;
            case DYNAMIC_STRONG:
                generateGap = baseEntity.getGenerateGap().get(1);
                break;
            case DYNAMIC_WEAK:
                generateGap = baseEntity.getGenerateGap().get(2);
                break;
            default:
                break;
        }

        if (generateGap == null || generateGap.isEmpty() || generateGap.size() < 2) {
            return 0;
        }

        int select = RandomUtil.betweenValue(generateGap.get(0), generateGap.get(1));
        return select;
    }

    private DiveSmallGridEntity randomSmallGrid(Dive model) {
        List<DiveSmallGridEntity> entityList = gameConfigManager.getEventDiveConfig().getDiveSmallGrid().values().stream().collect(Collectors.toList());
        DiveSmallGridEntity smallGridEntity = RandomUtil.randomElement(entityList);
        return smallGridEntity;
    }

    public List<Triple<Integer, Integer, Dive.DiveGrid>> getShineGirdList(int shineType, Dive model, int depth, int index) {
        List<Triple<Integer, Integer, Dive.DiveGrid>> result = Lists.newArrayList();
        // 手电筒 竖行
        if (shineType == SHINE_TYPE_PROP_A) {
            // 屏幕第一行 从黄线开始算上6行
            int startLine = getScreenFirstLineDepth(model.getDepth());
            int endLine = model.getDepth() + 1;

            for (int i = startLine; i <= endLine; i++) {
                Dive.DiveLine diveLine = getDiveLine(model, i);
                if (null == diveLine)
                    continue;
                Dive.DiveGrid diveGrid = diveLine.getGridList().get(index);
                result.add(ImmutableTriple.of(diveLine.getDepeth(), index, diveGrid));
            }
        } else if (shineType == SHINE_TYPE_PROP_B) {
            // 炸弹
            for (int[] dire : BOMB_DIRECTIONS) {
                int bombDepth = depth + dire[1];
                int bombIndex = index + dire[0];
                Dive.DiveGrid diveGrid = getDiveGrid(model, bombDepth, bombIndex);
                if (diveGrid != null) {
                    result.add(ImmutableTriple.of(bombDepth, bombIndex, diveGrid));
                }
            }
        }

        return result;
    }

    public List<Triple<Integer, Integer, Dive.DiveGrid>> getAroundGrid(Dive model, int depth, int index) {
        List<Triple<Integer, Integer, Dive.DiveGrid>> result = Lists.newArrayList();
        for (int[] dire : DIRECTIONS) {
            int dirDepth = depth + dire[1];
            int dirIndex = index + dire[0];
            Dive.DiveGrid grid = getDiveGrid(model, dirDepth, dirIndex);
            if (grid != null) {
                result.add(ImmutableTriple.of(dirDepth, dirIndex, grid));
            }
        }

        return result;
    }

    public Dive.DiveLine findDiveDepth(Dive model) {
        int curDepth = model.getDepth();

        boolean diveFlag = false;

        Dive.DiveLine startLine = getDiveLine(model, curDepth + 1);
        if (isFullLine(startLine))
            return null;
        else {
            for (int index = 0; index < startLine.getGridList().size(); index++) {
                Dive.DiveGrid grid = startLine.getGridList().get(index);
                if (grid.getEDiveGridType().isPass() && isLight(model, startLine.getDepeth(), index)) {
                    diveFlag = true;
                    break;
                }
            }
        }

        if (!diveFlag) {
            return null;
        }

//        Dive.DiveLine endLine = Iterables.getLast(model.getLineList());

        int maxPassDepth = 0;

        /*
        for (int index = 0; index < WIDTH; index++) {
            int passDepth = 0;
            for (int startDepth = startLine.getDepeth(); startDepth <= endLine.getDepeth(); startDepth++) {
                Dive.DiveLine diveLine = getDiveLine(model, startDepth);
                Dive.DiveGrid diveGrid = diveLine.getGridList().get(index);
                if (!diveGrid.getEDiveGridType().isPass()) {
                    break;
                }

                passDepth = startDepth;
            }

            if (passDepth > maxPassDepth)
                maxPassDepth = passDepth;
        }
         */

        // 查询一行 满行或者空格子是暗的行
        for (int lineIndex = 0; lineIndex < model.getLineList().size(); lineIndex++) {
            Dive.DiveLine diveLine = model.getLineList().get(lineIndex);
            int lineDepth = diveLine.getDepeth();
            if (lineDepth <= startLine.getDepeth())
                continue;
            if (isFullLine(diveLine) && !hasVarac(diveLine)) {
                maxPassDepth = lineDepth;
                break;
            }

            boolean passAllDark = true;
            for (int index = 0; index < diveLine.getGridList().size(); index++) {
                Dive.DiveGrid diveGrid = diveLine.getGridList().get(index);
                if (diveGrid.getEDiveGridType().isPass()) {
                    if (isLight(lineDepth - 1, lineDepth, index, model)) {
                        passAllDark = false;
                        break;
                    }
                }
            }

            if (passAllDark) {
                maxPassDepth = lineDepth;
                break;
            }
        }

        if (maxPassDepth > curDepth) {
            Dive.DiveLine diveLine = getDiveLine(model, maxPassDepth - 1);
            if (isScreenOut(model, diveLine.getDepeth()) && diveLine.getDepeth() - curDepth > 4) {
                if (!isFullLine(diveLine) && !hasVarac(diveLine)) {
                    for (int index = 0; index < diveLine.getGridList().size(); index++) {
                        Dive.DiveGrid diveGrid = diveLine.getGridList().get(index);
                        if (diveGrid.getType() == EDiveGrid.ePass.getType()) {
                            diveGrid.setType(EDiveGrid.ePop.getType());
                        }
                    }
                }
            }
            return diveLine;
        }

        if (isFullLine(startLine)) {
            return null;
        } else {
            // 第四行填满
            Dive.DiveLine lastLine = Iterables.getLast(model.getLineList());
            for (int startDepth = startLine.getDepeth() + 4; startDepth <= lastLine.getDepeth(); startDepth++) {
                Dive.DiveLine diveLine = getDiveLine(model, startDepth);
                if (null == diveLine)
                    break;
                if (hasVarac(diveLine)) {
                    continue;
                }

                for (Dive.DiveGrid grid : diveLine.getGridList()) {
                    if (grid.getType() == EDiveGrid.ePass.getType()) {
                        grid.setType(EDiveGrid.ePop.getType());
                    }
                }

                return getDiveLine(model, diveLine.getDepeth() - 1);
            }

            for (int startDepth = startLine.getDepeth() + 1; startDepth < startLine.getDepeth() + 4; startDepth++) {
                Dive.DiveLine diveLine = getDiveLine(model, startDepth);
                if (null == diveLine)
                    break;
                if (hasVarac(diveLine)) {
                    continue;
                }

                for (Dive.DiveGrid grid : diveLine.getGridList()) {
                    if (grid.getType() == EDiveGrid.ePass.getType()) {
                        grid.setType(EDiveGrid.ePop.getType());
                    }
                }

                return getDiveLine(model, diveLine.getDepeth() - 1);
            }

            return startLine;
        }

//        return isFullLine(startLine) ? null : startLine;
    }

    private boolean hasVarac(Dive.DiveLine diveLine) {
        for (Dive.DiveGrid grid : diveLine.getGridList()) {
            if (grid.getType() == EDiveGrid.eVarec.getType() || grid.getType() == EDiveGrid.eVarecCenter.getType()) {
                return true;
            }
        }

        return false;
    }

    private boolean isFullLine(Dive.DiveLine diveLine) {
        boolean full = true;

        for (Dive.DiveGrid grid : diveLine.getGridList()) {
            if (grid.getType() == EDiveGrid.ePass.getType())
                return false;
        }

        return full;
    }

    public int getDepth(int idx) {
        return idx / WIDTH;
    }

    public int getIndex(int idx) {
        return idx % WIDTH;
    }

    public boolean isScreenUpOut(Dive model, int depth) {
        int firstDepth = getScreenFirstLineDepth(model.getDepth());
        boolean screenOut = depth < firstDepth;
        return screenOut;
    }

    public boolean isScreenDownOut(Dive model, int depth) {
        return depth > model.getDepth() + 1;
    }

    public int getScreenFirstLineDepth(int curDepth) {
        int firstDepth = curDepth - (SCREEN_LINE - 1 - 1);
        return firstDepth;
    }

    public boolean isScreenOut(Dive model, int depth) {
        return isScreenUpOut(model, depth) || isScreenDownOut(model, depth);
    }

    private boolean isScreenOut(int curDepth, int depth) {
        return depth > curDepth + 1 || depth < curDepth - SCREEN_LINE + 2;
    }

    public boolean isValidIndex(int index) {
        return index >= 0 && index < WIDTH;
    }

    public boolean isDiveDepth(Dive model, int depth) {
        return model.getDepth() == depth || model.getDepth() + 1 == depth;
    }

    /**
     * depth, x, isLight
     * @param model
     * @return
     */
    private Table<Integer, Integer, Boolean> lightMap(Dive model) {
        int depth = getScreenFirstLineDepth(model.getDepth());
        Table<Integer, Integer, Boolean> lighting = HashBasedTable.create();
        for (int i = 0; i < SCREEN_LINE; i++, depth++) {
            Dive.DiveLine line = getDiveLine(model, depth);
            for (int x = 0; x < WIDTH; x++) {
                Dive.DiveGrid grid = line.getGridList().get(x);
                boolean isPass = grid.getEDiveGridType().isPass();
                if (i == 0) {
                    lighting.put(depth, x, isPass);
                } else {
                    if (lighting.get(depth, x) == null) {
                        if (lighting.get(depth - 1, x) && isPass) {
                            lighting.put(depth, x, true);
                            // 递归照亮左右
                            lighting(line, x, lighting, RIGHT);
                            lighting(line, x, lighting, LEFT);
                        } else {
                            lighting.put(depth, x, false);
                        }
                    }
                }
            }
        }

        return lighting;
    }

    private void lighting(Dive.DiveLine line, int x, Table<Integer, Integer, Boolean> lighting, int[] dir) {
        int depth = line.getDepeth();
        x += dir[0];
        if (x < 0 || x >= WIDTH)
            return;
        Boolean light = lighting.get(depth, x);
        if (light != null && light)
            return;

        Dive.DiveGrid grid = line.getGridList().get(x);
        if (!grid.getEDiveGridType().isPass())
            return;

        lighting.put(depth, x, true);
        lighting(line, x, lighting, dir);
    }

}
