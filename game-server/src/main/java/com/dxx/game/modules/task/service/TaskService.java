package com.dxx.game.modules.task.service;

import com.dxx.game.common.server.model.Result;
import com.dxx.game.dao.dynamodb.model.Task;
import com.dxx.game.dto.TaskProto.*;
import com.dxx.game.modules.reward.result.RewardResultSet;
import com.dxx.game.modules.task.TaskProcess;

import java.util.List;
import java.util.Map;

public interface TaskService {

    /**
     * 获取任务数据
     *
     * @param params
     * @return
     */
    Result<TaskGetInfoResponse> getInfoAction(TaskGetInfoRequest params);

    /**
     * 领取任务完成奖励
     *
     * @param params
     * @return
     */
    Result<TaskRewardDailyResponse> taskRewardDailyAction(TaskRewardDailyRequest params);

    /**
     * 领取任务完成奖励
     *
     * @param params
     * @return
     */
    Result<TaskRewardAchieveResponse> taskRewardAchieveAction(TaskRewardAchieveRequest params);

    /**
     * 领取活跃度奖励
     *
     * @param params
     * @return
     */
    Result<TaskActiveRewardAllResponse> taskActiveRewardAllAction(TaskActiveRewardAllRequest params);

    //--------------------------------------------------------------------------------------------

    /**
     * 更新任务
     *
     * @param userId
     * @param taskProcesses
     * @return
     */
    void updateTask(long userId, TaskProcess... taskProcesses);

    /**
     * 更新任务
     *
     * @param userId
     */
    void updateTask(long userId, List<TaskProcess> taskProcesses);

    /**
     * 更新任务
     *
     * @param userId
     * @param rewardResultSet
     */
    void updateTask(long userId, RewardResultSet rewardResultSet);

    Map<Integer, Task.TaskModel> getTasks(int taskType);

}
