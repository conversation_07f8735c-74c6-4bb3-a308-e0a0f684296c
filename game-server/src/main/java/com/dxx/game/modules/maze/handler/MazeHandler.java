package com.dxx.game.modules.maze.handler;

import com.dxx.game.common.server.annotation.ApiHandler;
import com.dxx.game.common.server.annotation.ApiMethod;
import com.dxx.game.common.server.model.Result;
import com.dxx.game.consts.MsgReqCommand;
import com.dxx.game.dto.MazeProto.*;
import com.dxx.game.modules.maze.service.MazeService;
import com.google.protobuf.Message;
import org.springframework.beans.factory.annotation.Autowired;

@ApiHandler
public class MazeHandler {

    @Autowired
    private MazeService mazeService;

    @ApiMethod(command = MsgReqCommand.MazeGetInfoRequest, name = "迷宫信息")
    public Result<MazeGetInfoResponse> getInfo(Message msg) {
        MazeGetInfoRequest params = (MazeGetInfoRequest) msg;
        return mazeService.getInfo(params);
    }

    @ApiMethod(command = MsgReqCommand.StartMazeRequest, name = "进入迷宫")
    public Result<StartMazeResponse> startMaze(Message msg) {
        StartMazeRequest params = (StartMazeRequest) msg;
        return mazeService.startMaze(params);
    }

    @ApiMethod(command = MsgReqCommand.MazeRoomStartRequest, name = "开始一个房间")
    public Result<MazeRoomStartResponse> mazeRoomStart(Message msg) {
        MazeRoomStartRequest params = (MazeRoomStartRequest) msg;
        return mazeService.mazeRoomStart(params);
    }

    @ApiMethod(command = MsgReqCommand.MazeRoomFinishRequest, name = "结束一个房间")
    public Result<MazeRoomFinishResponse> mazeRoomFinish(Message msg) {
        MazeRoomFinishRequest params = (MazeRoomFinishRequest) msg;
        return mazeService.mazeRoomFinish(params);
    }

    @ApiMethod(command = MsgReqCommand.MazeRoomOpenRequest, name = "翻开一个房间")
    public Result<MazeRoomOpenResponse> mazeRoomOpen(Message msg) {
        MazeRoomOpenRequest params = (MazeRoomOpenRequest) msg;
        return mazeService.mazeRoomOpen(params);
    }

    @ApiMethod(command = MsgReqCommand.QuitMazeRequest, name = "退出迷宫")
    public Result<QuitMazeResponse> quitMaze(Message msg) {
        QuitMazeRequest params = (QuitMazeRequest) msg;
        return mazeService.quitMaze(params);
    }

    @ApiMethod(command = MsgReqCommand.MazeNextLayerRequest, name = "进入迷宫下一层")
    public Result<MazeNextLayerResponse> mazeNextLayer(Message msg) {
        MazeNextLayerRequest params = (MazeNextLayerRequest) msg;
        return mazeService.mazeNextLayer(params);
    }

    @ApiMethod(command = MsgReqCommand.MazeSetFormationDataRequest, name = "设置透传字符串")
    public Result<MazeSetFormationDataResponse> mazeSetFormationData(Message msg) {
        MazeSetFormationDataRequest params=(MazeSetFormationDataRequest) msg;
        return mazeService.mazeSetFormationData(params);
    }

}
