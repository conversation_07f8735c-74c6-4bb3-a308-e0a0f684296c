package com.dxx.game.modules.equip.support;

import com.amazonaws.xray.spring.aop.XRayEnabled;
import com.dxx.game.config.GameConfigManager;
import com.dxx.game.config.entity.equip.EquipEntity;
import com.dxx.game.dao.dynamodb.model.Equip;
import com.dxx.game.dao.dynamodb.model.Hero;
import com.dxx.game.dao.dynamodb.repository.EquipDao;
import com.dxx.game.dao.dynamodb.repository.HeroDao;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
@XRayEnabled
public class EquipSupport {

    @Resource
    private GameConfigManager gameConfigManager;

    /**
     * 英雄卸下装备
     *
     * @param hero  英雄实体
     * @param equip 装备实体
     */
    public void downEquipment(Hero hero, Equip equip) {
        EquipEntity equipEntity = gameConfigManager.getEquipConfig().getEquipEntity(equip.getEquipId());
        equip.setHeroRowId(0L);
        hero.getEquips().remove(equipEntity.getType());
    }

    /**
     * 英雄戴上装备
     *
     * @param hero  英雄实体
     * @param equip 装备实体
     */
    public void upEquipment(Hero hero, Equip equip) {
        EquipEntity equipEntity = gameConfigManager.getEquipConfig().getEquipEntity(equip.getEquipId());
        equip.setHeroRowId(hero.getRowId());
        hero.getEquips().put(equipEntity.getType(), equip);
    }
}
