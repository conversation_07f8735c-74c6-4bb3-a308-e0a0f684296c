package com.dxx.game.modules.conquer.handler;

import com.dxx.game.common.server.annotation.ApiHandler;
import com.dxx.game.common.server.annotation.ApiMethod;
import com.dxx.game.common.server.model.Result;
import com.dxx.game.consts.MsgReqCommand;
import com.dxx.game.dto.ConquerProto;
import com.dxx.game.modules.conquer.service.ConquerService;
import com.google.protobuf.Message;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @date 2023/8/2 17:09
 */
@ApiHandler
public class ConquerHandler {
    @Autowired
    private ConquerService conquerService;

    @ApiMethod(command = MsgReqCommand.ConquerListRequest, name = "征服战-列表")
    public Result<ConquerProto.ConquerListResponse> conquerList(Message msg) {
        ConquerProto.ConquerListRequest params = (ConquerProto.ConquerListRequest) msg;
        return conquerService.conquerList(params);
    }

    @ApiMethod(command = MsgReqCommand.ConquerRecommendRequest, name = "征服战-推荐列表")
    public Result<ConquerProto.ConquerRecommendResponse> conquerRecommend(Message msg) {
        ConquerProto.ConquerRecommendRequest params = (ConquerProto.ConquerRecommendRequest) msg;
        return conquerService.conquerRecommend(params);
    }

    @ApiMethod(command = MsgReqCommand.ConquerRecommendRefRequest, name = "征服战-推荐列表刷新")
    public Result<ConquerProto.ConquerRecommendRefResponse> conquerRecommendRef(Message msg) {
        ConquerProto.ConquerRecommendRefRequest params = (ConquerProto.ConquerRecommendRefRequest) msg;
        return conquerService.conquerRecommendRef(params);
    }

    @ApiMethod(command = MsgReqCommand.ConquerBattleRequest, name = "征服战-征服")
    public Result<ConquerProto.ConquerBattleResponse> conquerBattle(Message msg) {
        ConquerProto.ConquerBattleRequest params = (ConquerProto.ConquerBattleRequest) msg;
        return conquerService.conquerBattle(params);
    }

    @ApiMethod(command = MsgReqCommand.ConquerRevoltRequest, name = "征服战-反叛")
    public Result<ConquerProto.ConquerRevoltResponse> conquerRevolt(Message msg) {
        ConquerProto.ConquerRevoltRequest params = (ConquerProto.ConquerRevoltRequest) msg;
        return conquerService.conquerRevolt(params);
    }

    @ApiMethod(command = MsgReqCommand.ConquerLootRequest, name = "征服战-抢夺")
    public Result<ConquerProto.ConquerLootResponse> conquerLoot(Message msg) {
        ConquerProto.ConquerLootRequest params = (ConquerProto.ConquerLootRequest) msg;
        return conquerService.conquerLoot(params);
    }

    @ApiMethod(command = MsgReqCommand.ConquerPardonRequest, name = "征服战-赦免")
    public Result<ConquerProto.ConquerPardonResponse> conquerPardon(Message msg) {
        ConquerProto.ConquerPardonRequest params = (ConquerProto.ConquerPardonRequest) msg;
        return conquerService.conquerPardon(params);
    }
}
