package com.dxx.game.modules.reward.model;

import com.amazonaws.xray.spring.aop.XRayEnabled;
import com.dxx.game.consts.RewardResourceType;
import com.dxx.game.consts.RewardType;

/**
 * <AUTHOR>
 * @date 2021/4/1 15:02
 */
@XRayEnabled
public class SkinReward implements Reward {

    /**
     * 奖励类型   {@link RewardType}
     */
    private RewardType type = RewardType.SKIN;

    /**
     * 奖励子类型   {@link RewardResourceType}
     */
    private RewardResourceType resourceType = RewardResourceType.NONE;

    /**
     * 数量
     */
    private int count;

    /**
     * 配置表ID
     */
    private int configId;

    public SkinReward(int configId, int count) {
        this.configId = configId;
        this.count = count;
    }

    public static SkinReward valueOf(int configId, int count) {
        return new SkinReward(configId, count);
    }

    @Override
    public RewardType getType() {
        return this.type;
    }

    @Override
    public RewardResourceType getResourceType() {
        return this.resourceType;
    }

    @Override
    public int getCount() {
        return this.count;
    }

    @Override
    public int getConfigId() {
        return this.configId;
    }

    @Override
    public Reward increase(int incrCount) {
        return null;
    }

    @Override
    public Reward union(Reward reward) {
        return null;
    }

    @Override
    public boolean match(Reward reward) {
        return false;
    }
}
