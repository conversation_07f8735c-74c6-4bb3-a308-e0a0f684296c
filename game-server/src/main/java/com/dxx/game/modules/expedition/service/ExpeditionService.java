package com.dxx.game.modules.expedition.service;

import com.dxx.game.common.server.model.Result;
import com.dxx.game.config.entity.expedition.ExpeditionStageEntity;
import com.dxx.game.consts.GameConstant;
import com.dxx.game.dao.dynamodb.model.UserExtend;
import com.dxx.game.dao.dynamodb.model.gameplay.UserExpedition;
import com.dxx.game.dao.dynamodb.model.gameplay.UserFixedFormation;
import com.dxx.game.dto.CommonProto;
import com.dxx.game.dto.ExpeditionProto;
import com.dxx.game.dto.MazeProto;
import com.dxx.game.modules.common.support.CommonHelper;
import org.apache.commons.lang3.tuple.Pair;

import java.util.List;

public interface ExpeditionService {
    /**
     * 远征拉取信息
     * @param params
     * @return
     */
    Result<ExpeditionProto.ExpeditionInfoResponse> getExpeditionInfo(ExpeditionProto.ExpeditionInfoRequest params);
    /**
     * 开始远征
     * @param params
     * @return
     */
    Result<ExpeditionProto.StartExpeditionResponse> startExpedition(ExpeditionProto.StartExpeditionRequest params);
    /**
     * 开始战斗
     * @param params
     * @return
     */
    Result<ExpeditionProto.ExpeditionBattleResponse> fight(ExpeditionProto.ExpeditionBattleRequest params);
    /**
     * 选择神器
     * @param params
     * @return
     */
    Result<ExpeditionProto.ExpeditionSelectRewardResponse> selectReward(ExpeditionProto.ExpeditionSelectRewardRequest params);
    /**
     * 使用神器
     * @param params
     * @return
     */
    Result<ExpeditionProto.ExpeditionUseItemResponse> useItem(ExpeditionProto.ExpeditionUseItemRequest params);
    /**
     * 领奖
     * @param params
     * @return
     */
    Result<ExpeditionProto.ExpeditionGetRewardResponse> getReward(ExpeditionProto.ExpeditionGetRewardRequest params);

    void initExpeditionModel(UserExpedition expeditionModel);

    List<List<Long>> getMatchList(int warZoneId, int layer);

    void delMatchList(int warZoneId, int layer);

    Pair<Integer, UserFixedFormation> initFormation(long userId, UserExpedition userExpeditionModel);

    void devMatch(UserExpedition userExpeditionModel);
}
