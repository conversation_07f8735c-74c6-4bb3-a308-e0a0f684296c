package com.dxx.game.modules.circular.service.impl;

import com.amazonaws.xray.spring.aop.XRayEnabled;
import com.dxx.game.common.aws.dynamodb.transaction.annotation.DynamoDBTransactional;
import com.dxx.game.common.server.context.RequestContext;
import com.dxx.game.common.server.model.Result;
import com.dxx.game.common.utils.DateUtils;
import com.dxx.game.config.GameConfigManager;
import com.dxx.game.config.entity.circularrecharge.DailyRechargeEntity;
import com.dxx.game.config.entity.circularrecharge.RewardGroupEntity;
import com.dxx.game.config.entity.circularrecharge.TotalConsumptionEntity;
import com.dxx.game.config.entity.iap.PurchaseEntity;
import com.dxx.game.consts.ErrorCode;
import com.dxx.game.consts.FunctionTypeEnum;
import com.dxx.game.dao.dynamodb.model.Activity;
import com.dxx.game.dao.dynamodb.repository.ActivityDao;
import com.dxx.game.dto.CommonProto;
import com.dxx.game.dto.CircularRechargeProto.*;
import com.dxx.game.modules.common.support.CommonHelper;
import com.dxx.game.modules.circular.service.CircularRechargeService;
import com.dxx.game.modules.function.support.FunctionHelp;
import com.dxx.game.modules.mail.MailService;
import com.dxx.game.modules.reward.result.RewardResultSet;
import com.dxx.game.modules.reward.service.RewardService;
import com.google.api.client.util.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;


@XRayEnabled
@Service
@Slf4j
public class CircularRechargeServiceImpl implements CircularRechargeService {

    @Autowired
    private ActivityDao activityDao;
    @Autowired
    private GameConfigManager gameConfigManager;
    @Autowired
    private FunctionHelp functionHelp;
    @Autowired
    private MailService mailService;
    @Autowired
    private RewardService rewardService;

    @Override
    @DynamoDBTransactional
    public Result<DailyRechargeResponse> doDailyRechargeAction(DailyRechargeRequest params) {
        long userId = RequestContext.getUserId();
        Activity activity = activityDao.getByUserId(userId);
        Map<Integer, Activity.DailyRechargeModel> dailyRecharges = activity.getDailyRecharges();
        if (dailyRecharges == null || dailyRecharges.isEmpty()) {
            return Result.Error(ErrorCode.QUERY_USER_DATA_ERROR);
        }
        DailyRechargeEntity dailyRechargeEntity = gameConfigManager.getCircularRechargeConfig().getDailyRechargeEntity(params.getConfigId());
        if (dailyRechargeEntity == null) {
            log.error("dailyRechargeEntity is null, configId: {}", params.getConfigId());
            return Result.Error(ErrorCode.PARAMS_ERROR);
        }
        Activity.DailyRechargeModel dailyRechargeModel = dailyRecharges.get(dailyRechargeEntity.getPurchaseId());
        if (dailyRechargeModel == null) {
            log.error("dailyRechargeModel is null, configId: {}", params.getConfigId());
            return Result.Error(ErrorCode.PARAMS_ERROR);
        }
        RewardResultSet rewardResultSet = null;
        if (params.getRewardType() == 0) { //领取充值奖励
            if (params.getRewardIndex() >= dailyRechargeEntity.getReward().size()) {
                log.error("dailyRechargeEntity getRewardIndex out of range, configId: {} index: {}", params.getConfigId(), params.getRewardIndex());
                return Result.Error(ErrorCode.PARAMS_ERROR);
            }
            long mask = 1;
            if (params.getRewardIndex() > 0) {
                mask = 1 << params.getRewardIndex();
            }
            long rewardGot = dailyRechargeModel.getHasGotReward() & mask;
            if (rewardGot != 0) {
                log.error("DAILY_RECHARGE_HAS_GOT_REWARD, configId: {} index: {}", params.getConfigId(), params.getRewardIndex());
                return Result.Error(ErrorCode.DAILY_RECHARGE_HAS_GOT_REWARD);
            }
            rewardGot = dailyRechargeModel.getHasGotReward() | mask;

            int needRechargeDay =  Integer.bitCount(dailyRechargeModel.getRechargeDays());
            if (needRechargeDay <= params.getRewardIndex() ) {
                log.error("DAILY_RECHARGE_NOT_ENOUGH, configId: {} index: {} needRechargeDay: {}",
                        params.getConfigId(), params.getRewardIndex(), needRechargeDay);
                return Result.Error(ErrorCode.DAILY_RECHARGE_NOT_ENOUGH);
            }
            int groupId = dailyRechargeEntity.getReward().get(params.getRewardIndex());
            List<List<Integer>> rewards = gameConfigManager.getCircularRechargeConfig().getRewardGroup().get(groupId).getReward();
            rewardResultSet = rewardService.executeRewards(userId, rewards);
            if (rewardResultSet.isFailed()) {
                return Result.Error(rewardResultSet.getResultCode());
            }
            dailyRechargeModel.setHasGotReward(rewardGot);
        } else { //领取累充次数奖励
            if (dailyRechargeEntity == null) {
                log.error("rechargeTargetEntity is null, configId: {}", params.getConfigId());
                return Result.Error(ErrorCode.PARAMS_ERROR);
            }
            if (params.getRewardIndex() >= dailyRechargeEntity.getReward().size()) {
                log.error("rechargeTargetEntity getRewardIndex out of range, configId: {} index: {}", params.getConfigId(), params.getRewardIndex());
                return Result.Error(ErrorCode.PARAMS_ERROR);
            }
            long mask = 1;
            if (params.getRewardIndex() > 0) {
                mask = 1 << params.getRewardIndex();
            }
            long rewardGot = dailyRechargeModel.getHasGotBoxReward() & mask;
            if (rewardGot != 0) {
                log.error("DAILY_RECHARGE_HAS_GOT_REWARD, configId: {} index: {}", params.getConfigId(), params.getRewardIndex());
                return Result.Error(ErrorCode.DAILY_RECHARGE_HAS_GOT_REWARD);
            }
            rewardGot = dailyRechargeModel.getHasGotBoxReward() | mask;

            int needRechargeDay = dailyRechargeEntity.getRechargetarget().get(params.getRewardIndex());
            if (Long.bitCount(dailyRechargeModel.getRechargeDays()) < needRechargeDay) {
                log.error("DAILY_RECHARGE_NOT_ENOUGH needRechargeDay, configId: {} index: {} needRechargeDay: {} realRechargeDay: {}",
                        params.getConfigId(), params.getRewardIndex(), needRechargeDay, Long.bitCount(dailyRechargeModel.getRechargeDays()));
                return Result.Error(ErrorCode.DAILY_RECHARGE_NOT_ENOUGH);
            }

            List<List<Integer>> rewards = new ArrayList<>();
            rewards.add(dailyRechargeEntity.getRewardtarget().get(params.getRewardIndex()));
            rewardResultSet = rewardService.executeRewards(userId, rewards);
            if (rewardResultSet.isFailed()) {
                return Result.Error(rewardResultSet.getResultCode());
            }
            dailyRechargeModel.setHasGotBoxReward(rewardGot);
        }
        activityDao.updateDailyRecharge(activity);
        DailyRechargeResponse.Builder response = DailyRechargeResponse.newBuilder();
        CommonProto.CommonData commonData = CommonHelper.buildCommonData(rewardResultSet);
        response.setCommonData(commonData);
        response.setDailyRecharge(this.buildDailyRechargeData(dailyRechargeModel, activity.getInitDailyRechargeTime()));
        return Result.Success(response.build());
    }

    private DailyRechargeData buildDailyRechargeData(Activity.DailyRechargeModel dailyRechargeModel, long initDailyRechargeTime) {
        DailyRechargeData.Builder builder = DailyRechargeData.newBuilder();
        builder.setConfigId(dailyRechargeModel.getId());
        DailyRechargeEntity dailyRechargeEntity = gameConfigManager.getCircularRechargeConfig().getDailyRechargeEntity(dailyRechargeModel.getId());
        builder.setRechargeDays(Integer.bitCount(dailyRechargeModel.getRechargeDays()));
        builder.setRewardGotBit(dailyRechargeModel.getHasGotReward());
        builder.setTotalRewardBit(dailyRechargeModel.getHasGotBoxReward());
        builder.setLastRechargeTime(dailyRechargeModel.getShowTimestamp());
        builder.setEndTimestamp(initDailyRechargeTime + (dailyRechargeEntity.getEndTime() * (DateUtils.DAY_PER_SECONDS)) - 1);
        return builder.build();
    }

    @Override
    @DynamoDBTransactional
    public Result<TotalConsumtionResponse> doTotalConsumtionAction(TotalConsumtionRequest params) {
        long userId = RequestContext.getUserId();
        Activity activity = activityDao.getByUserId(userId);
        Activity.TotalRechargeModel totalRechargeModel = activity.getTotalRecharge();
        if (totalRechargeModel == null) {
            return Result.Error(ErrorCode.QUERY_USER_DATA_ERROR);
        }
        TotalConsumptionEntity totalConsumptionEntity = gameConfigManager.getCircularRechargeConfig().getTotalConsumptionEntity(totalRechargeModel.getId());
        if (totalConsumptionEntity == null) {
            log.error("totalConsumptionEntity is null, configId: {}", totalRechargeModel.getId());
            return Result.Error(ErrorCode.PARAMS_ERROR);
        }
        if (params.getRewardIndex() >= totalConsumptionEntity.getReward().size()){
            log.error("totalConsumptionEntity getRewardIndex out of range, configId: {} index: {}", totalRechargeModel.getId(), params.getRewardIndex());
            return Result.Error(ErrorCode.PARAMS_ERROR);
        }
        long mask = 1;
        if (params.getRewardIndex() > 0){
            mask = 1 << params.getRewardIndex();
        }
        long rewardGot = totalRechargeModel.getHasGotReward()&mask;
        if (rewardGot != 0) {
            log.error("TotalConsumption_HAS_GOT_REWARD, configId: {} index: {}", totalRechargeModel.getId(), params.getRewardIndex());
            return Result.Error(ErrorCode.TOTAL_CONSUMPTION_HAS_GOT_REWARD);
        }
        int needConsumeId = totalConsumptionEntity.getPurchaseId().get(params.getRewardIndex());
        PurchaseEntity needPurchaseEntity = gameConfigManager.getIAPConfig().getPurchaseEntity(needConsumeId);
        int needConsume = Math.round(needPurchaseEntity.getPrice() * 100);
        if (gameConfigManager.isCn()) {
            needConsume = Math.round(needPurchaseEntity.getChinaPrice() * 100);
        }
        if (needConsume > totalRechargeModel.getTotalRecharge()) {
            log.error("TotalConsumption_NOT_ENOUGH, configId: {} index: {} needConsume: {} totalRecharge: {}", totalRechargeModel.getId(), params.getRewardIndex(), needConsume, totalRechargeModel.getTotalRecharge());
            return Result.Error(ErrorCode.TOTAL_CONSUMPTION_NOT_ENOUGH);
        }
        RewardGroupEntity rewardGroup = gameConfigManager.getCircularRechargeConfig().getRewardGroup().get(totalConsumptionEntity.getReward().get(params.getRewardIndex()));
        RewardResultSet rewardResultSet = rewardService.executeRewards(userId, rewardGroup.getReward());
        if (rewardResultSet.isFailed()) {
            return Result.Error(rewardResultSet.getResultCode());
        }
        rewardGot = totalRechargeModel.getHasGotReward() | mask;
        // 全部领取完成,重置领取状态
        if(Long.bitCount(rewardGot) == totalConsumptionEntity.getPurchaseId().size()) {
            int nMaxConsumeId = totalConsumptionEntity.getPurchaseId().get(totalConsumptionEntity.getPurchaseId().size()-1);
            PurchaseEntity nMaxPurchaseEntity = gameConfigManager.getIAPConfig().getPurchaseEntity(nMaxConsumeId);
            int nMaxConsume = Math.round(nMaxPurchaseEntity.getPrice() * 100);
            if (gameConfigManager.isCn()) {
                nMaxConsume = Math.round(nMaxPurchaseEntity.getChinaPrice() * 100);
            }
            totalRechargeModel.setTotalRecharge(totalRechargeModel.getTotalRecharge() - nMaxConsume);
            totalRechargeModel.setRound(totalRechargeModel.getRound() + 1);
            rewardGot = 0;
        }
        totalRechargeModel.setHasGotReward(rewardGot);
        activityDao.updateTotalRecharge(activity);

        TotalConsumtionResponse.Builder response = TotalConsumtionResponse.newBuilder();
        CommonProto.CommonData commonData = CommonHelper.buildCommonData(rewardResultSet);
        response.setCommonData(commonData);
        response.setTotalComsuption(this.buildTotalConsumtionData(totalRechargeModel));
        return Result.Success(response.build());
    }

    @Override
    public boolean checkRecharge(long userId, int productId, PurchaseEntity purchaseEntity) {
        // 累充
        float rechargeCount = purchaseEntity.getPrice();
        if (gameConfigManager.isCn()) {
            rechargeCount = purchaseEntity.getChinaPrice();
        }

        int rechargeNum = Math.round(rechargeCount * 100);

        if (functionHelp.isFunctionOpen(FunctionTypeEnum.FUNCTION_TOTAL_RECHARGE, userId)) {
            Activity activity = activityDao.getByUserId(userId);
            Activity.TotalRechargeModel totalRechargeModel = activity.getTotalRecharge();
            if (totalRechargeModel == null) {
                totalRechargeModel = this.initTotalRechargeData(userId);
            }
            int curTotalRecharge = totalRechargeModel.getTotalRecharge();
            curTotalRecharge = curTotalRecharge + rechargeNum;
            totalRechargeModel.setTotalRecharge(curTotalRecharge);
            activityDao.updateTotalRecharge(activity);
        }

        // 每日充值
        if (functionHelp.isFunctionOpen(FunctionTypeEnum.FUNCTION_DAILY_RECHARGE, userId)) {
            Activity activity = activityDao.getByUserId(userId);
            Map<Integer, Activity.DailyRechargeModel> map = this.initDailyRechargeData(userId);
            if (map != null) {
                map.forEach((k, v) -> {
                    long now = DateUtils.getUnixTime();
                    if (DateUtils.isSameDay(v.getTimestamp(), now)) {
                        int nCurTotalRecharge = v.getTotalRecharge();
                        nCurTotalRecharge += rechargeNum;
                        v.setTotalRecharge(nCurTotalRecharge);
                    } else {
                        v.setTotalRecharge(rechargeNum);
                    }
                    v.setTimestamp(DateUtils.getUnixTime());

                    if (v.getFirstPayTimestamp() == 0) {
                        v.setFirstPayTimestamp(DateUtils.getTimeHour0InDay());
                    }

                    int nIndex = (int)((now - v.getFirstPayTimestamp()) / DateUtils.DAY_PER_SECONDS);
                    DailyRechargeEntity dailyRechargeEntity = gameConfigManager.getCircularRechargeConfig().getDailyRechargeEntity(v.getId());
                    PurchaseEntity target = gameConfigManager.getIAPConfig().getPurchaseEntity(dailyRechargeEntity.getPurchaseId());
                    float targetCount = target.getPrice();
                    if (gameConfigManager.isCn()) {
                        targetCount = target.getChinaPrice();
                    }

                    int targetRechargeNum = Math.round(targetCount * 100);
                    if (v.getTotalRecharge() >= targetRechargeNum) {
                        int mask = 1;
                        if (nIndex > 0){
                            mask = 1 << nIndex;
                        }
                        int nRecorded = v.getRechargeDays() & mask;
                        if (nRecorded == 0) {
                            v.setShowTimestamp(DateUtils.getUnixTime());
                            v.setRechargeDays(v.getRechargeDays() | mask);
                        }
                    }
                });
                activityDao.updateDailyRecharge(activity);
            }
        }

        return true;
    }

    private  Activity.TotalRechargeModel initTotalRechargeData(long userId) {
        Activity activity = activityDao.getByUserId(userId);
        Activity.TotalRechargeModel totalRechargeModel = new Activity.TotalRechargeModel();
        totalRechargeModel.setTotalRecharge(0);
        totalRechargeModel.setHasGotReward(0);
        totalRechargeModel.setId(1);
        totalRechargeModel.setRound(1);
        activity.setTotalRecharge(totalRechargeModel);
        activityDao.updateTotalRecharge(activity);
        return totalRechargeModel;
    }

    private boolean isDailyRechargeOpen(long initDailyRechargeTime, int actId) {
        DailyRechargeEntity entity = gameConfigManager.getCircularRechargeConfig().getDailyRechargeEntity(actId);
        if (entity == null) {
            log.error("daily recharge config not found, actId: {}", actId);
            return false;
        }
        long oneDayInSeconds = DateUtils.DAY_PER_SECONDS;
        long openTime = initDailyRechargeTime + ((entity.getOpenTime()-1) * oneDayInSeconds);
        long endTime = initDailyRechargeTime + (entity.getEndTime() * oneDayInSeconds) - 1;
//        if (gameConfigManager.isTest()) {
//            log.info("isDailyRechargeOpen openTime: {}, endTime: {} now: {} actId: {} initDailyRechargeTime: {}",
//                    openTime, endTime,DateUtils.getUnixTime(), actId,initDailyRechargeTime);
//        }
        return DateUtils.isBetween(DateUtils.getUnixTime(), openTime, endTime);
    }

    private Map<Integer, Activity.DailyRechargeModel> initDailyRechargeData(long userId) {
        List<DailyRechargeEntity> openList = new ArrayList<>();
        Activity activity = activityDao.getByUserId(userId);
        long initDailyRechargeTime = activity.getInitDailyRechargeTime() == null ? DateUtils.getTimeHour0InDay() : activity.getInitDailyRechargeTime();

        boolean needUpdate = false;
        Map<Integer, Activity.DailyRechargeModel> map = activity.getDailyRecharges();
        if (map != null) {
            Iterator<Map.Entry<Integer, Activity.DailyRechargeModel>> iterator = map.entrySet().iterator();
            while (iterator.hasNext()) {
                Map.Entry<Integer, Activity.DailyRechargeModel> entry = iterator.next();
                if (!this.isDailyRechargeOpen(initDailyRechargeTime, entry.getValue().getId())) {
                    this.checkDailyRechargeSendMail(userId, entry.getValue());
                    iterator.remove();
                    needUpdate = true;
                }
            }
        }

        for (Map.Entry<Integer, List<DailyRechargeEntity> > entry : gameConfigManager.getDailyTypeActivityMap().entrySet()) {
            for (DailyRechargeEntity entity : entry.getValue()) {
                if (this.isDailyRechargeOpen(initDailyRechargeTime, entity.getID())) {
                    openList.add(entity);
                }
            }
        }

        if (!openList.isEmpty()) {
            if (map == null) {
                map = new HashMap<>();
                needUpdate = true;
            }
            for (DailyRechargeEntity entity : openList) {
                if (map.get(entity.getPurchaseId()) == null || map.get(entity.getPurchaseId()).getId() != entity.getID()) {
                    Activity.DailyRechargeModel model = new Activity.DailyRechargeModel();
                    model.setId(entity.getID());
                    model.setRechargeDays(0);
                    model.setHasGotReward(0);
                    model.setHasGotBoxReward(0);
                    model.setTotalRecharge(0);
                    model.setFirstPayTimestamp(0);
                    map.put(entity.getPurchaseId(), model);
                    needUpdate = true;
                }
            }
        }

        if (needUpdate) {
            activity.setInitDailyRechargeTime(initDailyRechargeTime);
            activity.setDailyRecharges(map);
            activityDao.updateDailyRecharge(activity);
        }
        return map;
    }

    private void checkDailyRechargeSendMail(long userId, Activity.DailyRechargeModel dailyRechargeModel) {
        // 充值奖励
        List<List<Integer>> rewards = Lists.newArrayList();
        DailyRechargeEntity entity = gameConfigManager.getCircularRechargeConfig().getDailyRechargeEntity(dailyRechargeModel.getId());
        int nRechargeDays = Integer.bitCount(dailyRechargeModel.getRechargeDays());
        int nRewardGotDays = Long.bitCount(dailyRechargeModel.getHasGotReward());
        if (nRewardGotDays < nRechargeDays) {
            for (int i = nRewardGotDays; i < nRechargeDays; i++) {
                RewardGroupEntity reward =  gameConfigManager.getCircularRechargeConfig().getRewardGroupEntity(entity.getReward().get(i));
                rewards.addAll(reward.getReward());
            }
        }
        if (!rewards.isEmpty()) {
            mailService.createMail(userId, entity.getProd_MailTempId(), userId +"_DailyRecharge_"+ entity.getID(), null, rewards);
        }

        List<List<Integer>> rewardsBox = Lists.newArrayList();
        // 宝箱奖励
        int nBoxRewardGotDays = Long.bitCount(dailyRechargeModel.getHasGotBoxReward());
        if (nBoxRewardGotDays < entity.getRechargetarget().size()) {
            for (int i = nBoxRewardGotDays; i < entity.getRechargetarget().size(); i++) {
                int nNeedRecharge = entity.getRechargetarget().get(i);
                if (nRechargeDays >= nNeedRecharge) {
                    List<Integer> reward = entity.getRewardtarget().get(i);
                    rewardsBox.add(reward);
                }
            }
        }
        if (!rewardsBox.isEmpty()) {
            mailService.createMail(userId, entity.getProd_MailTempId(), userId +"_DailyRechargeTotal_"+ entity.getID(), null, rewardsBox);
        }
    }

    private TotalConsumtionData buildTotalConsumtionData(Activity.TotalRechargeModel totalRechargeModel) {
        TotalConsumtionData.Builder builder = TotalConsumtionData.newBuilder();
        builder.setTotalConsumption(totalRechargeModel.getTotalRecharge());
        builder.setRewardGotBit(totalRechargeModel.getHasGotReward());
        builder.setRound(totalRechargeModel.getRound());
        return builder.build();
    }

    @Override
    public TotalConsumtionData getTotalConsumptionData(long userId) {
        if (!functionHelp.isFunctionOpen(FunctionTypeEnum.FUNCTION_TOTAL_RECHARGE, userId)) {
            return null;
        }
        Activity activity = activityDao.getByUserId(userId);
        Activity.TotalRechargeModel totalRechargeModel = activity.getTotalRecharge();
        if (totalRechargeModel == null) {
            totalRechargeModel =  this.initTotalRechargeData(userId);
        }

        return this.buildTotalConsumtionData(totalRechargeModel);
    }

    @Override
    public List<DailyRechargeData> getDailyRechargeData(long userId) {
        List<DailyRechargeData> dtos = new ArrayList<>();
        if (!functionHelp.isFunctionOpen(FunctionTypeEnum.FUNCTION_DAILY_RECHARGE, userId)) {
            return dtos;
        }

        Map<Integer, Activity.DailyRechargeModel> map = this.initDailyRechargeData(userId);
        if (map != null) {
            Activity activity = activityDao.getByUserId(userId);
            long initDailyRechargeTime = activity.getInitDailyRechargeTime() == null ? DateUtils.getTimeHour0InDay() : activity.getInitDailyRechargeTime();
            map.values().forEach(v -> {
                dtos.add(this.buildDailyRechargeData(v, initDailyRechargeTime));
            });
        }

        return dtos;
    }
}
