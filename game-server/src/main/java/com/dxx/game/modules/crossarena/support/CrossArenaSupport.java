package com.dxx.game.modules.crossarena.support;

import com.amazonaws.xray.spring.aop.XRayEnabled;
import com.dxx.game.common.redis.RedisLock;
import com.dxx.game.common.redis.RedisService;
import com.dxx.game.common.utils.DateUtils;
import com.dxx.game.common.utils.RandomUtil;
import com.dxx.game.config.GameConfigManager;
import com.dxx.game.config.entity.crossarena.*;
import com.dxx.game.consts.GameConstant;
import com.dxx.game.consts.RedisKeys;
import com.dxx.game.dao.dynamodb.model.Hero;
import com.dxx.game.dao.dynamodb.model.User;
import com.dxx.game.dao.dynamodb.model.UserExtend;
import com.dxx.game.dao.dynamodb.repository.UserExtendDao;
import com.dxx.game.modules.common.GroupHelper;
import com.dxx.game.modules.common.service.CommonService;
import com.dxx.game.modules.crossarena.data.CrossArenaRecordData;
import com.dxx.game.modules.hero.service.HeroService;
import com.dxx.game.modules.rank.support.RankHelper;
import com.dxx.game.modules.server.service.ServerListService;
import com.dxx.game.modules.user.service.UserService;
import com.google.api.client.util.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.Duration;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/9/6 16:40
 */
@Component
@XRayEnabled
public class CrossArenaSupport {
    @Autowired
    private RedisService redisService;
    @Autowired
    private GameConfigManager gameConfigManager;
    @Resource
    private RedisLock redisLock;
    @Autowired
    private UserService userService;
    @Autowired
    private HeroService heroService;
    @Autowired
    private CommonService commonService;
    @Autowired
    private GroupHelper groupHelper;

    /** 挑战列表人数 */
    public static final int CHALLENGE_LIST_COUNT = 4;
    public static final long MAX_TIME = 3000000000L;
    @Autowired
    private UserExtendDao userExtendDao;

    @Autowired
    private ServerListService serverListService;

    /** 最长持续时间 天*/
    private final int MAX_SETTLEMENT_TIME = 60;

    /**
     * 是否是竞技场开放时间
     */
    public boolean isOpenTime() {
        long time = DateUtils.getUnixTime();

        CrossArenaTimeEntity entity = gameConfigManager.getCrossArenaConfig().getCrossArenaTimeEntity(1);
        String openTime = DateUtils.getDateyyyyMMddf() + " " + entity.getOpenTime();
        String endTime = DateUtils.getDateyyyyMMddf() + " " + entity.getCloseTime();
        long openTimeStamp = DateUtils.parseToTimestamp(openTime) / 1000;
        long endTimeStamp = DateUtils.parseToTimestamp(endTime) / 1000;

        return DateUtils.isBetween(time, openTimeStamp, endTimeStamp);
    }

    public int getDan(UserExtend userExtend) {
        if(userExtend.getCrossArenaModel() == null) {
            return 1;
        }

        return userExtend.getCrossArenaModel().getDan();
    }

    public void upDan(int season, UserExtend userExtend) {
        int dan = userExtend.getCrossArenaModel().getDan();
        int fDan = dan + 1;
        if(gameConfigManager.getCrossArenaConfig().getCrossArenaLevelEntity(fDan) == null) {
            return;
        }

        userExtend.getCrossArenaModel().setDan(fDan);
    }

    public void downDan(int season, UserExtend userExtend) {
        int dan = userExtend.getCrossArenaModel().getDan();
        int fDan = dan - 1;
        if(fDan < 1) {
            return;
        }

        userExtend.getCrossArenaModel().setDan(fDan);
    }

    public void updateHistoryDan(int season, int dan, UserExtend userExtend){
        UserExtend.CrossArenaModel crossArenaModel =  userExtend.getCrossArenaModel();
        crossArenaModel.getDanMap().put(season, dan);

        // 转换为treeMap
        TreeMap<Integer, Integer> dataMap = Maps.newTreeMap();
        dataMap.putAll(crossArenaModel.getDanMap());

        // 删除
        while (dataMap.size() > 4) {
            dataMap.pollFirstEntry(); // 删除TreeMap中的第一个条目
        }

        crossArenaModel.setDanMap(dataMap);
    }

    private List<Integer> randRobotHeroIds() {
        List<Integer> result = new ArrayList<>();

        Map<Integer, CrossArenaRobotHeroEntity> robotHero = gameConfigManager.getCrossArenaConfig().getCrossArenaRobotHero();
        Integer[] keys = robotHero.keySet().toArray(new Integer[robotHero.size()]);
        int randKey = keys[RandomUtil.nextInt(keys.length)];
        CrossArenaRobotHeroEntity robotEntity = robotHero.get(randKey);
        result.add(robotEntity.getMemberData1());
        result.add(robotEntity.getMemberData2());
        result.add(robotEntity.getMemberData3());
        result.add(robotEntity.getMemberData4());
        result.add(robotEntity.getMemberData5());

        return result;
    }

    private void initRobots(int num, int serverId, int season, int dan, int group, int team) {
        String key = getGenRobotKey(season, dan, group, team);
        if(!redisService.setIfAbsent(key, DateUtils.getUnixTime(), Duration.ofDays(MAX_SETTLEMENT_TIME))) {
            return;
        }

        CrossArenaRobotEntity robotEntity = gameConfigManager.getCrossArenaConfig().getCrossArenaRobotEntity(dan);
        if(robotEntity == null) {
            return;
        }

        num = Math.min(num, 100);

        for(int i = 0; i < num; i++) {
            long power = RandomUtil.betweenValue(robotEntity.getPowerNum().get(0), robotEntity.getPowerNum().get(1));

            User robot = userService.createRobot(serverId, power);
            UserExtend robotExtend = userExtendDao.getByUserId(robot.getUserId());

            List<Integer> heroIds = this.randRobotHeroIds();
            for (int j = 0; j < heroIds.size(); j++) {
                int heroId = heroIds.get(j);

                boolean has = false;

                List<List<Integer>> initHeroIds = commonService.getGameConfigValueSplitOneArr(801, "\\|");
                for (List<Integer> initHeroId : initHeroIds) {
                    if (initHeroId.contains(heroId)) {
                        has = true;
                        break;
                    }
                }

                if(has) {
                    continue;
                }

                int heroLv = RandomUtil.betweenValue(robotEntity.getHeroLv().get(0), robotEntity.getHeroLv().get(1));
                int heroStar = RandomUtil.betweenValue(robotEntity.getHeroQuality().get(0), robotEntity.getHeroQuality().get(1));

                Hero hero = heroService.createHero(robot.getUserId(), heroId, heroLv, heroStar, 0, 0);
                List<Long> formation = robotExtend.getFormations().get(GameConstant.FORMATION_TYPE_CHAPTER);

                formation.set(j, hero.getRowId());
            }

            userExtendDao.update(robotExtend);

            String score = GameConstant.CROSS_ARENA_INIT_SCORE + "." + (MAX_TIME - DateUtils.getUnixTime());

            String k = getGroupKey(season, dan, group, team);
            redisService.zAdd(k, String.valueOf(robot.getUserId()), Double.parseDouble(score));
            expireKey(k);

            groupHelper.adding(groupHelper.getCrossArenaGroupKey(season, dan, group));
        }
    }

    /**
     * 分组
     */
    public List<Long> grouping(int season, int dan, long userId, int serverId) {
        List<Long> l = Lists.newArrayList();

        String s = redisService.hGet(getGroupIndexKey(season), String.valueOf(userId));
        if(s != null) {
            String[] arr = s.split("#");
            l.add(Long.parseLong(arr[0]));
            l.add(Long.parseLong(arr[1]));
            l.add(Long.parseLong(arr[2]));

            return l;
        }

        CrossArenaLevelEntity crossArenaLevelEntity = gameConfigManager.getCrossArenaConfig().getCrossArenaLevelEntity(dan);

        int teamUserNum = crossArenaLevelEntity.getPlayerNum();
        int interval = crossArenaLevelEntity.getServerNum();

        int serverZone = serverListService.getZoneMark(serverId);

        int group = groupHelper.grouping(serverId, interval);
        group = (serverZone * 1000000) + group;

        int team = groupHelper.indexing(groupHelper.getCrossArenaGroupKey(season, dan, group), teamUserNum);

        // 机器人开关
        if(true && crossArenaLevelEntity.getRobotNum() > 0) {
            initRobots(crossArenaLevelEntity.getRobotNum(), serverId, season, dan, group, team);
        }

        String key = getGroupKey(season, dan, group, team);

        String score = GameConstant.CROSS_ARENA_INIT_SCORE + "." + (MAX_TIME - DateUtils.getUnixTime());
        redisService.zAdd(key, String.valueOf(userId), Double.parseDouble(score));
        expireKey(key);

        long day = DateUtils.getDayOfWeek(DateUtils.getUnixTime());
        // 玩家分组索引
        String k = getGroupIndexKey(getSeason());
        String index = group + "#" + team + "#" + day;
        redisService.hSet(k, String.valueOf(userId), index);
        expireKey(k);

        l.add((long) group);
        l.add((long) team);
        l.add(day);

        return l;
    }

    public int getRank(int season, int dan, long userId) {
        Pair<Integer, Integer> index = this.groupTeam(getTeam(season, userId));
        if(index == null) {
            return 0;
        }

        String key = getGroupKey(season, dan, index.getLeft(), index.getRight());

        Long rank = redisService.zRank(key, String.valueOf(userId));
        if(rank == null) {
            return 0;
        }

        return rank.intValue() + 1;
    }

    public int getRank(int season, int dan, int group, int team, long userId) {
        String key = getGroupKey(season, dan, group, team);

        Long rank = redisService.zRank(key, String.valueOf(userId));
        if(rank == null) {
            return 0;
        }

        return rank.intValue() + 1;
    }

    public int getScore(int season, int dan, long userId) {
        Pair<Integer, Integer> index = this.groupTeam(getTeam(season, userId));
        if(index == null) {
            return 0;
        }

        String key = getGroupKey(season, dan, index.getLeft(), index.getRight());

        double s = redisService.zScoreWithDouble(key, String.valueOf(userId));

        return Double.valueOf(s).intValue();
    }

    public int getScore(int season, int dan, int group, int team, long userId) {
        String key = getGroupKey(season, dan, group, team);

        double s = redisService.zScoreWithDouble(key, String.valueOf(userId));

        return Double.valueOf(s).intValue();
    }


    /**
     * @return group -> team
     */
    public List<Long> getTeam(int season, long userId) {
        String key = getGroupIndexKey(season);
        String index = redisService.hGet(key, String.valueOf(userId));
        if(index == null || index.isBlank()) {
            return null;
        }

        List<Long> l = Lists.newArrayList();
        long group = Integer.parseInt(index.split("#")[0]);
        long team = Integer.parseInt(index.split("#")[1]);
        long day = Integer.parseInt(index.split("#")[2]);

        l.add(group);
        l.add(team);
        l.add(day);

        return l;
    }

    public int getTeamRankCount(int season, int dan, int group, int team) {
        return redisService.zCard(getGroupKey(season, dan, group, team)).intValue();
    }

    public Map<Long, Integer> getTeamListByGroup(int season, int dan, int group, int team) {
        String k = getGroupKey(season, dan, group, team);
        return RankHelper.rankListSimple(k, 0, Integer.MAX_VALUE);
    }

    /**
     * @return userId -> score
     */
    public Pair<Long, Integer> getTeamEle(int season, int dan, int group, int team, int rank) {
        String key = getGroupKey(season, dan, group, team);
        Set<ZSetOperations.TypedTuple<String>> rankData = redisService.zReverseRangeWithScores(key, rank - 1);
        if(rankData == null || rankData.isEmpty()) {
            return null;
        }

        ZSetOperations.TypedTuple<String> data = rankData.iterator().next();

        long userId = data.getValue() == null ? 0L : Long.parseLong(data.getValue());
        int score = Integer.parseInt(String.valueOf(data.getScore()).split("\\.")[0]);

        return Pair.of(userId, score);
    }

    public List<CrossArenaRecordData> getRecords(long userId) {
        String key = getRecordKey(getSeason(), userId);
        Set<String> set = redisService.sMembers(key);
        if(set == null || set.isEmpty()) {
            return null;
        }

        List<CrossArenaRecordData> dataList = new ArrayList<>();
        set.forEach(v -> {
            dataList.add(CrossArenaRecordData.deserialize(v));
        });

        return dataList;
    }

    public List<List<Integer>> getDailyRewardByRank(int dan, int rank) {
        Map<String, List<List<Integer>>> list = gameConfigManager.getCrossArenaDailyReward().get(dan);
        if(list == null || list.isEmpty()) {
            return null;
        }

        for(Map.Entry<String, List<List<Integer>>> entry : list.entrySet()) {
            String[] range = entry.getKey().split("#");
            List<List<Integer>> reward = entry.getValue();

            int start = Integer.parseInt(range[0]);
            int end = Integer.parseInt(range[1]);

            if(rank >= start && rank <= end) {
                return reward;
            }
        }

        return null;
    }

    public List<List<Integer>> getWeekRewardByRank(int dan, int rank) {
        Map<String, List<List<Integer>>> list = gameConfigManager.getCrossArenaWeekReward().get(dan);
        if(list == null || list.isEmpty()) {
            return null;
        }

        for(Map.Entry<String, List<List<Integer>>> entry : list.entrySet()) {
            String[] range = entry.getKey().split("#");
            List<List<Integer>> reward = entry.getValue();

            int start = Integer.parseInt(range[0]);
            int end = Integer.parseInt(range[1]);

            if(rank >= start && rank <= end) {
                return reward;
            }
        }

        return null;
    }

    /**
     * 计算加减分
     */
    public Pair<Integer, Integer> getAddAndCostScore(int rank, int ownerScore, int targetScore) {
        CrossArenaEntity crossArenaEntity = getCrossArenaConfig(rank);
        if(crossArenaEntity == null) {
            return null;
        }

        int addScore = (int) (crossArenaEntity.getBaseWinScore() - Math.ceil((ownerScore - targetScore) * crossArenaEntity.getAddition()));
//        int addScore = (int) (crossArenaEntity.getBaseWinScore() - ((ownerScore - targetScore) * crossArenaEntity.getAddition()));
        if (addScore < crossArenaEntity.getWinScoreSection().get(0)) {
            addScore = crossArenaEntity.getWinScoreSection().get(0);
        } else if (addScore > crossArenaEntity.getWinScoreSection().get(1)) {
            addScore = crossArenaEntity.getWinScoreSection().get(1);
        }

        int costScore = (int) (crossArenaEntity.getBaseLoseScore() + Math.ceil((ownerScore - targetScore) * crossArenaEntity.getAddition()));
//        int costScore = (int) (crossArenaEntity.getBaseLoseScore() + ((ownerScore - targetScore) * crossArenaEntity.getAddition()));
        if (costScore < crossArenaEntity.getLoseScoreSection().get(0)) {
            costScore = crossArenaEntity.getLoseScoreSection().get(0);
        } else if (costScore > crossArenaEntity.getLoseScoreSection().get(1)) {
            costScore = crossArenaEntity.getLoseScoreSection().get(1);
        }

        return Pair.of(addScore, costScore);
    }

    public void saveRecord(long userId, CrossArenaRecordData data) {
        int season = getSeason();

        String key = getRecordKey(season, userId);
        redisService.sAdd(key, data.serialize());
        expireKey(key);
    }

    public CrossArenaEntity getCrossArenaConfig(int rank) {
        Map<Integer, CrossArenaEntity> arenaEntityMap = gameConfigManager.getCrossArenaConfig().getCrossArena();

        CrossArenaEntity arenaEntity = null;
        for (Map.Entry<Integer, CrossArenaEntity> entry : arenaEntityMap.entrySet()) {
            if (rank >= entry.getValue().getRanktop() && rank <= entry.getValue().getRankbuttom()) {
                arenaEntity = entry.getValue();
                break;
            }
        }
        return arenaEntity;
    }

    public List<Pair<Long, Integer>> refreshChallengeList(int season, long userId, int dan, int group, int team) {
        List<Pair<Long, Integer>> userScores = new ArrayList<>();

        int rank = getRank(season, dan, userId);
        int count = getTeamRankCount(season, dan, group, team);

        List<Integer> rankList = getRankListOfChallenge(rank, count);

        // userId -> score
        rankList.forEach(v -> {
            Pair<Long, Integer> data = getTeamEle(season, dan, group, team, v);
            if(data != null) {
                userScores.add(data);
            }
        });

        return userScores;
    }

    public List<Pair<Long, Integer>> getChallengeList(int season, int dan, int group, int team, List<Long> userList) {
        List<Pair<Long, Integer>> userScores = new ArrayList<>();

        Map<Long, Integer> rankList = getTeamListByGroup(season, dan, group, team);
        if(rankList != null) {
            for(Map.Entry<Long, Integer> entry : rankList.entrySet()) {
                long uid = entry.getKey();
                int score = entry.getValue();

                for (Long userId : userList) {
                    if (userId == uid){
                        userScores.add(Pair.of(uid, score));
                    }
                }
            }
        }

        return userScores;
    }

    public List<Integer> getRankListOfChallenge(int userRank, int totalCount) {
        List<Integer> result = new ArrayList<>();
        if (userRank > 0 && userRank <= CHALLENGE_LIST_COUNT) {
            // 自己是前3名
            for (int rank = CHALLENGE_LIST_COUNT; rank > 0; rank --) {
                if (userRank == rank) {
                    // 5 - 10 名随机一个
                    if (totalCount < 10) {
                        result.add(RandomUtil.betweenValue(5, totalCount));
                    } else {
                        result.add(RandomUtil.betweenValue(5, 10));
                    }
                } else {
                    result.add(rank);
                }
            }

            return result;
        }

        Map<Integer, CrossArenaChallengeListRuleEntity> arenaChallengeListRule = gameConfigManager.getCrossArenaConfig().getCrossArenaChallengeListRule();
        for (Map.Entry<Integer, CrossArenaChallengeListRuleEntity> entry : arenaChallengeListRule.entrySet()) {
            List<Integer> rankRange = entry.getValue().getRank();
            int min = rankRange.get(0);
            int max = rankRange.get(1);

            List<Integer> positions = new ArrayList<>();
            positions.add(userRank);
            if (userRank >= min && (userRank <= max || max == -1)) {
                int pos1Rank = getRandPosition(1, userRank, entry.getValue(), positions);
                int pos2Rank = getRandPosition(2, userRank, entry.getValue(), positions);
                int pos3Rank = getRandPosition(3, userRank, entry.getValue(), positions);
//                int pos4Rank = getRandPosition(4, userRank, entry.getValue(), positions);
                int pos5Rank = getRandPosition(5, userRank, entry.getValue(), positions);

                // 排名超出上限
                if (pos5Rank >= totalCount) {
                    int limit = Math.max(1, totalCount - 10);
                    int loopCount = 0;
                    do {
                        loopCount++;
                        pos5Rank = RandomUtil.betweenValue(limit, totalCount);
                    } while (positions.contains(pos5Rank) && loopCount < 30);
                }

                result.add(pos1Rank);
                result.add(pos2Rank);
                result.add(pos3Rank);
//                result.add(pos4Rank);
                result.add(pos5Rank);
                break;
            }

        }

        return result;
    }

    /**
     * 根据规则随机位置
     */
    private static int getRandPosition(int position, int curRank, CrossArenaChallengeListRuleEntity config, List<Integer> positions) {
        int result = curRank;
        int start = 0;
        int end = 0;

        List<Float> posRange = null;
        if (position == 1) {
            posRange = config.getPos1();
        } else if (position == 2) {
            posRange = config.getPos2();
        } else if (position == 3) {
            posRange = config.getPos3();
        } else if (position == 4) {
//            posRange = config.getPos4();
        } else if (position == 5) {
            posRange = config.getPos5();
        }

        if (position <= 4) {
            start = (int) Math.ceil(curRank * posRange.get(0));
            end = (int) Math.ceil(curRank * posRange.get(1));
        } else {
            start = (int) (curRank + posRange.get(0));
            end = (int) (curRank + posRange.get(1));
        }

        result = RandomUtil.betweenValue(start, end);

        if (result == 0) {  // result == 0 时会导致从排行榜获取的是最后一名
            result +=1;
        }

        // 是否有重复
        while (true) {

            if (!positions.contains(result)) {
                positions.add(result);
                break;
            } else {
                result = result + 1;
            }
        }
        return result;
    }

    public Pair<Integer, Integer> groupTeam(List<Long> groupTeamInfo){
        if (groupTeamInfo == null){
            return null;
        }
        int group = groupTeamInfo.get(0).intValue();
        int team = groupTeamInfo.get(1).intValue();
        return Pair.of(group, team);
    }

    public boolean getChallengeLock(int dan, int group, int team) {
        String key = getChallengeLockKey(dan, group, team);
        return redisLock.lock(key, String.valueOf(0));
    }

    public Map<Long, Integer> getUserEnterDays(int season, List<Long> userIds) {
        List<String> userIdStrs = userIds.stream().map(String::valueOf).collect(Collectors.toList());

        List<Object> values = redisService.hmGetAll(getGroupIndexKey(season), new ArrayList<>(userIdStrs));
        Map<Long, Integer> userEnterDays = new HashMap<>();
        for (int i = 0; i < values.size(); i++) {
            Long userId = userIds.get(i);
            if (userId == null) {
                break;
            }

            Object gi = values.get(i);
            if(gi == null) {
                continue;
            }

            String enterDayStr = gi.toString().split("#")[2];
            userEnterDays.put(userId, Integer.valueOf(enterDayStr));
        }
        return userEnterDays;
    }

    public void initSeason() {
        redisService.setIfAbsent(RedisKeys.CROSS_ARENA_SEASON, 1L);
    }

    public int changeSeason() {
        return (int) redisService.incrBy(RedisKeys.CROSS_ARENA_SEASON);
    }

    public int getSeason() {
        return Integer.parseInt(redisService.get(RedisKeys.CROSS_ARENA_SEASON));
    }

    public void cleanSeasonData() {
        redisService.deleteKeysByPrefix("ca:*");
    }

//    public void cleanSeasonData(int season) {
//        redisService.deleteKeysByPrefix("ca:" + season);
//    }

    public String getGroupKey(int season, int dan, int group, int team) {
        return RedisKeys.CROSS_ARENA_BASE + season + RedisKeys.CROSS_ARENA_GROUP + ":" + dan + ":" + group + ":" + team;
    }

    public String getGroupIndexKey(int season) {
        return RedisKeys.CROSS_ARENA_BASE + season + RedisKeys.CROSS_ARENA_GROUP_INDEX;
    }

    public String getRecordKey(int season, long userId) {
        return RedisKeys.CROSS_ARENA_BASE + season + RedisKeys.CROSS_ARENA_RECORD + ":" + userId;
    }

    public String getChallengeLockKey(int dan, int group, int team) {
        return RedisKeys.CROSS_ARENA_BASE + "lk:" + dan + ":" + group + ":" + team;
    }

    public String getGenRobotKey(int season, int dan, int group, int team) {
        return RedisKeys.CROSS_ARENA_BASE + season + RedisKeys.CROSS_ARENA_ROBOT_GEN + ":" + dan + ":" + group + ":" + team;
    }

    public String getWeekSettlementKey(int season, int dan, int group, int team) {
        return getGroupKey(season, dan, group, team) + ":week";
    }

    public String getDaySettlementKey(String groupKey, long endTime) {
        return groupKey + ":day:" + endTime;
    }

    private void expireKey(String key) {
        redisService.expireKey(key, MAX_SETTLEMENT_TIME, TimeUnit.DAYS);
    }
}
