package com.dxx.game.modules.user.handler;

import com.dxx.game.common.server.annotation.ApiHandler;
import com.dxx.game.common.server.annotation.ApiMethod;
import com.dxx.game.common.server.context.RequestContext;
import com.dxx.game.common.server.model.Result;
import com.dxx.game.consts.MsgReqCommand;
import com.dxx.game.dto.UserProto.*;
import com.dxx.game.modules.user.service.UserService;
import com.google.protobuf.Message;
import org.springframework.beans.factory.annotation.Autowired;

@ApiHandler
public class UserHandler {

	@Autowired
	private UserService userService;

	@ApiMethod(command = MsgReqCommand.UserLoginRequest, name = "用户登录", skipAuth = true,
			skipLoginStateCheck = true,
			collectIp = true,
			skipIdempotent = true)
	public Result<UserLoginResponse> login(Message msg) {
		UserLoginRequest params = (UserLoginRequest)msg;
		return userService.loginAction(params, RequestContext.getClientIp());
//		return null;
	}

	@ApiMethod(command = MsgReqCommand.UserGetInfoRequest, name = "重新获取用户数据")
	public Result<UserGetInfoResponse> getInfo(Message msg) {
		UserGetInfoRequest params = (UserGetInfoRequest)msg;
		return userService.getInfoAction(params);
	}

	@ApiMethod(command = MsgReqCommand.UserHeartbeatRequest, name = "客户端心跳", skipIdempotent = true, skipVersionCheck = true, skipLoginStateCheck = true)
	public Result<UserHeartbeatResponse> heartbeat(Message msg) {
		UserHeartbeatRequest params = (UserHeartbeatRequest)msg;
		return userService.heartbeatAction(params);
	}

	@ApiMethod(command = MsgReqCommand.UserUpdateSystemMaskRequest, name = "更新系统掩码")
	public Result<UserUpdateSystemMaskResponse> updateSystemMask(Message msg) {
		UserUpdateSystemMaskRequest params = (UserUpdateSystemMaskRequest)msg;
		return userService.updateSystemMaskAction(params);
	}

	@ApiMethod(command = MsgReqCommand.UserUpdateGuideMaskRequest, name = "更新新手引导步骤")
	public Result<UserUpdateGuideMaskResponse> updateGuideMask(Message msg) {
		UserUpdateGuideMaskRequest params = (UserUpdateGuideMaskRequest)msg;
		return userService.updateGudeMaskAction(params);
	}

	@ApiMethod(command = MsgReqCommand.UserUpdateInfoRequest, name = "更新昵称/头像/头像框")
	public Result<UserUpdateInfoResponse> updateInfo(Message msg) {
		UserUpdateInfoRequest params = (UserUpdateInfoRequest)msg;
		return userService.updateInfoAction(params);
	}

	@ApiMethod(command = MsgReqCommand.UserGetOtherPlayerInfoRequest, name = "获取其他玩家信息")
	public Result<UserGetOtherPlayerInfoResponse> getOtherInfo(Message msg) {
		UserGetOtherPlayerInfoRequest params = (UserGetOtherPlayerInfoRequest) msg;
		return userService.getOtherInfo(params);
	}

	@ApiMethod(command = MsgReqCommand.UserGetCityInfoRequest, name = "获取玩家主城信息")
	public Result<UserGetCityInfoResponse> getCityInfo(Message msg) {
		UserGetCityInfoRequest params = (UserGetCityInfoRequest) msg;
		return userService.getCityInfo(params);
	}

	@ApiMethod(command = MsgReqCommand.UserHeartbeatSyncRequest, name = "心跳同步", skipIdempotent = true, skipVersionCheck = true, skipLoginStateCheck = true)
	public Result<UserHeartbeatSyncResponse> heartbeatSync(Message msg) {
		UserHeartbeatSyncRequest params = (UserHeartbeatSyncRequest) msg;
		return userService.userHeartbeatSync(params);
	}

	@ApiMethod(command = MsgReqCommand.UserGetBattleReportRequest, name = "获得战报")
	public Result<UserGetBattleReportResponse> getBattleReport(Message msg) {
		UserGetBattleReportRequest params = (UserGetBattleReportRequest) msg;
		return userService.getBattleReport(params);
	}
	@ApiMethod(command = MsgReqCommand.UserOpenModelRequest, name = "记录账号开启模块")
	public Result<UserOpenModelResponse> userOpenModel(Message msg) {
		UserOpenModelRequest params = (UserOpenModelRequest) msg;
		return userService.userOpenModel(params);
	}

	@ApiMethod(command = MsgReqCommand.UserSetFormationByTypeRequest, name = "设置阵容信息")
	public Result<UserSetFormationByTypeResponse> userSetFormationByType(Message msg) {
		UserSetFormationByTypeRequest params = (UserSetFormationByTypeRequest) msg;
		return userService.userSetFormationByType(params);
	}

	@ApiMethod(command = MsgReqCommand.UserGetFormationByTypeRequest, name = "获取阵容信息")
	public Result<UserGetFormationByTypeResponse> userGetFormationByType(Message msg) {
		UserGetFormationByTypeRequest params = (UserGetFormationByTypeRequest) msg;
		return userService.userGetFormationByType(params);
	}

	@ApiMethod(command = MsgReqCommand.UserTriggerRequest, name = "触发器")
	public Result<UserTriggerResponse> userTrigger(Message msg) {
		UserTriggerRequest params = (UserTriggerRequest)msg;
		return userService.userTrigger(params);
	}

	@ApiMethod(command = MsgReqCommand.UserTicketInfoRequest, name = "门票信息")
	public Result<UserTicketInfoResponse> userTicketInfo(Message msg) {
		UserTicketInfoRequest params = (UserTicketInfoRequest)msg;
		return userService.userTicketInfo(params);
	}

	@ApiMethod(command = MsgReqCommand.UserExchangeItemRequest, name = "兑换道具")
	public Result<UserExchangeItemResponse> userExchangeItem(Message msg) {
		UserExchangeItemRequest params = (UserExchangeItemRequest)msg;
		return userService.userExchangeItem(params);
	}

	@ApiMethod(command = MsgReqCommand.UserSyncPowerRequest, name = "同步战力")
	public Result<UserSyncPowerResponse> syncPower(Message msg) {
		UserSyncPowerRequest params = (UserSyncPowerRequest) msg;
		return userService.syncPower(params);
	}

	@ApiMethod(command = MsgReqCommand.UserHabbyMailBindRequest, name = "绑定海比账号")
	public Result<UserHabbyMailBindResponse> userHabbyMailBindRequest(UserHabbyMailBindRequest msg) {
		return userService.UserHabbyMailBindRequest(msg);
	}

	@ApiMethod(command = MsgReqCommand.UserHabbyMailRewardRequest, name = "领取海比绑定账号奖励")
	public Result<UserHabbyMailRewardResponse> UserHabbyMailRewardRequest(UserHabbyMailRewardRequest msg) {
		return userService.UserHabbyMailRewardRequest(msg);
	}

}


















