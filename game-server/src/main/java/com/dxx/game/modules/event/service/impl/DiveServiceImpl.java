package com.dxx.game.modules.event.service.impl;

import com.amazonaws.xray.spring.aop.XRayEnabled;
import com.dxx.game.common.aws.dynamodb.transaction.annotation.DynamoDBTransactional;
import com.dxx.game.common.server.context.RequestContext;
import com.dxx.game.common.server.model.Result;
import com.dxx.game.common.utils.CommonUtils;
import com.dxx.game.config.GameConfigManager;
import com.dxx.game.config.entity.event.EventEntity;
import com.dxx.game.config.entity.eventdive.DiveBaseEntity;
import com.dxx.game.consts.*;
import com.dxx.game.dao.dynamodb.model.Event;
import com.dxx.game.dao.dynamodb.model.Item;
import com.dxx.game.dao.dynamodb.model.User;
import com.dxx.game.dao.dynamodb.model.event.Dive;
import com.dxx.game.dao.dynamodb.repository.EventDao;
import com.dxx.game.dao.dynamodb.repository.ItemDao;
import com.dxx.game.dto.DiveProto;
import com.dxx.game.modules.common.support.CommonHelper;
import com.dxx.game.modules.event.consts.EDiveGrid;
import com.dxx.game.modules.event.service.DiveService;
import com.dxx.game.modules.event.service.EventService;
import com.dxx.game.modules.event.support.DiveSupport;
import com.dxx.game.modules.mail.MailService;
import com.dxx.game.modules.reward.model.Reward;
import com.dxx.game.modules.reward.result.RewardResultSet;
import com.dxx.game.modules.reward.service.DropService;
import com.dxx.game.modules.reward.service.RewardService;
import com.dxx.game.modules.task.support.TaskSupport;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.ImmutableTriple;
import org.apache.commons.lang3.tuple.Triple;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
@XRayEnabled
public class DiveServiceImpl implements DiveService {
    @Autowired
    private EventService eventService;
    @Autowired
    private EventDao eventDao;
    @Autowired
    private DiveSupport diveSupport;
    @Autowired
    private RewardService rewardService;
    @Autowired
    private ItemDao itemDao;
    @Autowired
    private DropService dropService;
    @Autowired
    private GameConfigManager gameConfigManager;
    @Autowired
    private MailService mailService;
    @Autowired
    private TaskSupport taskSupport;

    @DynamoDBTransactional
    @Override
    public Result<DiveProto.DiveOnOpenResponse> onOpen(DiveProto.DiveOnOpenRequest params) {
        long userId = RequestContext.getUserId();
        User user = RequestContext.getUser();

        EventEntity entity = eventService.getOpenEventByType(user, EventType.DIVE);
        if (entity == null) {
            return Result.Error(ErrorCode.ACTIVITY_NOT_OPEN);
        }

        Event event = eventDao.getByUserId(userId);
        if (event == null) {
            return Result.Error(ErrorCode.PARAMS_ERROR);
        }

        Dive model = getModel(event);

        DiveBaseEntity baseEntity = diveSupport.getDiveBaseEntity(model.getEventId());
        if (baseEntity == null) {
            return Result.Error(ErrorCode.CONFIG_NOT_EXIST);
        }

        List<List<Integer>> rewards = Lists.newArrayList();

        if (model.isFirst()) {
            model.setFirst(false);
            rewards.add(Lists.newArrayList(Lists.newArrayList(baseEntity.getDiveItem(), baseEntity.getDefaultDive())));
        }

        RewardResultSet rewardResultSet = null;
        if (!rewards.isEmpty()) {
            rewardResultSet = rewardService.executeRewards(userId, rewards);
        }

        update(event, model);

        DiveProto.DiveOnOpenResponse.Builder response = DiveProto.DiveOnOpenResponse.newBuilder();
        if (rewardResultSet != null) {
            response.setCommonData(CommonHelper.buildCommonData(rewardResultSet));
        }

        if (diveSupport.handelFill(model)) {
            update(event, model);
        }

        response.setDiveDto(buildDto(model));

        return Result.Success(response.build());
    }

    @Override
    public Dive getModel(Event event) {
        check(event);
        return event.getDive();
    }

    private void check(Event event) {
        if (event == null) {
            return;
        }

        User user = RequestContext.getUser();

        Dive model = event.getDive();

        if (model != null) {
            int eventId = model.getEventId();

            boolean isEnd = eventService.checkEndById(user, eventId);
            if (isEnd) {
                // 补发奖励
                if (!model.isMail()) {
                    exchangeMail(user.getUserId(), model);
                }
            }
        }

        EventEntity eventEntity = eventService.getOpenEventByType(user, EventType.DIVE);
        if (eventEntity != null) {
            if (model == null || model.getEventId() != eventEntity.getId()) {
                eventService.clear(event, RequestContext.getUserId(), EventType.DIVE,  eventEntity.getId());
                model = initModel(eventEntity.getId());
                taskSupport.triggerTask(RequestContext.getUserId(), TaskType.DIVE_LEVEL_NUM,model.getDepth());
            }
        }

        update(event, model);
    }

    private Dive initModel(int eventId) {
        Dive model = new Dive();

        model.setEventId(eventId);

        model.setFirst(true);  // 第一次进入活动
        model.setMail(false);

        model.setAcc(Lists.newArrayList());

        model.setDepth(1);
        model.setLineList(Lists.newArrayList());
        model.setSpecialLog(Maps.newHashMap());
        model.setContinuousLog(Maps.newHashMap());
        model.setVarecLog(Maps.newHashMap());
        model.setContinuousPLog(Maps.newHashMap());
        model.setVarecPLog(Maps.newHashMap());

        // 初始化地图
        DiveBaseEntity entity = diveSupport.getDiveBaseEntity(eventId);
        if (entity != null) {
            diveSupport.initStartMap(model, entity);
        }

        eventService.commitRank(EventType.DIVE, RequestContext.getUser(), model.getDepth());
        return model;
    }

    private void update(Event event, Dive model) {
        if (model == null) {
            return;
        }

        event.setDive(model);
        eventDao.updateDive(event);
    }

    @Override
    public DiveProto.DiveDto buildDto(Dive model) {
        int eventId = model.getEventId();

        DiveBaseEntity baseEntity = diveSupport.getDiveBaseEntity(eventId);

        DiveProto.DiveDto.Builder result = DiveProto.DiveDto.newBuilder();
        result.setEventId(eventId);
        result.setUserDepth(model.getDepth());

        // 配置数据
        result.setConfigDto(diveSupport.buildConfigDto(baseEntity));

        // 个人累计奖励
        result.addAllUserAccRewards(diveSupport.buildUserAccRewardDtoList(model, baseEntity));

        // 地图
        result.addAllLineDtos(diveSupport.buildLineDtos(model, baseEntity));

        return result.build();
    }

    private void exchangeMail(long userId, Dive model) {
        DiveBaseEntity baseEntity = diveSupport.getDiveBaseEntity(model.getEventId());

        // 清理的道具ID
        List<List<Integer>> mailItems = baseEntity.getMailItems();
        List<Integer> clearItemIds = mailItems.stream().map(l -> l.get(0)).collect(Collectors.toList());
        Map<Integer, Integer> exchangCoins = mailItems.stream().collect(Collectors.toMap(l -> l.get(0), l -> l.get(1)));

        int coins = 0;

        // 删除道具
        List<Item> clearItems = itemDao.queryByItemIds(userId, clearItemIds);
        for (Item clearItem : clearItems) {
            if (clearItem.getCount() > 0) {
                // 换成金币
                Integer ratio = exchangCoins.get(clearItem.getItemId());
                if (ratio != null) {
                    int exchangeNum = clearItem.getCount() * ratio;
                    coins += Math.max(0, exchangeNum);
                }
                log.info("DiveDeleteItem eventId={} itemId={} count={}", model.getEventId(), clearItem.getItemId(), clearItem.getCount());
                clearItem.setCount(0);
            }
            itemDao.delete(clearItem);  // 清空
        }

        // 邮件奖励
        List<List<Integer>> rewards = new ArrayList<>();

        // 累计奖励
        int accProgress = model.getDepth();
        if (accProgress > 0) {
            for (List<Integer> list : baseEntity.getBigReward()) {
                int configNeed = list.get(0);
                if (accProgress >= configNeed && !model.getAcc().contains(configNeed)) {
                    rewards.addAll(dropService.dropRewardsConfig(list.get(1)));
                    model.getAcc().add(configNeed);
                }
            }
        }

        if (coins > 0) {
            rewards.add(Lists.newArrayList(RewardResourceType.COINS.getValue(), coins));
        }

        if (!rewards.isEmpty()) {
            String mailUniqueId = userId + "_dive_" + model.getEventId();
            String tmpId = gameConfigManager.getDiveMail(baseEntity);

            mailService.createMail(userId, tmpId, mailUniqueId, rewards, MailSourceType.DIVE, model.getEventId());
        }

        model.setMail(true);
    }

    @DynamoDBTransactional
    @Override
    public Result<DiveProto.DiveBuyItemResponse> buyItem(DiveProto.DiveBuyItemRequest params) {
        long userId = RequestContext.getUserId();

        Integer buyNum = params.getBuyCount();
        if(!CommonUtils.checkUseCountLimit(buyNum)) {
            return Result.Error(ErrorCode.PARAMS_ERROR);
        }

        Event event = eventDao.getByUserId(userId);
        if (event == null) {
            return Result.Error(ErrorCode.QUERY_USER_DATA_ERROR);
        }

        Dive model = event.getDive();
        if (model == null) {
            return Result.Error(ErrorCode.QUERY_USER_DATA_ERROR);
        }

        DiveBaseEntity baseEntity = diveSupport.getDiveBaseEntity(model.getEventId());
        if (baseEntity == null) {
            return Result.Error(ErrorCode.CONFIG_NOT_EXIST);
        }

        Integer buyItemId = params.getButItemId();
        if (buyItemId == null) {
            buyItemId = baseEntity.getDiveItem();
        }

        if (buyItemId != baseEntity.getDiveItem() && buyItemId != baseEntity.getDivePropA() && buyItemId != baseEntity.getDivePropB()) {
            return Result.Error(ErrorCode.PARAMS_ERROR);
        }

        List<List<Integer>> rewards = Lists.newArrayList();
        rewards.add(Lists.newArrayList(buyItemId, buyNum));
        int price = getBuyPrice(baseEntity, buyItemId);
        rewards.add(Lists.newArrayList(RewardResourceType.DIAMONDS.getValue(), -buyNum * price));

        RewardResultSet rewardResultSet = rewardService.executeRewards(userId, rewards);
        if (rewardResultSet.isFailed()) {
            return Result.Error(rewardResultSet.getResultCode());
        }

        DiveProto.DiveBuyItemResponse.Builder resp = DiveProto.DiveBuyItemResponse.newBuilder();
        resp.setCommonData(CommonHelper.buildCommonData(rewardResultSet));

        return Result.Success(resp.build());
    }

    @DynamoDBTransactional
    @Override
    public Result<DiveProto.DiveAccRewardResponse> accReward(DiveProto.DiveAccRewardRequest params) {
        int accType = params.getAccType();
        if (accType != 1 && accType != 2) {
            return Result.Error(ErrorCode.PARAMS_ERROR);
        }

        int need = params.getNeed();
        if (need <= 0) {
            return Result.Error(ErrorCode.PARAMS_ERROR);
        }

        long userId = RequestContext.getUserId();

        Event event = eventDao.getByUserId(userId);
        if (event == null) {
            return Result.Error(ErrorCode.QUERY_USER_DATA_ERROR);
        }

        Dive model = event.getDive();
        if (model == null) {
            return Result.Error(ErrorCode.QUERY_USER_DATA_ERROR);
        }

        DiveBaseEntity baseEntity = diveSupport.getDiveBaseEntity(model.getEventId());
        if (baseEntity == null) {
            return Result.Error(ErrorCode.CONFIG_NOT_EXIST);
        }

        // 个人累计奖励
        return handleUserAccrReward(userId, need, event, model, baseEntity);
    }

    @DynamoDBTransactional
    @Override
    public Result<DiveProto.DiveShineResponse> shine(DiveProto.DiveShineRequest params) {

        int shineDepth = params.getDepth();
        int shineIndex = params.getIndex();
        if (shineDepth <= 0 || shineIndex < 0) {
            return Result.Error(ErrorCode.DIVE_INDEX_ERROR);
        }

        long userId = RequestContext.getUserId();

        Event event = eventDao.getByUserId(userId);
        if (event == null) {
            return Result.Error(ErrorCode.QUERY_USER_DATA_ERROR);
        }

        Dive model = event.getDive();
        if (model == null) {
            return Result.Error(ErrorCode.QUERY_USER_DATA_ERROR);
        }

        if (diveSupport.isScreenOut(model, shineDepth) || !diveSupport.isValidIndex(shineIndex)) {
            return Result.Error(ErrorCode.DIVE_INDEX_ERROR);
        }

        Dive.DiveGrid diveGrid = diveSupport.getDiveGrid(model, shineDepth, shineIndex);
        if (diveGrid == null) {
            return Result.Error(ErrorCode.QUERY_USER_DATA_ERROR);
        }

        if (diveGrid.getType() == EDiveGrid.ePass.getType() || diveGrid.getType() == EDiveGrid.eVarecCenter.getType()) {
            return Result.Error(ErrorCode.DIVE_GRID_TYPE_ERROR);
        }

        // 没点亮
        if (!diveSupport.isLight(model, shineDepth, shineIndex)) {
            return Result.Error(ErrorCode.DIVE_GRID_UN_LIGHT);
        }

        DiveBaseEntity baseEntity = diveSupport.getDiveBaseEntity(model.getEventId());
        if (baseEntity == null) {
            return Result.Error(ErrorCode.CONFIG_NOT_EXIST);
        }

        EDiveGrid diveGridType = diveGrid.getEDiveGridType();
        boolean consumeItem = !diveGridType.equals(EDiveGrid.eContinuousRewards);

        Item diveItem = itemDao.getByItemId(userId, baseEntity.getDiveItem());
        if (consumeItem) {
            if (diveItem == null || diveItem.getCount() < 1) {
                return Result.Error(ErrorCode.ITEM_IS_NOT_ENOUGH);
            }
        }

        Triple<Integer, Integer, Dive.DiveGrid> triple = ImmutableTriple.of(shineDepth, shineIndex, diveGrid);

        List<List<Integer>> rewards = Lists.newArrayList();

        DiveProto.DiveShineResponse.Builder resp = DiveProto.DiveShineResponse.newBuilder();

        if (consumeItem) {
            rewards.add(Lists.newArrayList(baseEntity.getDiveItem(), -1));
        }

        // 泡泡
        if (diveGridType.isPop()) {
            if (diveGrid.getShark() != null) {
                diveGrid.setShark(diveGrid.getShark() - 1);
                if (diveGrid.getShark() <= 0) {
                    diveGrid.setShark(null);
                } else {
                    // 鲨鱼逃窜
                    Triple<Integer, Integer, Dive.DiveGrid> sharkTriple = diveSupport.findSharkGrid(model, shineDepth, shineIndex, diveGrid);
                    if (sharkTriple != null) {
                        resp.setSharkGrid(diveSupport.buildDiveGridDto(sharkTriple, baseEntity));
                    } else {
                        // 无处可逃 直接死
                        diveGrid.setShark(null);
                    }
                }
            }

            if (diveGrid.getReward() != null) {
                rewards.add(diveGrid.getReward());
                model.setRewardAcc(model.getRewardAcc() + 1);
            } else {
                List<Integer> reward = diveSupport.getSpecialItemReward(diveGridType, baseEntity);
                if (reward != null) {
                    rewards.add(reward);
                    model.setRewardAcc(model.getRewardAcc() + 1);
                }
            }

            changeGirdToPass(diveGrid);
            resp.setShineGrid(diveSupport.buildDiveGridDto(triple, baseEntity));
        } else if (diveGrid.getType() == EDiveGrid.eContinuousRewards.getType()) {
            // 连续奖励计数-1
            if (diveGrid.getValue() != null && diveGrid.getValue() > 0) {
                diveGrid.setValue(Math.max(0, diveGrid.getValue() - 1));
                List<Integer> reward = diveSupport.randomContinousReward(model);
                rewards.add(reward);

                resp.setShineGrid(diveSupport.buildDiveGridDto(triple, baseEntity));
            }
        } else if (diveGrid.getType() == EDiveGrid.eVarec.getType()) {
            if (diveGrid.getReward() != null) {
                rewards.add(diveGrid.getReward());
                List<Triple<Integer, Integer, Dive.DiveGrid>> aroundGrids = diveSupport.getAroundGrid(model, shineDepth, shineIndex);
                for (Triple<Integer, Integer, Dive.DiveGrid> around : aroundGrids) {
                    Dive.DiveGrid grid = around.getRight();
                    if (grid.getType() == EDiveGrid.eVarecCenter.getType()) {
                        if (grid.getValue() != null && grid.getValue() > 0) {
                            grid.setValue(grid.getValue() - 1);
                            resp.setVarecGrid(diveSupport.buildDiveGridDto(around, baseEntity));
                        }
                    }
                }

                diveGrid.setReward(null);
            }

            // 标记点击过
            diveGrid.setValue(1);

            resp.setShineGrid(diveSupport.buildDiveGridDto(triple, baseEntity));
        } else if (diveGrid.getType() == EDiveGrid.eIce.getType()) {
            Integer ice = diveGrid.getValue();
            if (ice != null) {
                ice = Math.max(0, ice - 1);
                if (ice <= 0) {
                    changeGirdToPass(diveGrid);
                } else {
                    diveGrid.setValue(ice);
                }
            }

            resp.setShineGrid(diveSupport.buildDiveGridDto(triple, baseEntity));
        }

        List<Reward> parseRewards = rewardService.parseRewards(rewards, false);
        for (Reward reward : parseRewards) {
            if (reward.getCount() <= 0)
                continue;
            if (diveGridType.isPop() && isFilterReward(reward.getConfigId(), baseEntity))
                continue;
            resp.addRewardDto(CommonHelper.buildRewardDto(Lists.newArrayList(reward.getConfigId(), reward.getCount())));
        }

        rewardService.combineRewards(parseRewards);
        RewardResultSet rewardResultSet = rewardService.executeRewards(userId, parseRewards);
        if (rewardResultSet.isFailed()) {
            return Result.Error(rewardResultSet.getResultCode());
        }

        diveSupport.buffLine(model, baseEntity);

        // 下潜
        if (diveGrid.getType() == EDiveGrid.ePass.getType() && diveSupport.isDiveDepth(model, shineDepth)) {
            Dive.DiveLine diveLine = diveSupport.findDiveDepth(model);
            if (diveLine != null && diveLine.getDepeth() > model.getDepth()) {
                //任务
                taskSupport.triggerTask(userId, TaskType.DIVE_LEVEL_NUM, diveLine.getDepeth() - model.getDepth());
                model.setDepth(diveLine.getDepeth());
                eventService.commitRank(EventType.DIVE, RequestContext.getUser(), model.getDepth());
            }
        }

        // 移除屏幕外的
        for (Iterator<Dive.DiveLine> iterator = model.getLineList().iterator(); iterator.hasNext(); ) {
            Dive.DiveLine remLine = iterator.next();
            if (diveSupport.isScreenUpOut(model, remLine.getDepeth())) {
                iterator.remove();
            }
        }

        // 累计消耗水母
        model.setConsumeCnt(model.getConsumeCnt() + 1);

        update(event, model);

        DiveProto.DiveDto diveDto = buildDto(model);
        resp.setDiveDto(diveDto);
        resp.setCommonData(CommonHelper.buildCommonData(rewardResultSet));

        return Result.Success(resp.build());
    }

    @DynamoDBTransactional
    @Override
    public Result<DiveProto.DiveUsePropResponse> shineItem(DiveProto.DiveUsePropRequest params) {

        int shineDepth = params.getDepth();
        int shineIndex = params.getIndex();
        if (shineDepth <= 0 || shineIndex < 0) {
            return Result.Error(ErrorCode.DIVE_INDEX_ERROR);
        }

        int itemType = params.getItemType();
        if (itemType > 1) {
            return Result.Error(ErrorCode.PARAMS_ERROR);
        }

        long userId = RequestContext.getUserId();

        Event event = eventDao.getByUserId(userId);
        if (event == null) {
            return Result.Error(ErrorCode.QUERY_USER_DATA_ERROR);
        }

        Dive model = event.getDive();
        if (model == null) {
            return Result.Error(ErrorCode.QUERY_USER_DATA_ERROR);
        }

        if (diveSupport.isScreenOut(model, shineDepth) || !diveSupport.isValidIndex(shineIndex)) {
            return Result.Error(ErrorCode.DIVE_INDEX_ERROR);
        }

        Dive.DiveGrid diveGrid = diveSupport.getDiveGrid(model, shineDepth, shineIndex);
        if (diveGrid == null) {
            return Result.Error(ErrorCode.QUERY_USER_DATA_ERROR);
        }

        if (!diveGrid.getEDiveGridType().isPass()) {
            return Result.Error(ErrorCode.DIVE_GRID_TYPE_ERROR);
        }

        DiveBaseEntity baseEntity = diveSupport.getDiveBaseEntity(model.getEventId());
        if (baseEntity == null) {
            return Result.Error(ErrorCode.CONFIG_NOT_EXIST);
        }

        int consumeItemId = itemType == 0 ? baseEntity.getDivePropA() : baseEntity.getDivePropB();
        Item diveItem = itemDao.getByItemId(userId, consumeItemId);
        if (diveItem == null || diveItem.getCount() < 1) {
            return Result.Error(ErrorCode.ITEM_IS_NOT_ENOUGH);
        }

        List<List<Integer>> rewards = Lists.newArrayList();
        rewards.add(Lists.newArrayList(consumeItemId, -1));

        DiveProto.DiveUsePropResponse.Builder resp = DiveProto.DiveUsePropResponse.newBuilder();

        List<Triple<Integer, Integer, Dive.DiveGrid>> bombGridList;
        // 手电筒
        if (itemType == 0) {
            bombGridList = diveSupport.getShineGirdList(DiveSupport.SHINE_TYPE_PROP_A, model, shineDepth, shineIndex);
        } else {
            // 炸弹
            bombGridList = diveSupport.getShineGirdList(DiveSupport.SHINE_TYPE_PROP_B, model, shineDepth, shineIndex);
        }

        List<Triple<Integer, Integer, Dive.DiveGrid>> shineGridList = Lists.newArrayList();
        List<Triple<Integer, Integer, Dive.DiveGrid>> sharkGridList = Lists.newArrayList();
        List<Triple<Integer, Integer, Dive.DiveGrid>> varecGridList = Lists.newArrayList();

        for (Triple<Integer, Integer, Dive.DiveGrid> triple : bombGridList) {
            Dive.DiveGrid shineGrid = triple.getRight();
            EDiveGrid eDiveGrid = EDiveGrid.getEnumByType(shineGrid.getType());
            if (eDiveGrid.getType() == EDiveGrid.ePass.getType())
                continue;

            switch (eDiveGrid) {
                case ePop:
                    bombPop(model, triple, rewards, shineGridList, sharkGridList, baseEntity);
                    break;
                case eIce:
                    bombIce(triple, shineGridList, sharkGridList);
                    break;
                case eContinuousRewards:
                    bombContinuousRewards(model, triple, rewards, shineGridList);
                    break;
                case eVarec:
                    bombVarec(model, triple, rewards, shineGridList, varecGridList);
                    break;
                case eDiveItemPop:
                    bombPop(model, triple, rewards, shineGridList, sharkGridList, baseEntity);
                    break;
                case ePropAPop:
                    bombPop(model, triple, rewards, shineGridList, sharkGridList, baseEntity);
                    break;
                case ePropBPop:
                    bombPop(model, triple, rewards, shineGridList, sharkGridList, baseEntity);
                    break;
                case eShellAPop:
                    bombPop(model, triple, rewards, shineGridList, sharkGridList, baseEntity);
                    break;
                case eShellBPop:
                    bombPop(model, triple, rewards, shineGridList, sharkGridList, baseEntity);
                    break;
                case eSPReward1Pop:
                    bombPop(model, triple, rewards, shineGridList, sharkGridList, baseEntity);
                    break;
                default:
                    break;
            }
        }

        boolean findDiveDepth = false;
        for (Triple<Integer, Integer, Dive.DiveGrid> triple : shineGridList) {
            Dive.DiveGrid shineGrid = triple.getRight();
            int depth = triple.getLeft();
            if (!diveSupport.isDiveDepth(model, depth))
                continue;

            EDiveGrid eDiveGrid = EDiveGrid.getEnumByType(shineGrid.getType());
            if (eDiveGrid.isPass()) {
                findDiveDepth = true;
                break;
            }
        }

        diveSupport.buffLine(model, baseEntity);

        // 下潜
        if (findDiveDepth) {
            Dive.DiveLine diveLine = diveSupport.findDiveDepth(model);
            if (diveLine != null && diveLine.getDepeth() > model.getDepth()) {
                //任务
                taskSupport.triggerTask(userId, TaskType.DIVE_LEVEL_NUM, diveLine.getDepeth() - model.getDepth());
                model.setDepth(diveLine.getDepeth());
                eventService.commitRank(EventType.DIVE, RequestContext.getUser(), model.getDepth());
            }
        }

        // 移除屏幕外的
        for (Iterator<Dive.DiveLine> iterator = model.getLineList().iterator(); iterator.hasNext(); ) {
            Dive.DiveLine remLine = iterator.next();
            if (diveSupport.isScreenUpOut(model, remLine.getDepeth())) {
                iterator.remove();
            }
        }

        List<Reward> parseRewards = rewardService.parseRewards(rewards, false);
        for (Reward reward : parseRewards) {
            if (reward.getCount() <= 0)
                continue;
            resp.addRewardDto(CommonHelper.buildRewardDto(Lists.newArrayList(reward.getConfigId(), reward.getCount())));
        }

        rewardService.combineRewards(parseRewards);
        RewardResultSet rewardResultSet = rewardService.executeRewards(userId, parseRewards);
        if (rewardResultSet.isFailed()) {
            return Result.Error(rewardResultSet.getResultCode());
        }

        update(event, model);

        DiveProto.DiveDto diveDto = buildDto(model);
        resp.setCommonData(CommonHelper.buildCommonData(rewardResultSet));
        resp.setDiveDto(diveDto);

        for (Triple<Integer, Integer, Dive.DiveGrid> triple : shineGridList) {
            resp.addShineGrid(diveSupport.buildDiveGridDto(triple, baseEntity));
        }

        for (Triple<Integer, Integer, Dive.DiveGrid> triple : sharkGridList) {
            resp.addSharkGrid(diveSupport.buildDiveGridDto(triple, baseEntity));
        }

        for (Triple<Integer, Integer, Dive.DiveGrid> triple : varecGridList) {
            resp.addVarecGrid(diveSupport.buildDiveGridDto(triple, baseEntity));
        }

        return Result.Success(resp.build());
    }

    @DynamoDBTransactional
    @Override
    public Result<DiveProto.DiveAllAccRewardResponse> allAcc(DiveProto.DiveAllAccRewardRequest params) {

        long userId = RequestContext.getUserId();

        Event event = eventDao.getByUserId(userId);
        if (event == null) {
            return Result.Error(ErrorCode.QUERY_USER_DATA_ERROR);
        }

        Dive model = event.getDive();
        if (model == null) {
            return Result.Error(ErrorCode.QUERY_USER_DATA_ERROR);
        }

        DiveBaseEntity baseEntity = diveSupport.getDiveBaseEntity(model.getEventId());
        if (baseEntity == null) {
            return Result.Error(ErrorCode.CONFIG_NOT_EXIST);
        }

        DiveProto.DiveAllAccRewardResponse.Builder resp = DiveProto.DiveAllAccRewardResponse.newBuilder();
        List<List<Integer>> rewards = Lists.newArrayList();

        for (List<Integer> list : baseEntity.getBigReward()) {
            int configNeed = list.get(0);
            if (model.getDepth() < configNeed) {
                continue;
            }

            if (model.getAcc().contains(configNeed)) {
                continue;
            }

            int rewardId = list.get(1);
            rewards.addAll(dropService.dropRewardsConfig(rewardId));

            model.getAcc().add(configNeed);
            resp.addNeeds(configNeed);
        }

        RewardResultSet rewardResultSet = null;
        if (!rewards.isEmpty()) {
            rewardResultSet = rewardService.executeRewards(userId, rewards);
            if (rewardResultSet.isFailed()) {
                return Result.Error(rewardResultSet.getResultCode());
            }
        }

        update(event, model);

        resp.setCommonData(rewardResultSet == null ? CommonHelper.buildCommonData() : CommonHelper.buildCommonData(rewardResultSet));
        resp.setDto(buildDto(model));

        return Result.Success(resp.build());
    }

    private Result<DiveProto.DiveAccRewardResponse> handleUserAccrReward(long userId, int need, Event event, Dive model, DiveBaseEntity baseEntity) {
        if (model.getAcc().contains(need)) {
            return Result.Error(ErrorCode.TASK_ALREADY_RECEIVE);
        }

        int rewardId = 0;
        for (List<Integer> list : baseEntity.getBigReward()) {
            int configNeed = list.get(0);
            if (configNeed == need) {
                rewardId = list.get(1);
                break;
            }
        }

        if (rewardId <= 0) {
            return Result.Error(ErrorCode.CONFIG_NOT_EXIST);
        }

        if (model.getDepth() < need) {
            return Result.Error(ErrorCode.TASK_NOT_FINISH);
        }

        List<List<Integer>> rewards = dropService.dropRewardsConfig(rewardId);
        RewardResultSet rewardResultSet = rewardService.executeRewards(userId, rewards);
        if (rewardResultSet.isFailed()) {
            return Result.Error(rewardResultSet.getResultCode());
        }

        model.getAcc().add(need);
        update(event, model);

        DiveProto.DiveAccRewardResponse.Builder resp = DiveProto.DiveAccRewardResponse.newBuilder();
        resp.setCommonData(CommonHelper.buildCommonData(rewardResultSet));
        resp.setDiveDto(buildDto(model));

        return Result.Success(resp.build());
    }

    private int getBuyPrice(DiveBaseEntity baseEntity, int buyItemId) {
        if (buyItemId == baseEntity.getDiveItem()) {
            return baseEntity.getDivePrice();
        } else if (buyItemId == baseEntity.getDivePropA()) {
            return baseEntity.getDivePrice1();
        } else {
            return baseEntity.getDivePrice2();
        }
    }

    private void changeGirdToPass(Dive.DiveGrid grid) {
        grid.setType(EDiveGrid.ePass.getType());
        grid.setValue(null);
        grid.setReward(null);
    }

    private boolean isFilterReward(int configId, DiveBaseEntity baseEntity) {
        if (configId == baseEntity.getDiveItem())
            return true;
        if (configId == baseEntity.getDivePropA())
            return true;
        if (configId == baseEntity.getDivePropB())
            return true;
        if (configId == diveSupport.getShellAItem(baseEntity))
            return true;
        if (configId == diveSupport.getShellBItem(baseEntity))
            return true;

        return false;
    }

    private void bombPop(Dive model, Triple<Integer, Integer, Dive.DiveGrid> triple, List<List<Integer>> rewards, List<Triple<Integer, Integer, Dive.DiveGrid>> shineGridList, List<Triple<Integer, Integer, Dive.DiveGrid>> sharkGridList, DiveBaseEntity baseEntity) {
        Dive.DiveGrid shineGrid = triple.getRight();
        EDiveGrid eDiveGrid = shineGrid.getEDiveGridType();
        if (shineGrid.getReward() != null) {
            rewards.add(shineGrid.getReward());
            model.setRewardAcc(model.getRewardAcc() + 1);
        } else {
            List<Integer> reward = diveSupport.getSpecialItemReward(eDiveGrid, baseEntity);
            if (reward != null) {
                rewards.add(reward);
                model.setRewardAcc(model.getRewardAcc() + 1);
            }
        }

        if (shineGrid.getShark() != null) {
            sharkGridList.add(triple);
        }

        changeGirdToPass(shineGrid);
        shineGridList.add(triple);
    }

    private void bombIce(Triple<Integer, Integer, Dive.DiveGrid> triple, List<Triple<Integer, Integer, Dive.DiveGrid>> shineGridList, List<Triple<Integer, Integer, Dive.DiveGrid>> sharkGridList) {
        Dive.DiveGrid shineGrid = triple.getRight();
        if (shineGrid.getShark() != null) {
            sharkGridList.add(triple);
        }

        changeGirdToPass(shineGrid);
        shineGridList.add(triple);
    }

    private void bombContinuousRewards(Dive model, Triple<Integer, Integer, Dive.DiveGrid> triple, List<List<Integer>> rewards, List<Triple<Integer, Integer, Dive.DiveGrid>> shineGridList) {
        Dive.DiveGrid shineGrid = triple.getRight();
        if (shineGrid.getValue() != null && shineGrid.getValue() > 0) {
            List<Integer> reward = diveSupport.randomContinousReward(model);
            rewards.add(reward);
            shineGrid.setValue(Math.max(0, shineGrid.getValue() - 1));
        }

        shineGridList.add(triple);
    }

    private void bombVarec(Dive model, Triple<Integer, Integer, Dive.DiveGrid> triple, List<List<Integer>> rewards, List<Triple<Integer, Integer, Dive.DiveGrid>> shineGridList, List<Triple<Integer, Integer, Dive.DiveGrid>> varecGridList) {
        Dive.DiveGrid shineGrid = triple.getRight();
        int depth = triple.getLeft();
        if (shineGrid.getReward() != null) {
            rewards.add(shineGrid.getReward());
            shineGrid.setReward(null);

//            int depth = diveSupport.getDepth(shineGrid.getIdx());
            int index = triple.getMiddle();

            List<Triple<Integer, Integer, Dive.DiveGrid>> arounds = diveSupport.getAroundGrid(model, depth, index);
            for (Triple<Integer, Integer, Dive.DiveGrid> around : arounds) {
                Dive.DiveGrid diveGrid = around.getRight();
                if (diveGrid.getType() == EDiveGrid.eVarecCenter.getType()) {
                    if (diveGrid.getValue() != null && diveGrid.getValue() > 0) {
                        diveGrid.setValue(diveGrid.getValue() - 1);
                        varecGridList.add(around);
                        break;
                    }
                }
            }
        }

        // 标记点击过
        shineGrid.setValue(1);

        shineGridList.add(triple);
    }
}
