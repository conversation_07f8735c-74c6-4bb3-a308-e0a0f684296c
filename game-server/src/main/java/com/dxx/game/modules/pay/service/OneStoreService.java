package com.dxx.game.modules.pay.service;

import com.alibaba.fastjson.JSONObject;

public interface OneStoreService {
	/**
	 * 获取AccessToken
	 * @return
	 */
	public String getAccessToken(boolean isSandBox);
	
	/**
	 * 增加token调用次数
	 */
	public void addAccessTokenCount();
	
	/**
	 * 刷新token
	 * @param isRefreshNow
	 */
	void refreshToken(boolean isRefreshNow);

	/**
	 * 校验订单状态
	 */
	public int checkOrderState(JSONObject receiptJsonObj);
}
