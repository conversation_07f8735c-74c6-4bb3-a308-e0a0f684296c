package com.dxx.game.modules.integralshop.service.impl;

import com.amazonaws.xray.spring.aop.XRayEnabled;
import com.dxx.game.common.aws.dynamodb.transaction.annotation.DynamoDBTransactional;
import com.dxx.game.common.server.context.RequestContext;
import com.dxx.game.common.server.model.Result;
import com.dxx.game.common.utils.DateUtils;
import com.dxx.game.common.utils.RandomUtil;
import com.dxx.game.config.GameConfigManager;
import com.dxx.game.config.entity.integralshop.GoodsEntity;
import com.dxx.game.consts.ErrorCode;
import com.dxx.game.consts.EventType;
import com.dxx.game.consts.TaskType;
import com.dxx.game.consts.VipPermission;
import com.dxx.game.dao.dynamodb.model.User;
import com.dxx.game.dao.dynamodb.model.UserExtend;
import com.dxx.game.dao.dynamodb.model.gameplay.UserChapter;
import com.dxx.game.dao.dynamodb.repository.UserDao;
import com.dxx.game.dao.dynamodb.repository.UserExtendDao;
import com.dxx.game.dao.dynamodb.repository.gameplay.UserChapterDao;
import com.dxx.game.dto.IntegralShopProto;
import com.dxx.game.modules.common.support.CommonHelper;
import com.dxx.game.modules.event.service.EventService;
import com.dxx.game.modules.integralshop.service.IntegralShopService;
import com.dxx.game.modules.reward.result.RewardResultSet;
import com.dxx.game.modules.reward.service.RewardService;
import com.dxx.game.modules.task.support.TaskSupport;
import com.dxx.game.modules.user.service.UserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/8/18 20:31
 */
@Slf4j
@Service
@XRayEnabled
public class IntegralShopServiceImpl implements IntegralShopService {
    @Autowired
    private GameConfigManager gameConfigManager;
    @Autowired
    private UserExtendDao userExtendDao;
    @Autowired
    private RewardService rewardService;
    @Autowired
    private UserDao userDao;
    @Autowired
    private TaskSupport taskSupport;
    @Autowired
    private UserService userService;
    @Autowired
    private UserChapterDao userChapterDao;
    @Autowired
    private EventService eventService;

    @Override
    public void check(User user, UserExtend userExtend, UserChapter userChapter) {
        long time = DateUtils.getUnixTime();

        int chapterId = userChapter.getMaxChapterId();

        Map<Integer, UserExtend.IntegralShopModel> integralShopModels = userExtend.getIntegralShopModels();

        if (integralShopModels == null) {
            return;
        }

        // 判断是否解锁初始化
        for (Integer shopId : gameConfigManager.getShopGoodMap().keySet()) {

            // 商店入口判断
            int requireChapterId = gameConfigManager.getIntegralShopConfig().getDataEntity(shopId).getLevelRequirements();
            if (integralShopModels.get(shopId) == null && chapterId >= requireChapterId) {
                integralShopModels.put(shopId, createModel(shopId, chapterId));
                continue;
            }

            // 跨天判断
            if (integralShopModels.get(shopId) != null && !DateUtils.isSameDay(integralShopModels.get(shopId).getRefreshTime(), time)) {
                integralShopModels.put(shopId, createModel(shopId, chapterId));
            }
        }

        userExtend.setIntegralShopModels(integralShopModels);
    }

    private UserExtend.IntegralShopModel createModel(int shopId, int chapterId) {
        List<Integer> list = genShopGoods(shopId, chapterId).stream().map(GoodsEntity::getID).collect(Collectors.toList());

        UserExtend.IntegralShopModel integralShopModel = new UserExtend.IntegralShopModel();
        integralShopModel.setShopId(shopId);
        integralShopModel.setBuyList(new ArrayList<>());
        integralShopModel.setGoodsList(list);
        integralShopModel.setRefreshTime(DateUtils.getUnixTime());
        integralShopModel.setRefreshNum(0);

        List<Integer> refreshCost = gameConfigManager.getIntegralShopConfig().getDataEntity(shopId).getRefreshCost();

        int rNum = refreshCost == null ? 0 : refreshCost.size();
        int add = (int) userService.vipPermissionValueCheck(RequestContext.getUserId(), VipPermission.PERMISSION_15);
        integralShopModel.setMaxNum(rNum + add);

        return integralShopModel;
    }

    private List<GoodsEntity> genShopGoods(int shopId, int chapterId) {
        List<GoodsEntity> shopGoodList = new ArrayList<>();

        Map<Integer, List<GoodsEntity>> groupGoodList = new HashMap<>();
        gameConfigManager.getShopGoodMap().get(shopId).forEach((goodId, entity) -> {
            groupGoodList.computeIfAbsent(entity.getGroupId(), k -> new ArrayList<>()).add(entity);
        });

        groupGoodList.forEach((groupId, list) -> {
            List<Integer> weights = new ArrayList<>();
            list.forEach(goodsEntity -> {
                weights.add(goodsEntity.getWeightInGroup());
            });

            int index = RandomUtil.randomProbIndexWithSeed(new Random(), weights);
            GoodsEntity entity = list.get(index);

//            if (chapterId >= entity.getLevelRequirements()) {
//                shopGoodList.add(entity);
//            }

            shopGoodList.add(entity);
        });

        return shopGoodList;
    }

    private Map<Integer, List<GoodsEntity>> genAllShopGoods(int chapterId) {
        Map<Integer, List<GoodsEntity>> shopGoodList = new HashMap<>();

        gameConfigManager.getShopGoodMap().forEach((shopId, goodList) -> {
            shopGoodList.put(shopId, genShopGoods(shopId, chapterId));
        });

        return shopGoodList;
    }

    @DynamoDBTransactional
    @Override
    public Result<IntegralShopProto.IntegralShopBuyResponse> shopBuy(IntegralShopProto.IntegralShopBuyRequest params) {
        long userId = RequestContext.getUserId();
        User user = RequestContext.getUser();

        int shopId = params.getShopConfigId();
        int goodId = params.getGoodsConfigId();

        UserExtend userExtend = userExtendDao.getByUserId(userId);
        UserExtend.IntegralShopModel shopModel = userExtend.getIntegralShopModels().get(shopId);

        if (shopModel == null || !shopModel.getGoodsList().contains(goodId)) {
            return Result.Error(ErrorCode.INTEGRAL_SHOP_GOODS_NOT_EXIST);
        }

        int costItemId = gameConfigManager.getIntegralShopConfig().getDataEntity(shopId).getCurrencyID();

        GoodsEntity entity = gameConfigManager.getShopGoodMap().get(shopId).get(goodId);
        int price = gameConfigManager.getShopGoodMap().get(shopId).get(goodId).getPrice();
        int discount = gameConfigManager.getShopGoodMap().get(shopId).get(goodId).getDiscount();

        int rewardItemId = entity.getItems().get(0);
        int rewardCount = entity.getItems().get(1);

        int buyCount = (int) shopModel.getBuyList().stream().filter(v -> v == goodId).count();
        if (buyCount >= rewardCount) {
            return Result.Error(ErrorCode.INTEGRAL_SHOP_GOODS_BUY_COUNT);
        }

        int finalPrice = price * discount / 100;

        List<List<Integer>> rewardConfig = new ArrayList<>();
        rewardConfig.add(new ArrayList<>(List.of(rewardItemId, rewardCount)));
        rewardConfig.add(new ArrayList<>(List.of(costItemId, -finalPrice)));

        RewardResultSet rewardResultSet = rewardService.executeRewards(userId, rewardConfig);
        if (rewardResultSet.isFailed()) {
            return Result.Error(rewardResultSet.getResultCode());
        }

        shopModel.getBuyList().add(goodId);

        userExtendDao.update(userExtend);
        if (entity.getTypeId() == 2) {
            //任务 进行黑市购买次数
            taskSupport.triggerTask(userId, TaskType.ACHIEVE_TIMES_INTEGRAL_SHOP_BUY, 1);
            //任务 黑市累计消耗
            taskSupport.triggerTask(userId, TaskType.INTEGRAL_SHOP_CONSUME_COUNT, finalPrice);

            //消耗活动 黑市
            eventService.updateConsume(userId, EventType.CONSUME_INTEGRAL, finalPrice);
        }

        IntegralShopProto.IntegralShopBuyResponse.Builder response = IntegralShopProto.IntegralShopBuyResponse.newBuilder();
        response.setCommonData(CommonHelper.buildCommonData(rewardResultSet));
        response.setShopConfigId(shopId);
        response.setGoodsConfigId(goodId);
        return Result.Success(response.build());
    }

    @DynamoDBTransactional
    @Override
    public Result<IntegralShopProto.IntegralShopRefreshResponse> shopRefresh(IntegralShopProto.IntegralShopRefreshRequest params) {
        long userId = RequestContext.getUserId();

        int shopId = params.getShopConfigId();

        UserExtend userExtend = userExtendDao.getByUserId(userId);
        UserExtend.IntegralShopModel shopModel = userExtend.getIntegralShopModels().get(shopId);
        if (shopModel == null) {
            return Result.Error(ErrorCode.INTEGRAL_SHOP_GOODS_NOT_EXIST);
        }

        int refreshNum = shopModel.getRefreshNum();
        int maxNum = shopModel.getMaxNum();
        if (refreshNum >= maxNum) {
            return Result.Error(ErrorCode.INTEGRAL_SHOP_REFRESH_NUM_MAX);
        }

        List<Integer> refreshCost = gameConfigManager.getIntegralShopConfig().getDataEntity(shopId).getRefreshCost();
        int cost;
        if (refreshCost.size() <= refreshNum) {
            cost = refreshCost.get(refreshCost.size() - 1);
        } else {
            cost = refreshCost.get(refreshNum);
        }

        List<List<Integer>> rewardConfig = new ArrayList<>();
        rewardConfig.add(new ArrayList<>(List.of(2, -cost)));

        RewardResultSet rewardResultSet = rewardService.executeRewards(userId, rewardConfig);
        if (rewardResultSet.isFailed()) {
            return Result.Error(rewardResultSet.getResultCode());
        }

        UserChapter userChapter = userChapterDao.getByUserId(userId);
        int chapterId = userChapter.getMaxChapterId();

        List<Integer> list = genShopGoods(shopId, chapterId).stream().map(GoodsEntity::getID).collect(Collectors.toList());
        shopModel.setGoodsList(list);

        shopModel.setBuyList(new ArrayList<>());

        shopModel.setRefreshNum(refreshNum + 1);
        shopModel.setRefreshTime(DateUtils.getUnixTime());

        userExtendDao.update(userExtend);

        //消耗活动 黑市
        if(shopId == 2) {
            eventService.updateConsume(userId, EventType.CONSUME_INTEGRAL, cost);
            //任务 黑市累计消耗
            taskSupport.triggerTask(userId, TaskType.INTEGRAL_SHOP_CONSUME_COUNT, cost);
        }

        IntegralShopProto.IntegralShopRefreshResponse.Builder response = IntegralShopProto.IntegralShopRefreshResponse.newBuilder();
        response.setShop(CommonHelper.buildIntegralShopDto(shopModel));
        response.setCommonData(CommonHelper.buildCommonData(rewardResultSet));
        return Result.Success(response.build());
    }

    @DynamoDBTransactional
    @Override
    public Result<IntegralShopProto.IntegralShopGetInfoResponse> shopGetInfo(IntegralShopProto.IntegralShopGetInfoRequest params) {
        long userId = RequestContext.getUserId();

        int shopId = params.getShopConfigId();

        User user = userDao.getByUserId(userId);
        UserExtend userExtend = userExtendDao.getByUserId(userId);
        UserChapter userChapter = userChapterDao.getByUserId(userId);
        check(user, userExtend, userChapter);

        UserExtend.IntegralShopModel shopModel = userExtend.getIntegralShopModels().get(shopId);
        if (shopModel == null) {
            return Result.Error(ErrorCode.INTEGRAL_SHOP_GOODS_NOT_EXIST);
        }

        userExtendDao.update(userExtend);

        IntegralShopProto.IntegralShopGetInfoResponse.Builder response = IntegralShopProto.IntegralShopGetInfoResponse.newBuilder();
        response.setShop(CommonHelper.buildIntegralShopDto(shopModel));
        return Result.Success(response.build());
    }
}
