package com.dxx.game.modules.slg.service;

import com.dxx.game.common.server.model.Result;
import com.dxx.game.dto.SlgProto;

public interface SlgService {
    Result<SlgProto.SlgOperateResponse> operate(SlgProto.SlgOperateRequest params);

    Result<SlgProto.SlgApplyResponse> apply(SlgProto.SlgApplyRequest params);

    Result<SlgProto.SlgInfoResponse> info(SlgProto.SlgInfoRequest params);

    Result<SlgProto.SlgEnterSceneResponse> enterScene(SlgProto.SlgEnterSceneRequest params);

    Result<SlgProto.SlgUserBattleDtoResponse> userBattleInfo(SlgProto.SlgUserBattleDtoRequest params);

    Result<SlgProto.SlgTeamRebirthResponse> teamRebirth(SlgProto.SlgTeamRebirthRequest params);

    Result<SlgProto.SlgBacklogListResponse> backlogList(SlgProto.SlgBacklogListRequest params);

    Result<SlgProto.SlgExecutiveBacklogResponse> executiveBacklog(SlgProto.SlgExecutiveBacklogRequest params);
}
