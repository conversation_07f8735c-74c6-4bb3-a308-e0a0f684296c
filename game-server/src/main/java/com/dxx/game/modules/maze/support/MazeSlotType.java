package com.dxx.game.modules.maze.support;

import java.util.HashMap;
import java.util.Map;

public enum MazeSlotType {
    /** 没摇到*/
    NONE(0),
    /** 恢复生命*/
    RECOVER_HP(1),
    /** 攻击百分比-累乘*/
    ATTACK(2),
    /**  防御百分比-累乘*/
    DEFENSE(3),
    /** 血上限百分比-累乘*/
    MAX_HP(4),
    /** 技能*/
    SKILL(5),
    /** 金币*/
    GAME_IN_COIN(6),
    /** 技能*/
    BOMB(7)
    ;

    private int type;

    private static Map<Integer, MazeSlotType> values = new HashMap<>();

    static {
        MazeSlotType[] values1 = values();
        for (MazeSlotType type : values1) {
            values.put(type.getType(), type);
        }
    }


    MazeSlotType(int type) {
        this.type = type;
    }

    public int getType() {
        return type;
    }

    public static MazeSlotType getEnumByType(int type) {
        return values.get(type);
    }

}
