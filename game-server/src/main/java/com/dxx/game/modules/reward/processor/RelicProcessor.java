package com.dxx.game.modules.reward.processor;

import com.amazonaws.xray.spring.aop.XRayEnabled;
import com.dxx.game.config.GameConfigManager;
import com.dxx.game.consts.ErrorCode;
import com.dxx.game.consts.RewardResourceType;
import com.dxx.game.consts.RewardType;
import com.dxx.game.consts.TaskType;
import com.dxx.game.dao.dynamodb.model.UserExtend;
import com.dxx.game.dao.dynamodb.repository.UserExtendDao;
import com.dxx.game.dao.redis.BattleUnitCacheRedisDao;
import com.dxx.game.modules.relic.service.RelicService;
import com.dxx.game.modules.reward.action.RewardAction;
import com.dxx.game.modules.reward.model.RelicReward;
import com.dxx.game.modules.reward.model.Reward;
import com.dxx.game.modules.reward.result.RewardResult;
import com.dxx.game.modules.reward.service.RewardService;
import com.dxx.game.modules.task.support.TaskSupport;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/4/1 15:09
 */
@Slf4j
@Component
@XRayEnabled
public class RelicProcessor implements RewardProcessor {

    @Autowired
    private GameConfigManager gameConfigManager;
    @Autowired
    private UserExtendDao userExtendDao;
    @Autowired
    private RewardService rewardService;
    @Autowired
    private BattleUnitCacheRedisDao battleRedisDao;
    @Autowired
    private RelicService relicService;
    @Autowired
    private TaskSupport taskSupport;

    @Override
    public RewardType getType() {
        return RewardType.RELIC;
    }

    @Override
    public RewardResourceType getRewardResourceType() {
        return RewardResourceType.NONE;
    }

    @Override
    public RewardAction tryReward(long userId, Reward reward) {
        int resultCode = ErrorCode.SUCCESS;
        int addCount = reward.getCount();

        if (addCount <= 0) {
            resultCode = ErrorCode.COUNT_GT_ZERO;
        } else {
            RelicReward rw = (RelicReward) reward;
            int configId = rw.getConfigId();

            if (gameConfigManager.getItemConfig().getItemEntity(configId) == null) {
                resultCode = ErrorCode.CONFIG_NOT_EXIST;
            }
        }

        return simpleRewardAction(reward, resultCode);
    }

    @Override
    public RewardResult<?> executeReward(long userId, RewardAction rewardAction) {
        if (rewardAction.isFailed()) {
            return null;
        }

        RelicReward reward = (RelicReward) rewardAction.getReward();
        int configId = reward.getConfigId();

        RewardResult<UserExtend.RelicModel> result = new RewardResult<>(this.getType());
        UserExtend userExtend = userExtendDao.getByUserId(userId);

        int addCount = reward.getCount();
        if(addCount > 0) {
            Map<Integer, UserExtend.RelicModel> relics = userExtend.getRelics();
            if (relics == null) {
                relics = new HashMap<>();
                userExtend.setRelics(relics);
            }

            UserExtend.RelicModel model = relicService.createRelic(userExtend, configId);

            result.setCurrent(model);
            result.setActualCount(1);
        }

        userExtendDao.update(userExtend);
        battleRedisDao.removeData(userId);

        taskSupport.triggerTask(userId, TaskType.ACHIEVE_COUNT_DIFF_RELIC, userExtend.getRelics().size());

        return result;
    }
}
