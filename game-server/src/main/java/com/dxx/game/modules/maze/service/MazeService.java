package com.dxx.game.modules.maze.service;

import com.dxx.game.common.server.model.Result;
import com.dxx.game.dao.dynamodb.model.gameplay.UserMaze;
import com.dxx.game.dto.MazeProto.*;
import org.springframework.stereotype.Service;

@Service
public interface MazeService {
    /**
     * 进入迷宫
     *
     * @param params
     * @return
     */
    Result<StartMazeResponse> startMaze(StartMazeRequest params);

    /**
     * 开始一个房间
     *
     * @param params
     * @return
     */
    Result<MazeRoomStartResponse> mazeRoomStart(MazeRoomStartRequest params);

    /**
     * 结束一个房间
     *
     * @param params
     * @return
     */
    Result<MazeRoomFinishResponse> mazeRoomFinish(MazeRoomFinishRequest params);

    /**
     * 翻开一个房间
     *
     * @param params
     * @return
     */
    Result<MazeRoomOpenResponse> mazeRoomOpen(MazeRoomOpenRequest params);

    /**
     * 退出迷宫
     *
     * @param params
     * @return
     */
    Result<QuitMazeResponse> quitMaze(QuitMazeRequest params);

    /**
     * 进入迷宫下一层
     *
     * @param params
     * @return
     */
    Result<MazeNextLayerResponse> mazeNextLayer(MazeNextLayerRequest params);

    /**
     * 保存透传字符串
     *
     * @param params
     * @return
     */
    Result<MazeSetFormationDataResponse> mazeSetFormationData(MazeSetFormationDataRequest params);

    void initMazeModel(UserMaze mazeModel);

    Result<MazeGetInfoResponse> getInfo(MazeGetInfoRequest params);
}
