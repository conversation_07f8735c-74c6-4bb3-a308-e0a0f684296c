package com.dxx.game.modules.im;

import com.alibaba.fastjson.JSONObject;
import com.amazonaws.xray.spring.aop.XRayEnabled;
import com.dxx.game.common.redis.RedisLock;
import com.dxx.game.common.utils.CollectionUtils;
import com.dxx.game.common.utils.DateUtils;
import com.dxx.game.dao.dynamodb.model.IMGroupStatus;
import com.dxx.game.dao.dynamodb.model.IMGroupUserStatus;
import com.dxx.game.dao.dynamodb.model.Server;
import com.dxx.game.dao.dynamodb.model.guild.Guild;
import com.dxx.game.dao.dynamodb.model.guild.GuildUser;
import com.dxx.game.dao.dynamodb.repository.ServerDao;
import com.dxx.game.dao.dynamodb.repository.guild.GuildDao;
import com.dxx.game.dao.dynamodb.repository.guild.GuildUserDao;
import com.dxx.game.dto.ChatProto;
import com.dxx.game.modules.im.dto.*;
import com.google.api.client.util.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


@Service
@Slf4j
@XRayEnabled
public class IMService {

    private static final int SUCCESS_CODE = 0;

    @Value("${IM_SERVER_INTERNAL_HOST:https://dev-internal-api-gateway.lebi4.com/habbygame/im}")
    private String imInternalHost;

    @Resource
    private RedisLock redisLock;
    @Resource
    private ServerDao serverDao;
    @Resource
    private GuildDao guildDao;
    @Resource
    private GuildUserDao guildUserDao;

    public boolean createMapGroup(long mapId, String imGroupId) {
        CreateGroupRequest request = CreateGroupRequest.builder()
                .groupId(imGroupId)
                .type(CreateGroupRequest.Type.Public.name())
                .groupName("mapId_" + mapId)
                .trans("TRANS#" + mapId + "#" + imGroupId)
                .build();

        return createGroup(request);
    }

    public boolean createServerGroup(Integer serverId, String imGroupId) {
        CreateGroupRequest request = CreateGroupRequest.builder()
                .groupId(imGroupId)
                .type(CreateGroupRequest.Type.AVChatRoom.name())
                .groupName("server_" + serverId)
                .trans("TRANS#" + serverId + "#" + imGroupId)
                .build();

        return createGroup(request);
    }

    public boolean createGroup(CreateGroupRequest request) {
        try {
            String url = imInternalHost + "/group_open_http_svc/create_group";
            BaseResponse response = IMHttpClient.postJson(url, request, BaseResponse.class);
            if (response == null) {
                log.error("createGroup failed, response is null, request:{}", JSONObject.toJSONString(request));
                return false;
            }

            int code = response.getCode();
            if (code != SUCCESS_CODE) {
                log.error("createGroup failed, code is not 0, code:{}, request:{}, response{}",
                        code, JSONObject.toJSONString(request), JSONObject.toJSONString(response));
                return false;
            }

            log.info("createGroup success, request:{}", JSONObject.toJSONString(request));
            return true;
        } catch (Exception e) {
            log.error("createGroup failed, request:{}", JSONObject.toJSONString(request), e);
        }

        return false;
    }

    @Async
    public void asyncCreateServerGroupDao(Integer serverId, String imGroupId, Long openTime) {
        int expireInMill = 3000;
        String key = "createServerGroup:" + serverId;
        String lockValue = String.valueOf(System.currentTimeMillis());
        try {
            if (redisLock.lockWithOutRetry(key, lockValue, expireInMill)) {
                boolean isOk = createServerGroup(serverId, imGroupId);
                if (isOk) {
                    var server =  new Server();
                    server.setServerId(serverId);
                    server.setOpenTime(openTime);
                    server.setCreateTime(openTime);
                    server.setImGroupId(imGroupId);
                    server.setImGroupStatus(IMGroupStatus.CREATED);
                    server.setUpdateTime(DateUtils.getUnixTime());
                    serverDao.insertNow(server);
                }

            }
        } catch (Exception e) {
            log.error("asyncCreateServerGroupDao failed, serverId:{}, imGroupId:{}", serverId, imGroupId, e);
        } finally {
            redisLock.unlock(key, lockValue);
        }
    }

    @Async
    public void asyncCreateServerGroup(Integer serverId, String imGroupId) {
        int expireInMill = 3000;
        String key = "createServerGroup:" + serverId;
        String lockValue = String.valueOf(System.currentTimeMillis());
        try {
            if (redisLock.lockWithOutRetry(key, lockValue, expireInMill)) {
                boolean isOk = createServerGroup(serverId, imGroupId);
                if (isOk) {
                    Server server = new Server();
                    server.setServerId(serverId);
                    server.setImGroupId(imGroupId);
                    server.setImGroupStatus(IMGroupStatus.CREATED);
                    server.setUpdateTime(DateUtils.getUnixTime());
                    serverDao.updateNowIgnoreNulls(server);
                }

            }
        } catch (Exception e) {
            log.error("asyncCreateServerGroup failed, serverId:{}, imGroupId:{}", serverId, imGroupId, e);
        } finally {
            redisLock.unlock(key, lockValue);
        }
    }

    @Async
    public void asyncCreateGuildGroup(long guildId, String imGroupId, long operationUserId) {
        int expireInMill = 3000;
        String key = "createGuildGroup:" + guildId;
        String lockValue = String.valueOf(System.currentTimeMillis());
        try {
            if (redisLock.lockWithOutRetry(key, lockValue, expireInMill)) {
                boolean isOk = createGuildGroup(guildId, imGroupId, operationUserId);
                if (isOk) {
                    Guild guild = new Guild();
                    guild.setGuildId(guildId);
                    guildDao.setPrimaryKey(guild);
                    guild.setImGroupId(imGroupId);
                    guild.setImGroupStatus(IMGroupStatus.CREATED);
                    guild.setGuildUpdateTime(DateUtils.getUnixTime());
                    guildDao.updateNowIgnoreNulls(guild);
                }
            }
        } catch (Exception e) {
            log.error("asyncCreateGuildGroup failed, guildId:{}, imGroupId:{}, operationUserId:{}",
                    guildId, imGroupId, operationUserId, e);
        } finally {
            redisLock.unlock(key, lockValue);
        }
    }

    public boolean createGuildGroup(long guildId, String imGroupId, long operationUserId) {
        CreateGroupRequest request = CreateGroupRequest.builder()
                .groupId(imGroupId)
                .type(CreateGroupRequest.Type.Public.name())
                .groupName("guildId_" + guildId)
                .operationUserId(String.valueOf(operationUserId))
                .memberList(List.of(String.valueOf(operationUserId)))
                .trans("TRANS#" + guildId + "#" + imGroupId)
                .build();

        return createGroup(request);
    }

    public void sendGuildGroupMsg(int messageType, long guildId, String content) {
        // 根据 guildId 获取 imGroupId
        Guild guild = guildDao.getByGuildId(guildId);
        if (guild == null) {
            log.error("sendGuildGroupMsg failed, guild is null, messageType:{}, guildId:{}, content:{}",
                    messageType, guildId, content);
            return;
        }

        sendGroupMsg(messageType, guild.getImGroupId(), content);
    }

    public void sendGroupMsg(int messageType, String groupId, String content) {
        SendGroupMsgRequest request = SendGroupMsgRequest.builder()
                .groupId(groupId)
                .msgContent(MsgContent.builder().data(content)
                        .customType(String.valueOf(messageType))
                        .build())
                .build();

        try {
            String url = imInternalHost + "/group_open_http_svc/send_group_msg";
            BaseResponse response = IMHttpClient.postJson(url, request, BaseResponse.class);
            if (response == null) {
                log.error("sendGroupMsg failed, response is null, request:{}", JSONObject.toJSONString(request));
                return;
            }

            int code = response.getCode();
            if (code != SUCCESS_CODE) {
                log.error("sendGroupMsg failed, code is not 0, code:{}, request:{}, response{}",
                        code, JSONObject.toJSONString(request), JSONObject.toJSONString(response));
                return;
            }

            log.info("sendGroupMsg success, request:{}", JSONObject.toJSONString(request));
        } catch (Exception e) {
            log.error("sendGroupMsg failed, request:{}", JSONObject.toJSONString(request), e);
        }

    }

    public boolean sendSystemMsg(ChatProto.MessageType messageType, String groupId, String content) {
        SendSystemMsgRequest request = SendSystemMsgRequest.builder()
                .msgContent(MsgContent.builder().data(content)
                        .customType(String.valueOf(messageType.getNumber()))
                        .data(content)
                        .build())
                .groupId(groupId)
                .build();

        try {
            String url = imInternalHost + "/group_open_http_svc/send_group_system_notification";
            BaseResponse response = IMHttpClient.postJson(url, request, BaseResponse.class);
            if (response == null) {
                log.error("sendSystemMsg failed, response is null, request:{}", JSONObject.toJSONString(request));
                return false;
            }

            int code = response.getCode();
            if (code != SUCCESS_CODE) {
                log.error("sendSystemMsg failed, code is not 0, code:{}, request:{}, response:{}",
                        code, JSONObject.toJSONString(request), JSONObject.toJSONString(response));
                return false;
            }

            log.info("sendSystemMsg success, request:{}", JSONObject.toJSONString(request));
            return true;
        } catch (Exception e) {
            log.error("sendSystemMsg failed, request:{}", JSONObject.toJSONString(request), e);
        }

        return false;
    }

    public void batchSendPersonalMsg(int messageType, List<Long> userIdList, String content) {
        List<String> toAccountList = Lists.newArrayListWithCapacity(userIdList.size());
        for (Long userId : userIdList) {
            toAccountList.add(String.valueOf(userId));
        }

        SendPersonalMsgRequest request = SendPersonalMsgRequest.builder()
                .msgContent(MsgContent.builder().data(content)
                        .customType(String.valueOf(messageType))
                        .build())
                .toUserIds(toAccountList)
                .build();

        try {
            String url = imInternalHost + "/openim/sendmsg";
            BaseResponse response = IMHttpClient.postJson(url, request, BaseResponse.class);
            if (response == null) {
                log.error("batchSendPersonalMsg failed, response is null, request:{}", JSONObject.toJSONString(request));
                return;
            }

            int code = response.getCode();
            if (code != SUCCESS_CODE) {
                log.error("batchSendPersonalMsg failed, code is not 0, code:{}, request:{}, response:{}",
                        code, JSONObject.toJSONString(request), JSONObject.toJSONString(response));
                return;
            }

            log.info("batchSendPersonalMsg success, request:{}", JSONObject.toJSONString(request));
        } catch (Exception e) {
            log.error("batchSendPersonalMsg failed, request:{}", JSONObject.toJSONString(request), e);
        }
    }

    public void sendPersonalMsg(int messageType, Long userId, String content) {
        batchSendPersonalMsg(messageType, List.of(userId), content);
    }

    public Map<Long, Boolean> batchGetUserOnline(List<Long> userIdList) {
        GetUserOnlineStatusResponse getUserOnlineStatusResponse = batchGetUserOnlineStatus(userIdList);
        if (getUserOnlineStatusResponse == null) {
            return Collections.emptyMap();
        }

        GetUserOnlineStatusResponse.Data responseData = getUserOnlineStatusResponse.getData();
        if (responseData == null) {
            return Collections.emptyMap();
        }

        Map<String, Long> userStatuses = responseData.getUserStatuses();
        if (userStatuses == null) {
            return Collections.emptyMap();
        }

        Map<Long, Boolean> onlineMap = Maps.newHashMapWithExpectedSize(userIdList.size());
        userStatuses.forEach((userId, offlineTime) -> {
            if (StringUtils.isEmpty(userId)) {
                log.warn("userId is empty, userIdList:{}", userIdList);
                return;
            }

            try {
                if (offlineTime == -1) {
                    onlineMap.put(Long.parseLong(userId), true);
                } else {
                    onlineMap.put(Long.parseLong(userId), false);
                }
            } catch (NumberFormatException e) {
                log.error("userId is not number, userId:{}", userId);
            }
        });

        return onlineMap;
    }

    public Map<Long, Long> batchGetUserOnlineTime(List<Long> userIdList) {
        GetUserOnlineStatusResponse getUserOnlineStatusResponse = batchGetUserOnlineStatus(userIdList);
        if (getUserOnlineStatusResponse == null) {
            return Collections.emptyMap();
        }

        GetUserOnlineStatusResponse.Data responseData = getUserOnlineStatusResponse.getData();
        if (responseData == null) {
            return Collections.emptyMap();
        }

        Map<String, Long> userStatuses = responseData.getUserStatuses();
        if (userStatuses == null) {
            return Collections.emptyMap();
        }

        Map<Long, Long> onlineMap = Maps.newHashMapWithExpectedSize(userIdList.size());
        userStatuses.forEach((userId, offlineTime) -> {
            if (StringUtils.isEmpty(userId)) {
                log.warn("userId is empty, userIdList:{}", userIdList);
                return;
            }

            try {
                onlineMap.put(Long.parseLong(userId), offlineTime);
            } catch (NumberFormatException e) {
                log.error("userId is not number, userId:{}", userId);
            }
        });

        return onlineMap;
    }

    private GetUserOnlineStatusResponse batchGetUserOnlineStatus(List<Long> userIdList) {
        List<String> userIdStrList = Lists.newArrayListWithCapacity(userIdList.size());
        for (Long userId : userIdList) {
            var str = String.valueOf(userId);
            if (!userIdStrList.contains(str)) {
                userIdStrList.add(str);
            }
        }

        GetUserOnlineStatusRequest request = GetUserOnlineStatusRequest.builder()
                .userIds(userIdStrList)
                .build();
        try {

            String url = imInternalHost + "/group_open_http_svc/batch_get_user_online_status";
            GetUserOnlineStatusResponse response = IMHttpClient.postJson(url, request, GetUserOnlineStatusResponse.class);
            if (response == null) {
                log.error("batchGetUserOnlineStatus failed, response is null, request:{}", JSONObject.toJSONString(request));
                return null;
            }

            int code = response.getCode();
            if (code != SUCCESS_CODE) {
                log.error("batchGetUserOnlineStatus failed, code is not 0, code:{}, request:{}, response{}",
                        code, JSONObject.toJSONString(request), JSONObject.toJSONString(response));
                return null;
            }

            log.info("batchGetUserOnlineStatus success, request:{}", JSONObject.toJSONString(request));
            return response;
        } catch (Exception e) {
            log.error("batchGetUserOnlineStatus failed, request:{}", JSONObject.toJSONString(request), e);
        }

        return null;
    }

    public boolean addGroupMember(long operationUserId, Long joinUserId, String groupId) {
        return addGroupMembers(operationUserId, List.of(joinUserId), groupId);
    }

    public boolean addGroupMembers(long operationUserId, List<Long> joinUserIdList, String groupId) {
        if (CollectionUtils.isEmpty(joinUserIdList)) {
            log.error("addGroupMember failed, joinUserIdList is empty, operationUserId:{}, groupId:{}",
                    operationUserId, groupId);
            return false;
        }

        String trans;
        if (joinUserIdList.size() == 1) {
            trans = "TRANS#" + joinUserIdList.get(0) + "#" + groupId + "#" + (System.currentTimeMillis() / 1000);
        } else {
            trans = "TRANS#" + groupId + "#" + (System.currentTimeMillis() / 1000);
        }

        AddGroupMemberRequest request = AddGroupMemberRequest.builder()
                .trans(trans)
                .groupId(groupId)
                .operationUserId(String.valueOf(operationUserId))
                .memberList(joinUserIdList.stream().map(String::valueOf).collect(Collectors.toList()))
                .build();
        try {

            String url = imInternalHost + "/group_open_http_svc/add_group_member";
            BaseResponse response = IMHttpClient.postJson(url, request, BaseResponse.class);
            if (response == null) {
                log.error("addGroupMember failed, response is null, request:{}", JSONObject.toJSONString(request));
                return false;
            }

            int code = response.getCode();
            if (code != SUCCESS_CODE) {
                log.error("addGroupMember failed, code is not 0, code:{}, request:{}, response{}",
                        code, JSONObject.toJSONString(request), JSONObject.toJSONString(response));
                return false;
            }

            log.info("addGroupMember success, request:{}", JSONObject.toJSONString(request));
            return true;
        } catch (Exception e) {
            log.error("addGroupMember failed, request:{}", JSONObject.toJSONString(request), e);
        }

        return false;
    }

    public boolean removeGroupMember(long operationUserId, Long removeUserId, String groupId) {
        RemoveGroupMemberRequest request = RemoveGroupMemberRequest.builder()
                .trans("TRANS#" + removeUserId + "#" + groupId + "#" + (System.currentTimeMillis() / 1000))
                .groupId(groupId)
                .operationUserId(String.valueOf(operationUserId))
                .memberList(List.of(String.valueOf(removeUserId)))
                .build();
        try {

            String url = imInternalHost + "/group_open_http_svc/delete_group_member";
            BaseResponse response = IMHttpClient.postJson(url, request, BaseResponse.class);
            if (response == null) {
                log.error("removeGroupMember failed, response is null, request:{}", JSONObject.toJSONString(request));
                return false;
            }

            int code = response.getCode();
            if (code != SUCCESS_CODE) {
                log.error("removeGroupMember failed, code is not 0, code:{}, request:{}, response{}",
                        code, JSONObject.toJSONString(request), JSONObject.toJSONString(response));
                return false;
            }

            log.info("removeGroupMember success, request:{}", JSONObject.toJSONString(request));
            return true;
        } catch (Exception e) {
            log.error("removeGroupMember failed, request:{}", JSONObject.toJSONString(request), e);
        }

        return false;
    }

    @Async
    public void asyncAddGroupMember(long operationUserId, Long joinUserId, String groupId) {
        boolean isOk = addGroupMember(operationUserId, joinUserId, groupId);
        if (isOk) {
            GuildUser guildUser = new GuildUser();
            guildUser.setUserId(joinUserId);
            guildUser.setImGroupUserStatus(IMGroupUserStatus.JOINED);
            guildUserDao.setPrimaryKey(guildUser);
            guildUserDao.updateNowIgnoreNulls(guildUser);
        }
    }
}
