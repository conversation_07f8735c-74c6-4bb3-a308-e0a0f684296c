package com.dxx.game.modules.guild.service;

import com.dxx.game.common.server.model.Result;
import com.dxx.game.dao.dynamodb.model.guild.Guild;
import com.dxx.game.dao.dynamodb.model.guild.GuildUser;
import com.dxx.game.dto.GuildProto.*;

/**
 * @authoer: lsc
 * @createDate: 2023/4/18
 * @description:
 */
public interface GuildBossService {

    /**
     * 获取公会boss数据
     * @param params
     * @return
     */
    Result<GuildBossGetInfoResponse> getInfoAction(GuildBossGetInfoRequest params);

    /**
     * 购买挑战次数
     */
    Result<GuildBossBuyCntResponse> bossBuyCntAction(GuildBossBuyCntRequest params);

    /**
     * 领取任务奖励
     */
    Result<GuildBossTaskRewardResponse> bossTaskRewardAction(GuildBossTaskRewardRequest params);

    /**
     * 获取boss排行榜
     */
    Result<GuildBossGetRankListResponse> bossGetRankListAction(GuildBossGetRankListRequest params);

    /**
     * 刷新数据
     */
    void checkUserData(Guild guild, GuildUser guildUser);

    /**
     * 检测公会boss数据
     */
    void checkBossData(Guild guild);

    /**
     * 公会boss数据对象
     */
    GuildBossInfoDto buildGuildBossInfoDto(Guild guild, GuildUser guildUser, boolean queryChallengeRecords);

    /**
     * 检测世界boss挑战次数恢复
     * @param guildUser
     */
    void checkChallengeCnt(Guild guild, GuildUser guildUser);

    Result<GuildBossBattleResponse> bossBattle(GuildBossBattleRequest params);

    Result<GuildBossBattleGRankResponse> gRankList(GuildBossBattleGRankRequest params);

    int getBossOpenId();
}
