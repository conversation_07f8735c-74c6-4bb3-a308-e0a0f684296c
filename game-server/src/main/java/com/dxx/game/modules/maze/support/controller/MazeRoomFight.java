package com.dxx.game.modules.maze.support.controller;

import com.amazonaws.xray.spring.aop.XRayEnabled;
import com.dxx.game.common.server.context.RequestContext;
import com.dxx.game.common.utils.RandomUtil;
import com.dxx.game.common.utils.Symbol;
import com.dxx.game.config.GameConfigManager;
import com.dxx.game.config.entity.maze.DataEntity;
import com.dxx.game.config.entity.maze.MonsterEntity;
import com.dxx.game.config.entity.maze.SmallSlotEntity;
import com.dxx.game.config.object.MazeConfig;
import com.dxx.game.consts.ErrorCode;
import com.dxx.game.dao.dynamodb.model.gameplay.UserMaze;
import com.dxx.game.dao.dynamodb.repository.gameplay.UserFixedFormationDao;
import com.dxx.game.dto.BattleProto;
import com.dxx.game.dto.CommonProto;
import com.dxx.game.dto.MazeProto.*;
import com.dxx.game.modules.battle.BattleService;
import com.dxx.game.modules.common.support.CommonHelper;
import com.dxx.game.modules.maze.support.IMazeRoomController;
import com.dxx.game.modules.maze.support.MazeHelper;
import com.dxx.game.modules.maze.support.MazeMapBean;
import com.dxx.game.modules.maze.support.MazeRoomBean;
import com.dxx.game.modules.maze.support.annotation.MazeRoomType;
import org.apache.commons.lang3.tuple.Pair;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Random;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentSkipListMap;

@MazeRoomType(roomType = com.dxx.game.modules.maze.support.MazeRoomType.MAZE_ROOM_FIGHT)
@XRayEnabled
public class MazeRoomFight implements IMazeRoomController {

    @Resource
    private GameConfigManager gameConfigManager;
    @Resource
    private MazeHelper mazeHelper;
    @Resource
    private BattleService battleService;
    @Resource
    private UserFixedFormationDao userFixedFormationDao;

    @Override
    public void createRoom(DataEntity dataEntity, MazeMapBean mapBean, MazeRoomBean roomBean) {
        roomBean.setEventType(dataEntity.getId());
        roomBean.setRoomType(dataEntity.getRoomType());

        String parameter = dataEntity.getParameter();
        String[] split = parameter.split(Symbol.SHUXIAN);
        int monsterType = Integer.parseInt(split[0]);
        ConcurrentHashMap<Integer, List<MonsterEntity>> mazeMonsterTypeMap = gameConfigManager.getMazeMonsterTypeMap();
        List<MonsterEntity> monsterEntities = mazeMonsterTypeMap.get(monsterType);
        List<Integer> prob = new ArrayList<>();
        for (MonsterEntity monsterEntity : monsterEntities) {
            prob.add(monsterEntity.getWeight());
        }
        Random random = new Random(roomBean.getSeed());
        int index = RandomUtil.randomProbIndexWithSeed(random, prob);
        MonsterEntity monsterEntity = monsterEntities.get(index);
        roomBean.getEventData().add(monsterEntity.getId());
    }

    @Override
    public boolean roomOpen(MazeRoomBean roomBean, UserMaze mazeModel) {
        mazeHelper.createMonster(roomBean.getXPoint(), roomBean.getYPoint(), mazeModel);
        return false;
    }

    @Override
    public MazeRoomStartResponse roomStartData(MazeMapBean mapBean, MazeRoomBean roomBean, UserMaze mazeModel) {
        MazeRoomStartResponse.Builder builder = MazeRoomStartResponse.newBuilder();
        MazeBattleData.Builder battleBuilder = MazeBattleData.newBuilder();
        battleBuilder.setConfigId(roomBean.getEventData().get(0));
        builder.setBattleData(battleBuilder);
        return builder.build();
    }

    @Override
    public Pair<Boolean, MazeRoomFinishResponse.Builder> roomFinish(MazeMapBean mapBean, MazeRoomBean roomBean, UserMaze mazeModel, MazeRoomFinishRequest params) {
        var monster = mazeHelper.getMonster(roomBean.getXPoint(), roomBean.getYPoint(), mazeModel);
        var formation = userFixedFormationDao.getMaze(RequestContext.getUserId());
        var uni = CommonHelper.convertJson2Uni(formation.getFixedFormation());
        BattleProto.RMazeCombatReq.Builder req = BattleProto.RMazeCombatReq.newBuilder();
        req.setMonsterId(roomBean.getEventData().get(0));
        req.setSeed(roomBean.getSeed());

        CommonProto.MazeBattleReqDto.Builder info = CommonProto.MazeBattleReqDto.newBuilder();

        info.setUnit(uni);
        info.addAllHeroDatas(mazeHelper.buildHeroData(mazeModel));
        info.putAllSlotBuffs(mazeModel.getSlotBuffs());
        info.putAllSmallSlotBuffs(mazeModel.getSmallSlotBuffs());
        info.putAllSkills(mazeModel.getSkills());
        info.putAllMonsterHpPercent(mazeHelper.buildMonsterInfoData(monster.getInfos()));
        info.setPower(mazeModel.getWeightPower());
        info.setLayer(mapBean.getLayerDataList().get(mazeModel.getCurrentLayer() - 1).getLevelId());

        req.setInfo(info);

        BattleProto.RMazeCombatResp resp = battleService.maze(req.build());
        int code = resp.getCode();
        if (code != ErrorCode.SUCCESS) {
            return null;
        }

        for (CommonProto.MazeHeroData hero : resp.getBattleInfo().getHeroDatasList()) {
            mazeModel.getHeroList().forEach(userHero -> {
                if (userHero.getHeroRowId() == hero.getHeroRowId()) {
                    userHero.setHpPercent(hero.getHpPercent());
                    userHero.setRechargePercent(hero.getRechargePercent());
                    userHero.setNearDeath(hero.getNearDeath());
                }
            });
        }
        var win = resp.getBattleInfo().getResult() == 1;
        MazeRoomFinishResponse.Builder builder = MazeRoomFinishResponse.newBuilder();

        if (win) {
            mazeModel.getMonsterList().remove(monster);

            int rewardId = randomReward(roomBean);
            AddSlot(rewardId, mazeModel);

            EventRandIndexDto.Builder eventRandIndexDto = EventRandIndexDto.newBuilder();
            eventRandIndexDto.addRandIndexs(rewardId);
            builder.addRandIndexDtos(eventRandIndexDto);

        } else {
            monster.getInfos().clear();
            resp.getBattleInfo().getMonsterHpPercentMap().forEach((k, v) -> {
                UserMaze.UserMazeMonsterInfoModel m = new UserMaze.UserMazeMonsterInfoModel();
                m.setNearDeath(v.getNearDeath());
                m.setRechargePercent(v.getRechargePercent());
                m.setHpPercent(v.getHpPercent());
                monster.getInfos().put(k, m);
            });
        }
        builder.setBattleReqInfo(req.getInfo());
        builder.setBattleResqInfo(resp.getBattleInfo());

        return Pair.of(win, builder);
    }

    @Override
    public int checkRoom(MazeMapBean mapBean, MazeRoomBean roomBean, UserMaze mazeModel) {
        return ErrorCode.SUCCESS;
    }

    private void AddSlot(int id, UserMaze mazeModel) {
        mazeHelper.addSmallSlotBuff(id, mazeModel);
    }

    private int randomReward(MazeRoomBean roomBean) {
        MazeConfig mazeConfig = gameConfigManager.getMazeConfig();
        MonsterEntity monsterEntity = mazeConfig.getMonsterEntity(roomBean.getEventData().get(0));
        ConcurrentSkipListMap<Integer, SmallSlotEntity> smallSlot = mazeConfig.getSmallSlot();

        List<SmallSlotEntity> smallSlotEntityList = new ArrayList<>();
        List<Integer> prob = new ArrayList<>();
        for (SmallSlotEntity smallSlotEntity : smallSlot.values()) {
            if (smallSlotEntity.getGroup() == monsterEntity.getRewardsGroup()) {
                smallSlotEntityList.add(smallSlotEntity);
                prob.add(smallSlotEntity.getWeight());
            }
        }

        Random random = new Random(roomBean.getSeed() + roomBean.getXPoint() + roomBean.getYPoint());
        int index = RandomUtil.randomProbIndexWithSeed(random, prob);
        return smallSlotEntityList.get(index).getId();
    }
}
