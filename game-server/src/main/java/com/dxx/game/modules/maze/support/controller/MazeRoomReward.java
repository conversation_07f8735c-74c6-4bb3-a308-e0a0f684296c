package com.dxx.game.modules.maze.support.controller;

import com.amazonaws.xray.spring.aop.XRayEnabled;
import com.dxx.game.common.server.context.RequestContext;
import com.dxx.game.common.utils.Symbol;
import com.dxx.game.config.GameConfigManager;
import com.dxx.game.config.entity.maze.DataEntity;
import com.dxx.game.config.object.MazeConfig;
import com.dxx.game.consts.ErrorCode;
import com.dxx.game.dao.dynamodb.model.gameplay.UserMaze;
import com.dxx.game.dto.CommonProto.CommonData;
import com.dxx.game.dto.CommonProto.RewardDto;
import com.dxx.game.dto.MazeProto.MazeRoomFinishRequest;
import com.dxx.game.dto.MazeProto.MazeRoomFinishResponse;
import com.dxx.game.dto.MazeProto.MazeRoomStartResponse;
import com.dxx.game.modules.common.service.CommonService;
import com.dxx.game.modules.common.support.CommonHelper;
import com.dxx.game.modules.maze.support.*;
import com.dxx.game.modules.maze.support.annotation.MazeRoomType;
import com.dxx.game.modules.reward.result.RewardResultSet;
import com.dxx.game.modules.reward.service.DropService;
import com.dxx.game.modules.reward.service.RewardService;
import org.apache.commons.lang3.tuple.Pair;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Random;

@MazeRoomType(roomType = com.dxx.game.modules.maze.support.MazeRoomType.MAZE_ROOM_REWARD)
@XRayEnabled
public class MazeRoomReward implements IMazeRoomController {
    @Resource
    private GameConfigManager gameConfigManager;
    @Resource
    private DropService dropService;
    @Resource
    private RewardService rewardService;
    @Resource
    private CommonService commonService;
    @Resource
    private MazeHelper mazeHelper;

    @Override
    public void createRoom(DataEntity dataEntity, MazeMapBean mapBean, MazeRoomBean roomBean) {
        roomBean.setEventType(dataEntity.getId());
        roomBean.setRoomType(dataEntity.getRoomType());
    }

    @Override
    public boolean roomOpen(MazeRoomBean roomBean, UserMaze mazeModel) {
        return false;
    }

    @Override
    public MazeRoomStartResponse roomStartData(MazeMapBean mapBean, MazeRoomBean roomBean, UserMaze mazeModel) {
        List<List<Integer>> rewardList = randomReward(roomBean, mazeModel);
        MazeRoomStartResponse.Builder builder = MazeRoomStartResponse.newBuilder();
        for (List<Integer> reward : rewardList) {
            RewardDto.Builder rewardBuild = RewardDto.newBuilder();
            rewardBuild.setType(reward.get(0));
            rewardBuild.setConfigId(reward.get(1));
            rewardBuild.setCount(reward.get(2));
            builder.addBoxRewardList(rewardBuild);
        }
        return builder.build();
    }

    @Override
    public Pair<Boolean, MazeRoomFinishResponse.Builder> roomFinish(MazeMapBean mapBean, MazeRoomBean roomBean, UserMaze mazeModel, MazeRoomFinishRequest params) {
        MazeRoomFinishResponse.Builder builder = MazeRoomFinishResponse.newBuilder();
        List<List<Integer>> lists = randomReward(roomBean, mazeModel);
        Long userId = RequestContext.getUserId();
        RewardResultSet rewardResultSet = rewardService.executeRewards(userId, lists);
        CommonData commonData = CommonHelper.buildCommonData(rewardResultSet);
        builder.setCommonData(commonData);
        return Pair.of(true, builder);
    }

    private List<List<Integer>> randomReward(MazeRoomBean roomBean, UserMaze mazeModel) {
        MazeConfig mazeConfig = gameConfigManager.getMazeConfig();
        DataEntity dataEntity = mazeConfig.getDataEntity(roomBean.getEventType());
        int id = dataEntity.getId();
        String parameter = dataEntity.getParameter();

        int startCount = mazeModel.getStartCount();
        String[] split = parameter.split(Symbol.SHUXIAN);
        if (id == MazeEventType.MAZE_EVENT_END_TREASURE.getType() && startCount <= 1) {
            // 关底宝箱 首次特殊处理
            split = commonService.getGameConfigValue(8203).split(Symbol.SHUXIAN);
        }

        List<Integer> dropIdList = new ArrayList<>();
        for (String s : split) {
            dropIdList.add(Integer.parseInt(s));
        }
        List<List<Integer>> rewardList = new ArrayList<>();
        Random random = new Random(roomBean.getSeed());
        for (int dropId : dropIdList) {
            List<Integer> reward = dropService.randomDropWishSeed(dropId, random);
            rewardList.add(reward);
        }
        return rewardList;
    }

    @Override
    public int checkRoom(MazeMapBean mapBean, MazeRoomBean roomBean, UserMaze mazeModel) {
        MazeConfig mazeConfig = gameConfigManager.getMazeConfig();
        DataEntity dataEntity = mazeConfig.getDataEntity(roomBean.getEventType());
        int id = dataEntity.getId();

        if (id == MazeEventType.MAZE_EVENT_END_TREASURE.getType()) {
            if (!mazeHelper.checkLayerClean(mazeModel.getCurrentLayer(), mapBean, mazeModel)) {
                return ErrorCode.MAZE_ROOM_FIGHT_NOT_CLEAR;
            }
        }
        return ErrorCode.SUCCESS;
    }
}
