package com.dxx.game.modules.im.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;


/**
 * <AUTHOR>
 * @date 2024/8/21 14:38
 */
@EqualsAndHashCode(callSuper = true)
@lombok.Data
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
public class BeforeJoinQuitGroupResponse extends CallbackResponse {

    private Data data;
    

    @lombok.Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Data {
        private String imGroupType;
        private String gameGroupType;
        private String gameServerId;
        private String gameChatGroupId;
    }

}