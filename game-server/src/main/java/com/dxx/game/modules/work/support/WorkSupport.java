package com.dxx.game.modules.work.support;

import com.amazonaws.xray.spring.aop.XRayEnabled;
import com.dxx.game.common.utils.DateUtils;
import com.dxx.game.dao.dynamodb.model.Work;
import com.dxx.game.dao.dynamodb.repository.WorkDao;
import com.dxx.game.modules.mail.MailService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * 待办事务
 */
@Slf4j
@Component
@XRayEnabled
public class WorkSupport {
    @Resource
    private WorkDao workDao;
    @Autowired
    private MailService mailService;

    public void dealMailWork(long userId) {
        List<Work> workList = getMailWork(userId);
        if (workList.isEmpty()) {
            return;
        }

        workList.forEach(v -> {
            dealWorkNow(v);

            Work.MailModel mail = v.getMail();
            String uniqueId = mail.getUniqueId();
            String templeId = mail.getTempleId();
            mailService.createMail(userId, templeId, uniqueId, mail.getParams(), mail.getRewards());

            log.info("deal mail work: userId={}, uniqueId={}, templeId={}, rowId={}", userId, uniqueId, templeId, v.getRowId());
        });
    }

    public void addMailWork(long userId, String uniqueId, String templeId, List<List<Integer>> rewards, Map<String, String> params) {
        Work work = new Work();

        work.setUserId(userId);
        work.setRowId(UUID.randomUUID().toString());

        work.setType(WorkType.MAIL);
        work.setTime(DateUtils.getUnixTime());
        work.setTtlTime(DateUtils.getUnixTime() + DateUtils.DAY_30_SECONDS);

        Work.MailModel mail = new Work.MailModel();
        mail.setUniqueId(uniqueId);
        mail.setTempleId(templeId);
        mail.setRewards(rewards);
        mail.setParams(params);

        work.setMail(mail);

        workDao.insert(work);
    }

    public void addOreWork(int type, long userId, int mapId, int index, long startTime, long endTime, List<List<Integer>> reward) {
        Work work = new Work();

        work.setUserId(userId);
        work.setRowId(UUID.randomUUID().toString());

        work.setType(type);
        work.setTime(DateUtils.getUnixTime());
        work.setTtlTime(DateUtils.getUnixTime() + DateUtils.DAY_30_SECONDS);

        Work.OreModel model = new Work.OreModel();
        model.setMapId(mapId);
        model.setIndex(index);
        model.setStartTime(startTime);
        model.setEndTime(endTime);
        model.setReward(reward);

        work.setOre(model);

        workDao.insert(work);
    }

    public void addTaxWork(long userId, int tax) {
        Work work = new Work();

        work.setUserId(userId);
        work.setRowId(UUID.randomUUID().toString());

        work.setType(WorkType.TAX);
        work.setTime(DateUtils.getUnixTime());
        work.setTtlTime(DateUtils.getUnixTime() + DateUtils.DAY_30_SECONDS);

        work.setTax(tax);

        workDao.insert(work);
    }

    public Work getOreWork(long userId) {
        List<Work> works = workDao.getAll(userId);
        works = works.stream().filter(v -> v.getType() == WorkType.ORE_HOLD_DONE || v.getType() == WorkType.ORE_BE_LOSE).collect(Collectors.toList());
        if (works.isEmpty()) {
            return null;
        }
        return works.get(0);
    }

    public List<Work> getTaxWork(long userId) {
        return workDao.getAllWorks(userId, WorkType.TAX);
    }

    public List<Work> getMailWork(long userId) {
        return workDao.getAllWorks(userId, WorkType.MAIL);
    }

    public List<Work> getSlgWork(long userId) {
        return workDao.getAllWorks(userId, WorkType.SLG);
    }

    public List<Work> batchGetWorksByIds(Long userId, List<String> rowIds) {
        return Collections.unmodifiableList(workDao.batchGetItem(userId, rowIds));
    }

    public void dealWorkNow(Work work) {
        log.info("dealWork: userId={}, type={}", work.getUserId(), work.getType());
        workDao.deleteNow(work);
    }

    public void dealWork(Work work) {
        log.info("dealWork: userId={}, type={}", work.getUserId(), work.getType());
        workDao.delete(work);
    }
}
