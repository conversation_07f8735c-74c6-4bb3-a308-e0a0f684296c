package com.dxx.game.modules.user.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.amazonaws.xray.spring.aop.XRayEnabled;
import com.dxx.game.common.JSONRespHelper;
import com.dxx.game.common.aws.dynamodb.transaction.DynamoDBTransactionAspectSupport;
import com.dxx.game.common.aws.dynamodb.transaction.DynamoDBWriteType;
import com.dxx.game.common.aws.dynamodb.transaction.annotation.DynamoDBTransactional;
import com.dxx.game.common.channel.AccountService;
import com.dxx.game.common.channel.common.consts.LoginChannelID;
import com.dxx.game.common.httpclient.OkHttpClientUtil;
import com.dxx.game.common.redis.RedisLock;
import com.dxx.game.common.redis.RedisService;
import com.dxx.game.common.redis.annotation.DisableRedisLock;
import com.dxx.game.common.server.context.RequestContext;
import com.dxx.game.common.server.context.ResponseContext;
import com.dxx.game.common.server.model.Result;
import com.dxx.game.common.server.model.UserToken;
import com.dxx.game.common.server.util.RequestIdUtil;
import com.dxx.game.common.utils.*;
import com.dxx.game.config.GameConfigManager;
import com.dxx.game.config.entity.avatar.AvatarEntity;
import com.dxx.game.config.entity.gameconfig.ConfigEntity;
import com.dxx.game.config.entity.iap.MonthCardEntity;
import com.dxx.game.config.entity.item.ItemEntity;
import com.dxx.game.config.entity.playeravatar.PlayerNameEntity;
import com.dxx.game.config.entity.vip.VipEntity;
import com.dxx.game.config.object.IAPConfig;
import com.dxx.game.consts.*;
import com.dxx.game.dao.dynamodb.model.*;
import com.dxx.game.dao.dynamodb.model.gameplay.Conquer;
import com.dxx.game.dao.dynamodb.model.gameplay.UserChapter;
import com.dxx.game.dao.dynamodb.model.guild.Guild;
import com.dxx.game.dao.dynamodb.model.guild.GuildUser;
import com.dxx.game.dao.dynamodb.repository.*;
import com.dxx.game.dao.dynamodb.repository.gameplay.ConquerDao;
import com.dxx.game.dao.dynamodb.repository.gameplay.UserChapterDao;
import com.dxx.game.dao.dynamodb.repository.guild.GuildDao;
import com.dxx.game.dao.dynamodb.repository.guild.GuildUserDao;
import com.dxx.game.dao.redis.BattleUnitCacheRedisDao;
import com.dxx.game.dao.redis.UserFullInfoRedisDao;
import com.dxx.game.dao.redis.UserInfoRedisDao;
import com.dxx.game.dto.CommonProto;
import com.dxx.game.dto.CommonProto.CommonParams;
import com.dxx.game.dto.UserProto;
import com.dxx.game.dto.UserProto.*;
import com.dxx.game.modules.chapter.service.ChapterService;
import com.dxx.game.modules.city.service.CityService;
import com.dxx.game.modules.common.service.CommonService;
import com.dxx.game.modules.common.service.SensitiveWordsService;
import com.dxx.game.modules.common.support.CommonHelper;
import com.dxx.game.modules.crossarena.service.CrossArenaService;
import com.dxx.game.modules.equip.service.EquipService;
import com.dxx.game.modules.event.service.impl.EventServiceImpl;
import com.dxx.game.modules.function.support.FunctionHelp;
import com.dxx.game.modules.guild.service.GuildService;
import com.dxx.game.modules.hero.service.HeroService;
import com.dxx.game.modules.im.IMGroupIdGenerator;
import com.dxx.game.modules.im.IMService;
import com.dxx.game.modules.integralshop.service.IntegralShopService;
import com.dxx.game.modules.item.service.ItemService;
import com.dxx.game.modules.pay.service.PayService;
import com.dxx.game.modules.pvp.support.BattleUnitCache;
import com.dxx.game.modules.pvp.support.PVPHelper;
import com.dxx.game.modules.relic.service.RelicService;
import com.dxx.game.modules.reward.model.SimpleReward;
import com.dxx.game.modules.reward.result.RewardResultSet;
import com.dxx.game.modules.reward.service.RewardService;
import com.dxx.game.modules.server.service.ServerListService;
import com.dxx.game.modules.talentsystem.service.TalentSystemService;
import com.dxx.game.modules.task.support.TaskSupport;
import com.dxx.game.modules.tga.TgaService;
import com.dxx.game.modules.user.HabbyAuthResult;
import com.dxx.game.modules.user.HabbyBindResult;
import com.dxx.game.modules.user.service.UserService;
import com.dxx.game.modules.user.service.UserVersionService;
import com.dxx.game.modules.user.support.TicketHelper;
import com.dxx.game.modules.work.support.WorkSupport;
import com.google.common.collect.Maps;
import io.netty.handler.codec.http.FullHttpRequest;
import io.netty.handler.codec.http.HttpHeaders;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.opensearch.common.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.nio.ByteBuffer;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;
import java.util.zip.CRC32;


@Service
@Slf4j
@XRayEnabled
public class UserServiceImpl implements UserService {

    @Resource
    private CommonService commonService;
    @Resource
    private GameConfigManager gameConfigManager;
    @Resource
    private RewardService rewardService;
    @Resource
    private UserDao userDao;
    @Resource
    private UserExtendDao userExtendDao;
    @Resource
    private ItemService itemService;
    @Resource
    private EquipService equipService;
    @Resource
    private ShopDao shopDao;
    @Resource
    private LogResourceDao logResourceDao;
    @Resource
    private FrozeDeviceDao frozeDeviceDao;
    @Resource
    private AccountService accountService;
    @Resource
    private SensitiveWordsService sensitiveWordsService;
    @Resource
    private UserInfoRedisDao userInfoRedisDao;
    @Resource
    private RedisLock redisLock;
    @Resource
    private EquipDao equipDao;
    @Resource
    private GuildDao guildDao;
    @Resource
    private GuildUserDao guildUserDao;

    @Autowired
    private ChapterService chapterService;
    @Autowired
    private CityService cityService;
    @Autowired
    private HeroService heroService;
    @Autowired
    private IntegralShopService integralShopService;
    @Autowired
    private CrossArenaService crossArenaService;
    @Autowired
    private GuildService guildService;

    @Autowired
    private ReportDao reportDao;
    @Autowired
    private PVPHelper pvpHelper;

    @Autowired
    private TaskSupport taskSupport;
    @Autowired
    private PayService payService;
    @Autowired
    private BattleUnitCacheRedisDao battleRedisDao;
    @Autowired
    private RelicService relicService;
    @Autowired
    private TalentSystemService talentSystemService;
    @Autowired
    private WorkSupport workSupport;
    @Autowired
    private HeroDao heroDao;

    @Resource
    private AccountDao accountDao;
    @Autowired
    private ServerListService serverListService;

    @Autowired
    private FunctionHelp functionHelp;

    @Resource
    private ServerDao serverDao;
    @Resource
    private IMService imService;
    @Autowired
    private RedisService redisService;
    @Autowired
    private BattleUnitCache formationCache;
    @Resource
    private UserFullInfoRedisDao userFullInfoRedisDao;
    @Autowired
    private TgaService tgaService;
    @Resource
    private UserChapterDao userChapterDao;
    @Resource
    private ConquerDao conquerDao;
    @Autowired
    private UserVersionService userVersionService;
    @Autowired
    private EventServiceImpl eventServiceImpl;
    @Autowired
    private TicketHelper ticketHelper;

    @DynamoDBTransactional
    @Override
    public Result<UserLoginResponse> loginAction(UserLoginRequest params, String ip) {
        // 关闭账号注册开关
        boolean closeReg = false;

        // todo client version
        log.info("userLoginRecordStart time:{} account:{} deviceId:{} languageMark:{} serverId:{} clientVersion:{}", System.currentTimeMillis(), params.getCommonParams().getAccountId(), params.getCommonParams().getDeviceId(), params.getCommonParams().getLanguageMark(), params.getCommonParams().getServerId(), 1);
        if ((params.getCommonParams().getDeviceId().isEmpty() && params.getCommonParams().getAccountId().isEmpty())
                || params.getCommonParams().getAccountId().length() >= 256 || params.getCommonParams().getDeviceId().length() >= 128) {
            return Result.Error(ErrorCode.PARAMS_ERROR);
        }

        if (Strings.isNullOrEmpty(params.getCommonParams().getLanguageMark())) {
            return Result.Error(ErrorCode.LOGIN_LANGUAGE_IS_NULL);
        }

        // 设备号是否被封
        if (!StringUtils.isEmpty(params.getCommonParams().getDeviceId())) {
            FrozeDevice frozeDevice = frozeDeviceDao.getItem(params.getCommonParams().getDeviceId());
            if (frozeDevice != null) {
                if (frozeDevice.getTm() == 0 || frozeDevice.getTm() > DateUtils.getUnixTime()) {
                    ResponseContext.setFrozeTime(frozeDevice.getTm());
                    return Result.Error(ErrorCode.USER_BE_SEALED);
                } else {
                    // 自动解封
                    frozeDeviceDao.delete(frozeDevice);
                }
            }
        }

        int channelId = params.getChannelId();

        Pair<User, Account> queryUserResult = this.queryUserOnLogin(params.getCommonParams().getServerId(), params.getCommonParams(), params.getAccountId2());
        Account account = queryUserResult.getRight();
        User user = queryUserResult.getLeft();

        boolean isNewUser = false;
        if (user == null) {
            // google正常注册 其他关闭注册
            if(channelId != 2 && closeReg) {
                return Result.Error(ErrorCode.VERIFY_LOGIN_FAILED);
            }

            // 国内-验证登录
            if (gameConfigManager.isCn() && !accountService.verifyLogin(channelId, params.getCommonParams().getAccountId(), params.getVerification())) {
                return Result.Error(ErrorCode.VERIFY_LOGIN_FAILED);
            }
            int serverId = params.getCommonParams().getServerId();
            if (serverId == 0) {
                serverId = serverListService.generateServerId(params.getCommonParams().getLanguageMark());
                log.info("generate serverId mark:{} serverId:{}", params.getCommonParams().getLanguageMark(), serverId);
            }
            user = this.createUser(serverId, params, ip);

            user.setAccountKey(account.getPK());
            isNewUser = true;
        }

        boolean isNewAccount = false;
        if (account.getServerUserIdMap() == null) {
            account.setServerUserIdMap(Maps.newHashMap());
            isNewAccount = true;
        }
        if (!Objects.equals(user.getUserId(), account.getLastLoginUserId())) {
            account.setLastLoginUserId(user.getUserId());
            account.setChannelId((short) channelId);

            account.getServerUserIdMap().put(user.getServerId(), user.getUserId());
        }
        account.setDeviceId(params.getCommonParams().getDeviceId().trim());
        account.setLastLoginTime(DateUtils.getUnixTime());
        accountDao.updateAccInfo(account);
        updateLastLoginDeviceId(account.getDeviceId(), account.getServerUserIdMap());
        if (!Strings.isNullOrEmpty(account.getAccountId())) {
            user.setAccountId(account.getAccountId().trim());
            user.setAccountId2(account.getAccountId2());
        }
        user.setLastLoginDeviceId(account.getDeviceId());

        UserLoginResponse.Builder response = UserLoginResponse.newBuilder();
        long userId = user.getUserId();
        RequestContext.setUserId(userId);
        RequestContext.setUser(user);
        // TODO 需要玩家数据升级打开
//        userVersionService.checkUserVersion();
        UserExtend userExtend = userExtendDao.getByUserId(userId);
        UserChapter userChapter = userChapterDao.getByUserId(userId);

        Conquer conquer = conquerDao.getByUserId(userId);
        if (!isNewUser) {
            // 非新号
            if (!DateUtils.isSameDay(user.getLoginTimestamp(), DateUtils.getUnixTime())) {
                user.setLoginDays(user.getLoginDays() + 1);
                taskSupport.triggerTask(userId, TaskType.ACHIEVE_TOTAL_LOGIN_DAYS, 1);
            }
        } else {
            //新号

            initNewUser(userExtend);
            userChapter = userChapterDao.initChapter(userId, 1);

            ConfigEntity configEntity901 = gameConfigManager.getGameConfigConfig().getConfigEntity(901);
            String value = configEntity901.getValue();
            List<List<Integer>> rewardConfig = rewardService.stringToRewardList(value);
            for (List<Integer> reward : rewardConfig) {
                rewardService.checkRewardConfig(reward);
                int type = reward.get(0);
                int itemId = reward.get(1);
                int count = reward.get(2);
                if (type == 0) {
                    if (itemId == RewardResourceType.COINS.getValue()) {
                        user.setCoins((long) count);
                    } else if (itemId == RewardResourceType.DIAMONDS.getValue()) {
                        user.setDiamonds((long) count);
                    }
                } else if (type == 3) {
                    Item item = itemService.createItem(userId, itemId, count);
                    response.addItems(CommonHelper.buildItemDto(item));
                }

            }

            userExtendDao.update(userExtend);
            userChapterDao.update(userChapter);
            // 任务
            taskSupport.triggerTask(userId, TaskType.ACHIEVE_TOTAL_LOGIN_DAYS, 1);
            taskSupport.triggerTask(userId, TaskType.ACHIEVE_COUNT_DIFF_HERO, userExtend.getHeroRecords().size());
            List<Long> formation = formationCache.getFormation(GameConstant.FORMATION_TYPE_CHAPTER, userExtend);
            int totalLevel = heroService.totalFormationHeroLevel(userId, formation);
            taskSupport.triggerTask(userId, TaskType.ACHIEVE_TOTAL_FORMATION_HERO_LEVEL, totalLevel);
        }

        initTicket(user);
        checkTicket(user);

        RequestContext.setUserId(userId);
        RequestContext.setUser(user);

        // 封装所有客户端保存阵容
        Map<Integer, List<Long>> formations = userExtend.getFormations();
        for (Map.Entry<Integer, List<Long>> entry : formations.entrySet()) {
            Integer formationType = entry.getKey();
            List<Long> formation = entry.getValue();
            CommonProto.LongArray.Builder formationBuild = CommonProto.LongArray.newBuilder();
            formationBuild.addAllLongArray(formation);
            response.putFormations(formationType, formationBuild.build());
        }

        // 判断是否被封号
        if (this.isFrozeUser(user)) {
            return Result.Error(ErrorCode.USER_BE_SEALED);
        }

        if (channelId == LoginChannelID.WECHAT_MINI_GAME.getId()) {//微信登录
            if (StringUtils.isEmpty(params.getVerification())) {//需要把code2Session获取的verification传回来
                log.error("verification is empty, params:{}", params);
                return Result.Error(ErrorCode.PARAMS_ERROR);
            }
            String jsonVerification = CryptUtil.decode(params.getVerification());
            JSONObject jsonObject = JSONObject.parseObject(jsonVerification);
            if (!jsonObject.containsKey("sessionKey")) {
                log.error("sessionKey is empty, params:{}", params);
                return Result.Error(ErrorCode.PARAMS_ERROR);
            }
            user.setWeChatMiniGameSessionKey(jsonObject.getString("sessionKey"));
        }

        // 判断是否是白名单
//        response.setIsWhite(isWhite(userId));

        loginCheck();


        // 处理各个业务登录逻辑
        cityService.checkChest(userChapter, userExtend);
        integralShopService.check(user, userExtend, userChapter);
        crossArenaService.check(userExtend);

        // 更新最近登录时间戳
        user.setLoginTimestamp(DateUtils.getUnixTime());
        user.setLastLoginDeviceId(params.getCommonParams().getDeviceId());
        user.setClientNetVersion(params.getCommonParams().getVersion());

        int abTest = commonService.getGameConfigIntValue(10001);
        if (abTest == 0) {
            if (user.getAbTest() == null || user.getAbTest() == 0) {
                String country = user.getCountry();
                if (country == null || country.isEmpty() || country.isBlank()) {
                    country = "default";
                }
                String key = RedisKeys.AB + country;
                long count = redisService.incrBy(key);
                if ((count & 1) == 0) {
                    user.setAbTest(GameConstant.AB_A);
                } else {
                    user.setAbTest(GameConstant.AB_B);
                }
            }
        } else if (abTest == GameConstant.AB_A) {
            user.setAbTest(GameConstant.AB_A);
        } else if (abTest == GameConstant.AB_B) {
            user.setAbTest(GameConstant.AB_B);
        }

        initWealCard(userId);

        List<Equip> equips = equipService.getAllEquip(userId);
        List<Hero> heros = heroService.getAllHeros(userId);


        userInfoRedisDao.updateInfo(user, userChapter.getMaxChapterId());
        resetBattleData(userId, heros, userExtend);

        // 战力计算
//        long maxPower = pvpHelper.syncPower(user.getUserId(), "Login");

        userDao.update(user);
        userExtendDao.update(userExtend);

//        userDao.updateUserDataWhenLogin(user);

        String accessToken = createAccessToken(user);
        if (accessToken.isEmpty()) {
            DynamoDBTransactionAspectSupport.setRollBack();
            log.error("createAccessToken failed, userId:{}", userId);
            return Result.Error(ErrorCode.SERVER_SYSTEM_ERROR);
        }

        // 任务
        taskSupport.triggerTask(userId, TaskType.DAILY_LOGIN_NUM, 1);

        response.setUserId(userId);
        response.setLoginDays(user.getLoginDays());
        response.setUserInfoDto(CommonHelper.buildUserInfoDto(user));
        response.setUserCurrency(CommonHelper.buildUserCurrencyMsg(user));
        response.setAccessToken(accessToken);
        response.setTimestamp(DateUtils.getUnixTime());
        response.setSystemMask(user.getSystemMask());
        response.setUserLevel(CommonHelper.buildUserLevelMsg(user));
        response.setGuideMask(user.getGuideMask());
//		response.setCommonData(CommonHelper.buildCommonData());
        response.setRegisterTimestamp(user.getCreateTimestamp());
        response.setUserMission(CommonHelper.buildUserMission(userChapter));
        response.addAllEquipments(equipService.getAllEquipDto(userId, equips));
        response.addAllItems(itemService.getAllItems(userId));
        response.addAllWearEquipRowIds(equipService.getAllEquipIds(equips));
        response.setTransId(RequestIdUtil.getMaxRequestId(userId));
        response.setUserVipLevel(CommonHelper.buildUserVipLevelMsg(user));

        response.setCity(CommonHelper.buildCityDto(userExtend.getCity()));

        response.setTimeZone(DateUtils.strZone);

        response.addAllUserTickets(CommonHelper.buildTicketDtos(user));

        response.setAccountKey(user.getAccountKey());

        if (user.getAbTest() == null) {
            response.setAbTest(0);
        } else {
            response.setAbTest(user.getAbTest());
        }

        Integer serverId = user.getServerId();
        response.setServerId(serverId);
        response.setServerIMGroupId(IMGroupIdGenerator.generateGroupId(IMGroupIdGenerator.GroupType.SERVER, serverId));
        checkCreateServerGroup(serverId);

        if (userExtend.getTalentSystems() != null) {
            response.addAllTalentSystems(userExtend.getTalentSystems().values().stream().map(CommonHelper::buildTalentSystemDto).collect(Collectors.toList()));
        }
        if (userExtend.getRelics() != null) {
            response.addAllRelics(userExtend.getRelics().values().stream().map(CommonHelper::buildRelicDto).collect(Collectors.toList()));
        }
        if (userExtend.getSkins() != null) {
            response.addAllSkins(userExtend.getSkins());
        }
        if (account.getBindStatus() != null) {
            response.setHabbyMailBind(account.getBindStatus());
        }
        if (account.getHabbyEmail() != null) {
            response.setHabbyId(account.getHabbyEmail());
        }
        if (account.getBindReward() != null) {
            response.setHabbyMailReward(account.getBindReward());
        }

        response.addAllHeros(heros.stream().map(CommonHelper::buildHeroDto).collect(Collectors.toList()));

        response.setChapter(CommonHelper.buildChapterDto(userChapter));

        if (userExtend.getHeroBookRewardRecords() != null) {
            response.addAllHeroBookRewards(userExtend.getHeroBookRewardRecords());
        }
        if (userExtend.getHeroScoreCounts() != null) {
            userExtend.getHeroScoreCounts().forEach((k, v) -> {
                CommonProto.DIntInt.Builder data = CommonProto.DIntInt.newBuilder();
                data.setKey(k);
                data.setVal(v);
                response.addHeroBookScoreCounts(data);
            });
        }

        response.setTower(userExtend.getTower());
        if (userExtend.getTowerReward() != null) {
            response.setTowerReward(userExtend.getTowerReward());
        }

        if (conquer.getLord() != null) {
            response.setLord(CommonHelper.buildLordDto(conquer.getLord()));
        }
        response.setSlaveCount((int) conquer.getSlaveIds().stream().distinct().count());

        if (userExtend.getIntegralShopModels() != null) {
            userExtend.getIntegralShopModels().forEach((k, v) -> {
                response.addIntegralShops(CommonHelper.buildIntegralShopDto(v));
            });
        }
        response.addAllOpenModelIdList(userExtend.getOpenModelId());

        response.setGuildTechLv(userExtend.getGuildTechLv());

        if (user.getOpenServerTime() != null) {
            response.setOpenServerTime(getOpenServerTime(user));
            response.setOpenServerResetTime(getSystemOpenServerTime(user));
        }

        if (userExtend.getHeroBondCounts() != null) {
            userExtend.getHeroBondCounts().forEach((k, v) -> {
                CommonProto.DIntInt.Builder data = CommonProto.DIntInt.newBuilder();
                data.setKey(k);
                data.setVal(v);
                response.addHeroBondCounts(data);
            });
        }

        response.setWarId(user.getWarId());

        response.setTgaInfoDto(CommonHelper.buildTgaInfo(account));

        response.setPrivateChatChapterLimit(getPrivateChatChapterLimit(userExtend.getPrivateChatChapterLimit()));

        response.setIsNewAccount(isNewAccount);

        response.setIsNewUser(isNewUser);

        if (userExtend.getDungeonInfoList() != null) {
            response.addAllDungeonInfos(CommonHelper.buildDungeonInfos(userExtend.getDungeonInfoList()));
        }
        if (userChapter != null && !userChapter.getElites().isEmpty()){
            response.putAllEliteChapterInfos(CommonHelper.buildEliteInfos(userChapter.getElites()));
        }
        response.setServerCountry(gameConfigManager.getCountry());
        return Result.Success(response.build());
    }




    public void checkCreateServerGroup(Integer serverId) {
        var server = serverDao.getServer(serverId);
        if (server != null) {
            String imGroupId = server.getImGroupId();
            if (org.apache.commons.lang3.StringUtils.isEmpty(imGroupId)) {
                imGroupId = IMGroupIdGenerator.generateGroupId(IMGroupIdGenerator.GroupType.SERVER, serverId);
                imService.asyncCreateServerGroup(serverId, imGroupId);
            } else {
                if (server.getImGroupStatus() == null || server.getImGroupStatus() == IMGroupStatus.INIT) {
                    imService.asyncCreateServerGroup(serverId, imGroupId);
                }
            }
        } else {
            String imGroupId = IMGroupIdGenerator.generateGroupId(IMGroupIdGenerator.GroupType.SERVER, serverId);
            var openTime = serverListService.getOpenServerTime(serverId);
            imService.asyncCreateServerGroupDao(serverId, imGroupId, openTime);
        }
    }


    @Override
    public long getOpenServerTime(User user) {
        return user.getOpenServerTime();
    }

    @Override
    public long getSystemOpenServerTime(User user) {
        return DateUtils.getSystemResetTimeByTime0(user.getOpenServerTime()
                - DateUtils.ONE_DAY_TIME_STAMP / 1000);
    }

    @Override
    public long getWarOpenServerTime(User user) {
        return serverListService.getServerWarOpenTime(WarType.DEFAULT_WAR_TYPE, user.getServerId());
    }


    @Override
    public long getWarSystemOpenServerTime(User user) {
        return DateUtils.getSystemResetTimeByTime0(getWarOpenServerTime(user) - DateUtils.ONE_DAY_TIME_STAMP / 1000);
    }


    private void initNewUser(UserExtend userExtend) {
        //初始化主城
        cityService.init(userExtend);
        //初始化英雄&主角
        heroService.init(userExtend);
    }

    private void updateLastLoginDeviceId(String deviceId, Map<Integer, Long> serverUserIdMap) {
        serverUserIdMap.values().forEach(temp -> {
            User tempUser = new User();
            tempUser.setUserId(temp.longValue());
            tempUser.setLastLoginDeviceId(deviceId);
            userDao.updateLastDeviceId(tempUser);
        });
    }

    @DynamoDBTransactional
    @Override
    public Result<UserGetInfoResponse> getInfoAction(UserGetInfoRequest params) {
        UserGetInfoResponse.Builder response = UserGetInfoResponse.newBuilder();
        long userId = RequestContext.getUserId();
        User user = this.getUser(userId);
        if (user == null) {
            return Result.Error(ErrorCode.PARAMS_ERROR);
        }

        loginCheck();
        UserChapter userChapter = userChapterDao.getByUserId(userId);
        response.setUserCurrency(CommonHelper.buildUserCurrencyMsg(user));
        response.setUserLevel(CommonHelper.buildUserLevelMsg(user));
        response.setTransId(RequestIdUtil.getMaxRequestId(userId));
        response.setUserMission(CommonHelper.buildUserMission(userChapter));
        response.addAllEquipments(equipService.getAllEquips(userId));
        response.addAllItems(itemService.getAllItems(userId));
        response.setIapInfo(payService.buildUserIAPInfo());
        return Result.Success(response.build());
    }

    @DynamoDBTransactional(DynamoDBWriteType.TRANSACTION)
    @DisableRedisLock
    @Override
    public Result<UserHeartbeatResponse> heartbeatAction(UserHeartbeatRequest params) {
        UserHeartbeatResponse.Builder response = UserHeartbeatResponse.newBuilder();
        userInfoRedisDao.updateActiveTM(RequestContext.getUserId());
        return Result.Success(response.build());
    }

    @DynamoDBTransactional
    @Override
    public Result<UserUpdateSystemMaskResponse> updateSystemMaskAction(UserUpdateSystemMaskRequest params) {
        int position = params.getPosition();
        int value = params.getValue();
        if (position <= 1) {
            return Result.Error(ErrorCode.PARAMS_ERROR);
        }
        if (value < 0 || value > 1) {
            return Result.Error(ErrorCode.PARAMS_ERROR);
        }
        long userId = RequestContext.getUserId();
        User user = this.getUser(userId);

        UserUpdateSystemMaskResponse.Builder response = UserUpdateSystemMaskResponse.newBuilder();
        long mask = user.getSystemMask();

        if (value == 1 && !MaskUtil.isTrue(mask, position)) {
            mask = MaskUtil.setMask(mask, position);
        } else if (value == 0 && MaskUtil.isTrue(mask, position)) {
            mask = MaskUtil.resetMask(mask, position);
        }
        user.setSystemMask(mask);
        userDao.updateSystemMask(user);

        response.setSystemMask(mask);
        return Result.Success(response.build());
    }

    @DynamoDBTransactional
    @Override
    public Result<UserUpdateGuideMaskResponse> updateGudeMaskAction(UserUpdateGuideMaskRequest params) {
        long userId = RequestContext.getUserId();
        User user = this.getUser(userId);

        user.setGuideMask(params.getGuideMask());
        userDao.updateGuideMask(user);
        UserUpdateGuideMaskResponse.Builder response = UserUpdateGuideMaskResponse.newBuilder();
        response.setGuideMask(user.getGuideMask());
        return Result.Success(response.build());
    }

    @DynamoDBTransactional
    @Override
    public Result<UserProto.UserCancelAccountResponse> cancelAccountAction(UserProto.UserCancelAccountRequest params) {
        long userId = RequestContext.getUserId();
        User user = this.getUser(userId);

        if (!StringUtils.isEmpty(user.getAccountId())) {
            user.setAccountId(user.getAccountId() + "_" + DateUtils.getUnixTime());
        }
        if (!StringUtils.isEmpty(user.getDeviceId())) {
            user.setDeviceId(user.getDeviceId() + "_" + DateUtils.getUnixTime());
        }

        userDao.updateAccount(user);

        UserProto.UserCancelAccountResponse.Builder response = UserProto.UserCancelAccountResponse.newBuilder();
        return Result.Success(response.build());
    }

    @DynamoDBTransactional
    @Override
    public Result<UserUpdateInfoResponse> updateInfoAction(UserUpdateInfoRequest params) {
        long userId = RequestContext.getUserId();
        User user = this.getUser(userId);

        String nameId = null;
        try {
            String nickName = params.getNickName().trim();
            int avatar = params.getAvatar();
            int avatarFrame = params.getAvatarFrame();
            boolean isUpdate = false;
            RewardResultSet costResultSet = null;
            if (!StringUtils.isEmpty(nickName) && (user.getNickName() == null || !user.getNickName().equals(nickName))) {
                // 更新昵称
                int costDiamonds = 0;
                int nameLength = nickName.getBytes(StandardCharsets.UTF_8).length;
                int minLen = 3;
                int maxLen = 18;
                if (nameLength < minLen || nameLength > maxLen) {
                    return Result.Error(ErrorCode.USER_NICKNAME_LEN_ERROR);
                }

                nameId = CryptUtil.md5(nickName).toLowerCase();
                // 避免昵称重复
                if (!redisLock.lockWithOutRetry(nameId, String.valueOf(userId), 30000)) {
                    return Result.Error(ErrorCode.USER_NICKNAME_REPEATED);
                }
                //TODO 动态传入
                Long useUserId = userDao.getUserIdByNickName(nickName, user.getServerId());
                if (useUserId != null) {
                    return Result.Error(ErrorCode.USER_NICKNAME_REPEATED);
                }

                boolean isLegal = sensitiveWordsService.checkIsLegal(nickName);
                if (!isLegal) {
                    return Result.Error(ErrorCode.USER_NICKNAME_NOT_LEGAL);
                }

                if (costDiamonds > 0) {
                    SimpleReward cost = SimpleReward.valueOf(RewardResourceType.DIAMONDS, -costDiamonds);
                    costResultSet = rewardService.executeReward(userId, cost);
                    if (costResultSet.isFailed()) {
                        return Result.Error(costResultSet.getResultCode());
                    }
                }

                user.setNickName(nickName);
                isUpdate = true;
                userInfoRedisDao.updateNickName(userId, nickName);
            }

            // 更新头像
            if (avatar > 0 && (user.getAvatar() == null || user.getAvatar() != avatar)) {
                // 判断是否拥有头像
				AvatarEntity avatarEntity = gameConfigManager.getAvatarConfig().getAvatarEntity(avatar);
				if (avatarEntity == null || avatarEntity.getType() != 1) {
					return Result.Error(ErrorCode.PARAMS_ERROR);
				}
//
//				int defaultAvatarId = gameConfigManager.getConstantConfig().getConstantEntity("avatar_default").getTypeInt();
//				if (avatar != defaultAvatarId) {
//					// 是否拥有头像
//					Item item = itemDao.getByItemId(userId, avatar);
//					if (item == null) {
//						return Result.Error(ErrorCode.PARAMS_ERROR);
//					}
//				}
                user.setAvatar(avatar);
                userInfoRedisDao.updateAvatar(userId, avatar);
                isUpdate = true;
            }

            if (avatarFrame > 0 && (user.getAvatarFrame() == null || user.getAvatarFrame() != avatarFrame)) {
				AvatarEntity avatarEntity = gameConfigManager.getAvatarConfig().getAvatarEntity(avatarFrame);
				if (avatarEntity == null || avatarEntity.getType() != 2) {
					return Result.Error(ErrorCode.PARAMS_ERROR);
				}
//				int defaultFrameId = gameConfigManager.getConstantConfig().getConstantEntity("avatarframe_default").getTypeInt();
//				if (avatarFrame != defaultFrameId) {
//					// 是否拥有头像框
//					Item item = itemDao.getByItemId(userId, avatarFrame);
//					if (item == null) {
//						return Result.Error(ErrorCode.PARAMS_ERROR);
//					}
//				}
                user.setAvatarFrame(avatarFrame);
                userInfoRedisDao.updateAvatarFrame(userId, avatarFrame);
                isUpdate = true;
            }

            if (isUpdate) {
                userDao.updateUserInfo(user);
            }

            UserProto.UserUpdateInfoResponse.Builder response = UserProto.UserUpdateInfoResponse.newBuilder();
            response.setCommonData(CommonHelper.buildCommonData(costResultSet));
            response.setUserInfoDto(CommonHelper.buildUserInfoDto(user));
            return Result.Success(response.build());
        } finally {
            if (!StringUtils.isEmpty(nameId)) {
                redisLock.unlock(nameId, String.valueOf(userId));
            }
        }
    }

    @DynamoDBTransactional
    @Override
    public Result<UserGetOtherPlayerInfoResponse> getOtherInfo(UserGetOtherPlayerInfoRequest params) {
        List<Long> userIds = params.getOtherUserIdsList();

        List<PlayerInfoDto> playerInfoDtoList = this.getPlayerInfos(userIds);
        if (playerInfoDtoList.isEmpty()) {
            return Result.Error(ErrorCode.PARAMS_ERROR);
        }

        UserGetOtherPlayerInfoResponse.Builder response = UserGetOtherPlayerInfoResponse.newBuilder();
        response.addAllPlayerInfos(playerInfoDtoList);
        return Result.Success(response.build());
    }

    @DynamoDBTransactional
    @Override
    public Result<UserGetCityInfoResponse> getCityInfo(UserGetCityInfoRequest params) {
        UserGetCityInfoResponse response = getCityInfoResponse(params.getUserId());
        if (response == null) {
            return Result.Error(ErrorCode.PARAMS_ERROR);
        }

        return Result.Success(response);
    }

    @DynamoDBTransactional
    @Override
    public Result<UserHeartbeatSyncResponse> userHeartbeatSync(UserHeartbeatSyncRequest params) {
        UserHeartbeatSyncResponse.Builder response = UserHeartbeatSyncResponse.newBuilder();
        response.setTime(DateUtils.getUnixTime());

        return Result.Success(response.build());
    }

    public void initTicket(User user) {
        long now = DateUtils.getUnixTime();
        long sys = DateUtils.getSystemResetTime();

        if (user.getTicketBuyRefTime() == null) {
            user.setTicketBuyRefTime(sys);
        }

        for (ItemEntity itemEntity : getTicketItems()) {
            if (itemEntity.getItemTypeParam().isEmpty()) {
                log.error("init ticket item type param is empty, itemId={}", itemEntity.getId());
                continue;
            }

            int itemId = itemEntity.getId();
            TicketType ticketType = TicketType.getTicketTypeById(itemId);
            if (ticketType == null) {
                log.error("init ticket item recover type is empty, itemId={}", itemEntity.getId());
                continue;
            }

            // 初始数量
            int initNum = ticketHelper.getTicketInitNum(user.getUserId(), itemId);
            Integer num = user.getTicketNums().get(itemId);
            if (num == null) {
                user.getTicketNums().put(itemId, initNum);
                user.getTicketBuyCounts().put(itemId, 0);
                if (ticketType.getRecoverType() == 1) {
                    user.getTicketRefTimes().put(itemId, now);
                } else {
                    user.getTicketRefTimes().put(itemId, sys);
                }
            }
        }
    }

    @Override
    public void checkTicket(User user) {
        if (isRobot(user)) {
            return;
        }

        long now = DateUtils.getUnixTime();

        if (now >= user.getTicketBuyRefTime()) {
            Map<Integer, Integer> ticketBuyCounts = user.getTicketBuyCounts();
            ticketBuyCounts.forEach((itemId, count) -> {
                ticketBuyCounts.put(itemId, 0);
            });

            user.setTicketBuyRefTime(DateUtils.getSystemResetTime());
        }

        Map<Integer, Integer> ticketNums = user.getTicketNums();
        for (Map.Entry<Integer, Integer> entry : ticketNums.entrySet()) {
            int itemId = entry.getKey();
            Integer num = entry.getValue();

            ItemEntity itemEntity = gameConfigManager.getItemConfig().getItemEntity(itemId);
            if (itemEntity.getItemTypeParam().isEmpty()) {
                log.error("check ticket item type param is empty, itemId={}", itemEntity.getId());
                continue;
            }

            TicketType ticketType = TicketType.getTicketTypeById(itemId);
            if (ticketType == null) {
                log.error("check ticket item recover type is empty, itemId={}", itemEntity.getId());
                continue;
            }

            // 最大数量
            int maxNum = ticketHelper.getTicketMaxNum(user.getUserId(), itemId);
            // 恢复时间/数量
            int recover = ticketHelper.getTicketRecoverNum(user.getUserId(), itemId);

            if (num >= maxNum) {
                continue;
            }

            long refTime = user.getTicketRefTimes().get(itemId);

            int finalNum;
            long finalRefTime;

            if (ticketType.getRecoverType() == 1) {
                int addCount = (int) ((now - refTime) / recover);
                if (addCount <= 0) {
                    continue;
                }

                finalNum = num + addCount;

                if (finalNum > maxNum) {
                    finalRefTime = now;
                } else {
                    finalRefTime = refTime + (long) addCount * recover;
                }
            } else {
                if (now >= refTime) {
                    finalNum = num + recover;
                    finalRefTime = DateUtils.getSystemResetTime();
                } else {
                    finalNum = num;
                    finalRefTime = refTime;
                }
            }

            if (finalNum > maxNum) {
                finalNum = maxNum;
            }

            ticketNums.put(itemId, finalNum);
            user.getTicketRefTimes().put(itemId, finalRefTime);
        }
    }

    @Override
    public RewardResultSet costTicket(TicketType ticketType, long userId, int cost) {
        return rewardService.executeCost(userId, new ArrayList<>(List.of(ticketType.getItemId(), cost)));
    }

    public List<ItemEntity> getTicketItems() {
        return getItemsByType(RewardType.TICKET.getValue());
    }

    public List<ItemEntity> getItemsByType(int type) {
        return gameConfigManager.getItemConfig().getItem().values().stream().filter(v -> v.getItemType() == type).collect(Collectors.toList());
    }

    private void resetBattleData(long userId, List<Hero> heroes, UserExtend userExtend) {
        formationCache.creatCache(userId, heroes, userExtend);
    }

    @Override
    public User getUser(long userId) {
        if (RequestContext.getUser() != null && RequestContext.getUser().getUserId().equals(userId)) {
            return RequestContext.getUser();
        }
        User user = userDao.getByUserId(userId);
        if (user == null) {
            log.error("user data is null, userId = {}", userId);
            return null;
        }

        if (RequestContext.getUserId() != null && RequestContext.getUserId() == userId) {
            checkTicket(user);
            RequestContext.setUser(user);
        }

        return user;
    }


    //用来给记账相关的接口加trans用的
    //考虑调用这个接口的很可能不是当前玩家本身，比如其他玩家触发的所以不要和requestcontext的userid挂上钩
    //还有这个是给log用的。
    //如果是其他玩家，就得给+个1
    @Override
    public long getNextTransId(long userId) {
        User user = this.getUser(userId);
        long next = user.getMaxTransId() + 1;
        user.setMaxTransId(next);
        user.setMaxRequestId(RequestIdUtil.getMaxRequestId(userId));
        userDao.updateMaxTransId(user);
        return next;
    }

    @Override
    public String createAccessToken(User user) {
        UserToken userToken = new UserToken();
        userToken.setUserId(user.getUserId());
        userToken.setLoginTM(user.getLoginTimestamp());
        userToken.setLoginDeviceId(user.getLastLoginDeviceId());
        userToken.setAccountId(user.getAccountId());
        userToken.setAccountKey(user.getAccountKey());
        userToken.setServerIMGroupId(IMGroupIdGenerator.generateGroupId(IMGroupIdGenerator.GroupType.SERVER, user.getServerId()));
        return CryptUtil.encode(JSONObject.toJSONString(userToken));
    }

    @Override
    public int parseAccessToken(CommonParams commonParams) {
        try {
            String accessToken = commonParams.getAccessToken();
            if (accessToken.isEmpty()) {
                log.warn("accessToken is empty, commonParams:{}", commonParams);
                return ErrorCode.USER_ACCESS_TOKEN_ERROR;
            }

            String decodeStr = CryptUtil.decode(accessToken);
            if (decodeStr.isEmpty()) {
                log.warn("decode accessToken is empty, commonParams:{}", commonParams);
                return ErrorCode.USER_ACCESS_TOKEN_ERROR;
            }

            UserToken userToken = JSONObject.parseObject(decodeStr, UserToken.class);
            RequestContext.setUserId(userToken.getUserId());
            RequestContext.setDeviceId(userToken.getLoginDeviceId());
            RequestContext.setAccountId(userToken.getAccountId());
            userInfoRedisDao.updateActiveTM(RequestContext.getUserId());
            return ErrorCode.SUCCESS;
        } catch (Exception e) {
            log.error("parseAccessToken, e:", e);
            log.error("accessToken decode error, commonParams:{}, errMsg:{}", commonParams, e.getMessage());
            return ErrorCode.USER_ACCESS_TOKEN_ERROR;
        }
    }

    @Override
    public int checkLoginState(boolean checkDoubleClient) {
        if (RequestContext.getUserId() == null) {
            return ErrorCode.USER_ACCESS_TOKEN_ERROR;
        }
        User user = this.getUser(RequestContext.getUserId());
        if (user == null) {
            log.error("getUser is null, userId:{}", RequestContext.getUserId());
            return ErrorCode.USER_LOGIN_FAILED;
        }

        // 是否被封号
        if (this.isFrozeUser(user)) {
            return ErrorCode.USER_BE_SEALED;
        }

        // 重复登录
        if (checkDoubleClient) {
            if (!StringUtils.isEmpty(RequestContext.getDeviceId()) && !user.getLastLoginDeviceId().equals(RequestContext.getDeviceId())) {
                log.error("login repeated, userId:{}, lastLoginDeviceId:{}, deviceId:{}", RequestContext.getUserId(), user.getLastLoginDeviceId(), RequestContext.getDeviceId());
                return ErrorCode.USER_LONGIN_REPEAT;
            }
        }

        return ErrorCode.SUCCESS;
    }

    @Override
    public int getChapterId(long userId) {
        UserChapter userChapter = userChapterDao.getByUserId(userId);
        return userChapter.getMaxChapterId();
    }

    private String randomName() {
        List<PlayerNameEntity> entities = new ArrayList<>(gameConfigManager.getPlayerAvatarConfig().getPlayerName().values());
        int randomIndex = RandomUtil.nextInt(entities.size() - 1);

        return entities.get(randomIndex).getName();
    }

    private int randomAvatar() {
        List<AvatarEntity> as = gameConfigManager.getAvatarConfig().getAvatar().values().stream().filter(v -> v.getType() == 1 && v.getUnLockType() == 1).collect(Collectors.toList());
        List<AvatarEntity> entities = new ArrayList<>(as);
        int randomIndex = RandomUtil.nextInt(entities.size() - 1);

        return entities.get(randomIndex).getId();
    }

    @Override
    public User createRobot(int serverId, long power) {
        long robotUid = commonService.generateUserId();

        String deviceId = "ROBOT_" + robotUid;

        User user = new User();
        user.setUserId(robotUid);
        user.setDeviceId(deviceId);
        user.setAccountId(deviceId);
        user.setServerId(serverId);
        user.setAvatar(randomAvatar());
        user.setNickName(randomName());
        user.setPower(power);
        user.setVipLevel((short) 0);
        user.setLoginTimestamp(DateUtils.getUnixTime());

        user.setIsRobot(true);

        userDao.insert(user);

        UserExtend userExtend = UserExtend.init(robotUid);
        initNewUser(userExtend);
        userExtendDao.insert(userExtend);

        UserChapter userChapter = userChapterDao.initChapter(robotUid, 1);
        userChapterDao.insert(userChapter);

        Conquer conquer = new Conquer(robotUid);
        conquer.setSlaveIds(new ArrayList<>());
        conquer.setHistoryLords(new ArrayList<>());
        conquerDao.insert(conquer);

        return user;
    }

    public boolean isRobot(User user) {
        return user.getIsRobot() != null && user.getIsRobot();
    }

    @Override
    public User DevCreateRobot(int serverId) {
        long robotUid = commonService.generateUserId();
        String deviceId = "ROBOT_" + robotUid;

        UserProto.UserLoginRequest.Builder p = UserProto.UserLoginRequest.newBuilder();
        p.setChannelId(0);
        p.setAccountId2(deviceId);

        CommonParams.Builder b = CommonParams.newBuilder();
        b.setDeviceId(deviceId);
        b.setServerId(serverId);
        b.setLanguageMark("EDITOR");
        b.setAccountId(deviceId);

        p.setCommonParams(b);

        Account account = new Account();
        account.setPK(deviceId);
        account.setDeviceId(deviceId);
        account.setAccountId(deviceId);
        account.setAccountId2(deviceId);
        account.setCreateTime(DateUtils.getUnixTime());
        account.setLastLoginUserId(robotUid);
        accountDao.insert(account);

        if (serverId == 0) {
            serverId = serverListService.generateServerId(b.getLanguageMark());
        }

        User user = this.createUser(robotUid, serverId, p.build(), "0.0.0.0");
        user.setAccountKey(account.getPK());
        user.setIsRobot(true);

        UserExtend userExtend = userExtendDao.getByUserId(robotUid);
        initNewUser(userExtend);
        userExtend.setTower(701);
        userExtendDao.update(userExtend);

        UserChapter userChapter = userChapterDao.initChapter(robotUid, 19);
        userChapterDao.insert(userChapter);

        account.setServerUserIdMap(Maps.newHashMap());
        account.getServerUserIdMap().put(user.getServerId(), user.getUserId());

        accountDao.updateAccInfo(account);
        return user;
    }


    /**
     * 创建新用户
     *
     * @param params
     * @param ip
     * @return
     */
    @Override
    public User createUser(long userId, int serverId, UserLoginRequest params, String ip) {
            // 初始金币
            long initCoins = 100;
            // 初始钻石
            long initDiamonds = 100;
            // 初始等级
            int initLevel = 1;

            User user = new User();
//        if (!StringUtils.isEmpty(params.getCommonParams().getDeviceId())) {
//            user.setDeviceId(params.getCommonParams().getDeviceId());
//        }
            if (!StringUtils.isEmpty(params.getCommonParams().getAccountId())) {
                user.setAccountId(params.getCommonParams().getAccountId());
            }
            if (!StringUtils.isEmpty(params.getCommonParams().getLanguageMark())) {
                user.setLanguageMark(params.getCommonParams().getLanguageMark());
        }

        user.setAccountId2(params.getAccountId2());
        user.setChannelId((short) params.getChannelId());
        user.setUserId(userId);
        user.setCoins(initCoins);
        user.setDiamonds(initDiamonds);
        user.setLevel((short) initLevel);
        user.setExp(0);
        user.setAccountStatus((short) 0);
        user.setLoginTimestamp(DateUtils.getUnixTime());
        user.setUserIp(ip);
        user.setCreateTimestamp(DateUtils.getUnixTime());
        user.setLastLoginDeviceId(user.getDeviceId());
        user.setCountry(RequestContext.getCountryCode());
        user.setLoginDays(1);

        user.setMaxTransId(RequestIdUtil.DEFAULT_TRANS_ID);
        user.setMaxRequestId(RequestIdUtil.DEFAULT_TRANS_ID);
        user.setSystemMask(0L);
        user.setGuideMask(0L);

        user.setVipExp(0);
        user.setVipLevel((short) 0);

        user.setPower(0L);

        user.setServerId(serverId);
        user.setOpenServerTime(serverListService.getOpenServerTime(user.getServerId()));

        user.setWarId(serverListService.getServerWarId(serverId, WarType.DEFAULT_WAR_TYPE));

        user.setTicketNums(new HashMap<>());
        user.setTicketRefTimes(new HashMap<>());
        user.setTicketBuyCounts(new HashMap<>());

        userDao.insert(user);

        // 初始化userExtend表
        UserExtend userExtend = UserExtend.init(userId);

        userExtendDao.insert(userExtend);

        UserChapter userChapter = userChapterDao.initChapter(userId, 1);
        userChapterDao.insert(userChapter);

        Shop shop = Shop.init(userId);
        shopDao.insertNow(shop);

        serverListService.onUserCreate(serverId);

        Conquer conquer = new Conquer(userId);
        conquer.setSlaveIds(new ArrayList<>());
        conquer.setHistoryLords(new ArrayList<>());
        conquerDao.insert(conquer);

        return user;
    }


    @Override
    public User createUser(int serverId, UserLoginRequest params, String ip) {
        Long userId = commonService.generateUserId();
        return createUser(userId, serverId, params, ip);
    }

    private void initWealCard(long userId) {
        Shop shop = shopDao.getByUserId(userId);

        IAPConfig iapConfig = gameConfigManager.getIAPConfig();

        int configId = 303;

        MonthCardEntity monthCardEntity = iapConfig.getMonthCardEntity(configId);
        Shop.IAPModel iap = shop.getIap();
        Map<Integer, Shop.IAPMonthCardModel> monthCardMap = iap.getMonthCardMap();
        if (!monthCardMap.containsKey(configId)) {
            Shop.IAPMonthCardModel iapMonthCardModel = new Shop.IAPMonthCardModel();
            iapMonthCardModel.setId(configId);
            monthCardMap.put(configId, iapMonthCardModel);
        }
        Shop.IAPMonthCardModel iapMonthCardModel = monthCardMap.get(configId);
        int lastCount = iapMonthCardModel.getLastCount();
        if (monthCardEntity.getDuration() == 0) {
            lastCount = Integer.MAX_VALUE - 1;
        }
        iapMonthCardModel.setLastCount(lastCount + monthCardEntity.getDuration());

        shopDao.update(shop);
    }

    /**
     * 登录检查
     */
    private void loginCheck() {
        long userId = RequestContext.getUserId();
        User user = this.getUser(userId);

        // 内购服务登录检查
        payService.loginCheck();

        //月卡
        payService.checkMonthCard();

        tgaService.addTotalLoginDays(user);
    }

    /**
     * 是否被封号
     *
     * @param user
     * @return
     */
    private boolean isFrozeUser(User user) {
        // 判断是否被封号
        if (user.getAccountStatus() == 1) {
            if (user.getFrozeTime() == 0 || user.getFrozeTime() > DateUtils.getUnixTime()) {
                ResponseContext.setFrozeTime(user.getFrozeTime());
                return true;
            } else {
                // 自动解封
                user.setAccountStatus((short) 0);
                user.setFrozeTime(0L);
            }
        }
        return false;
    }

    @Override
    public long getIncreTransId(long userId) {
        return commonService.generateUserTransId(userId);
    }

    public UserGetCityInfoResponse getCityInfoResponse(long userId) {
        User user = userDao.getByUserId(userId);
        if (user == null) {
            return null;
        }

        Conquer conquer = conquerDao.getByUserId(userId);

        UserGetCityInfoResponse.Builder response = UserGetCityInfoResponse.newBuilder();
        response.setUserId(userId);
        response.setNickName(user.getNickName() == null ? "" : user.getNickName());
        response.setAvatar(user.getAvatar() == null ? 0 : user.getAvatar());
        response.setAvatarFrame(user.getAvatarFrame() == null ? 0 : user.getAvatarFrame());

        if (conquer.getLord() != null) {
            response.setExtra(CommonHelper.buildLordDto(conquer.getLord()));
        }

        response.setPower(user.getPower());

        GuildUser guildUser = guildUserDao.getByUserId(userId);
        if (guildUser != null && guildUser.getGuildId() > 0) {
            response.setGuildId(guildUser.getGuildId());
            Guild guild = guildDao.getByGuildId(guildUser.getGuildId());
            response.setGuildName(guild.getGuildName());
        }

        return response.build();
    }

    public List<PlayerInfoDto> getPlayerInfosByCache(List<Long> userIds) {
        Map<Long, UserProto.PlayerInfoDto> list = userFullInfoRedisDao.getData(userIds);
        List<Long> noCache = new ArrayList<>();
        for (Long userId : userIds) {
            if (list.get(userId) == null) {
                noCache.add(userId);
            }
        }
        if (!noCache.isEmpty()) {
            List<PlayerInfoDto> infos = getPlayerInfos(noCache);
            userFullInfoRedisDao.updateInfos(infos);
            infos.addAll(list.values());
            return infos;
        } else {
            return list.values().stream().collect(Collectors.toList());
        }
    }

    //    @Override
    private List<PlayerInfoDto> getPlayerInfos(List<Long> userIds) {
        Map<Long, User> users = userDao.getByUserIds(userIds);
        Map<Long, UserExtend> userExtendMap = userExtendDao.getByUserIds(userIds);
        Map<Long, UserChapter> userChapterMap = userChapterDao.getByUserIds(userIds);
        Map<Long, Conquer> conquerMap = conquerDao.getByUserIds(userIds);
        List<PlayerInfoDto> playerInfoDtos = new ArrayList<>();
        for (Long userId : userIds) {
            User user = users.get(userId);
            if (user == null) {
                continue;
            }

            UserExtend userExtend = userExtendMap.get(user.getUserId());
            UserChapter userChapter = userChapterMap.get(user.getUserId());
            Conquer conquer = conquerMap.get(user.getUserId());

            PlayerInfoDto.Builder builder = PlayerInfoDto.newBuilder();

            builder.setNickName(user.getNickName() == null ? "" : user.getNickName());
            builder.setUserId(user.getUserId());
            builder.setAvatar(user.getAvatar() == null ? 0 : user.getAvatar());
            builder.setAvatarFrame(user.getAvatarFrame() == null ? 0 : user.getAvatarFrame());
            builder.setLastLoginTimestamp(user.getLoginTimestamp());

            builder.setChapterId(userChapter.getMaxChapterId());
            builder.setWaveIndex(userChapter.getMaxWaveIndex());

//            builder.setHero(CommonHelper.buildHeroDto(heroService.getHero(userId, userExtend.getActorModel().getRowId())));

            List<CommonProto.HeroDto> heroDtos = new ArrayList<>();
            List<Long> formation = formationCache.getFormation(GameConstant.FORMATION_TYPE_CHAPTER, userExtend);
            List<Long> heroRowIds = formation.stream().filter(Objects::nonNull).filter(v -> v != 0L).collect(Collectors.toList());
            Map<Long, Hero> heroMap = heroService.getAllHeros(userId, heroRowIds).stream().collect(Collectors.toMap(Hero::getRowId, hero -> hero));
            formation.forEach(v -> {
                if (v != null && v != 0L) {
                    heroDtos.add(CommonHelper.buildHeroDto(heroMap.get(v)));
                }
            });
            builder.addAllFormationHero(heroDtos);
            builder.addAllFormationRowIds(formation);

            if (conquer != null && conquer.getLord() != null) {
                builder.setExtra(CommonHelper.buildLordDto(conquer.getLord()));
            }

            List<Long> equipDress = new ArrayList<>();
            heroMap.forEach((k, v) -> {
                if (v.getEquips() != null) {
                    for (Equip equip : v.getEquips().values()) {
                        equipDress.add(equip.getRowId());
                    }
                }
            });

            if (!equipDress.isEmpty()) {
                equipDao.getListByRowIds(userId, equipDress).forEach(v -> {
                    builder.addEquipments(CommonHelper.buildEquipmentDto(v));
                });
            }

            builder.setPower(user.getPower());
            builder.setPrivateChatChapterLimit(getPrivateChatChapterLimit(userExtend.getPrivateChatChapterLimit()));

            GuildUser guildUser = guildUserDao.getByUserId(userId);

            if (guildUser != null) {
                builder.setGuildId(String.valueOf(guildUser.getGuildId()));
                Guild guild = guildDao.getByGuildId(guildUser.getGuildId());
                if (guild != null) {
                    builder.setGuildName(guild.getGuildName() == null ? "" : guild.getGuildName());
                    builder.setGuildIcon(guild.getGuildIcon() == null ? 0 : guild.getGuildIcon());
                    builder.setGuildIconBg(guild.getGuildIconBg() == null ? 0 : guild.getGuildIconBg());
                }
            }

            playerInfoDtos.add(builder.build());
        }
        return playerInfoDtos;
    }

    @Override
    public int getPrivateChatChapterLimit(Integer value) {
        if (value == null || value == 0) {
            var data = gameConfigManager.getFunctionConfig().getFunctionEntity(FunctionTypeEnum.FUNCTION_CHAT.getFunctionKey()).getUnlockArgs();
            return Integer.parseInt(data);
        }
        return value;
    }

    @DynamoDBTransactional
    @Override
    public Result<UserTicketInfoResponse> userTicketInfo(UserTicketInfoRequest params) {
        long userId = RequestContext.getUserId();
        User user = getUser(userId);

        userDao.updateTicket(user);

        UserTicketInfoResponse.Builder response = UserTicketInfoResponse.newBuilder();
        response.setCommonData(CommonHelper.buildCommonData().toBuilder().addAllUserTickets(CommonHelper.buildTicketDtos(user)).build());
        return Result.Success(response.build());

    }

    @DynamoDBTransactional
    @Override
    public Result<UserExchangeItemResponse> userExchangeItem(UserExchangeItemRequest params) {
        long userId = RequestContext.getUserId();

        int itemId = params.getItemId();
        int count = params.getCount();

        if (!CommonUtils.checkUseCountLimit(count)) {
            return Result.Error(ErrorCode.PARAMS_ERROR);
        }

        ItemEntity itemEntity = gameConfigManager.getItemConfig().getItemEntity(itemId);
        if (itemEntity == null) {
            return Result.Error(ErrorCode.CONFIG_NOT_EXIST);
        }

        if (itemEntity.getSubType() != 31) {
            return Result.Error(ErrorCode.PARAMS_ERROR);
        }

        List<List<Integer>> costs = itemEntity.getItemTypeParam();
        if (costs == null || costs.isEmpty()) {
            return Result.Error(ErrorCode.PARAMS_ERROR);
        }

        List<Integer> cost = costs.get(0);

        RewardResultSet costResultSet = rewardService.executeCost(userId, new ArrayList<>(List.of(cost.get(0), cost.get(1) * count)));
        if (costResultSet.isFailed()) {
            return Result.Error(costResultSet.getResultCode());
        }

        RewardResultSet rewardResultSet = rewardService.executeReward(userId, new ArrayList<>(List.of(itemId, count)));
        if (rewardResultSet.isFailed()) {
            return Result.Error(rewardResultSet.getResultCode());
        }

        UserExchangeItemResponse.Builder response = UserExchangeItemResponse.newBuilder();
        response.setCommonData(CommonHelper.buildCommonData(costResultSet, rewardResultSet));
        return Result.Success(response.build());
    }

    @DynamoDBTransactional
    @Override
    public Result<UserSyncPowerResponse> syncPower(UserSyncPowerRequest params) {
        long maxPower = pvpHelper.syncPower(RequestContext.getUserId(), "SyncPower");

        UserSyncPowerResponse.Builder response = UserSyncPowerResponse.newBuilder();
        response.setCommonData(CommonHelper.buildCommonData());
        response.setHistoryMaxPower(maxPower);
        return Result.Success(response.build());
    }

    @DynamoDBTransactional
    @Override
    public Result<UserGetBattleReportResponse> getBattleReport(UserGetBattleReportRequest params) {
        long reportId = params.getReportId();

        Report report = reportDao.getByRowId(reportId);
        if (report == null) {
            return Result.Error(ErrorCode.PARAMS_ERROR);
        }

        UserGetBattleReportResponse.Builder response = UserGetBattleReportResponse.newBuilder();
        response.setRecord(pvpHelper.buildPVPRecord(report));

        return Result.Success(response.build());
    }

    @Override
    @DynamoDBTransactional
    public Result<UserOpenModelResponse> userOpenModel(UserOpenModelRequest params) {
        Long userId = RequestContext.getUserId();
        UserExtend userExtend = userExtendDao.getByUserId(userId);
        UserChapter userChapter = userChapterDao.getByUserId(userId);
        List<Integer> modelId = params.getModelIdsList();
        List<Integer> openModelId = userExtend.getOpenModelId();

        for (Integer v : modelId) {
            if (!openModelId.contains(v)) {
                if (!functionHelp.openFunction(userId, v, userExtend, userChapter)) {
                    return Result.Error(ErrorCode.PARAMS_ERROR);
                } else {
                    openModelId.add(v);
                    if (v == FunctionTypeEnum.FUNCTION_CHEST_FACTORY.getFunctionKey()) {
                        cityService.checkChest(userChapter, userExtend);
                    } else if (v == FunctionTypeEnum.FUNCTION_FIRST_RECHARGE.getFunctionKey()) {
                        payService.initFirstRecharge();
                    }
                }
            }
        }

        userExtendDao.update(userExtend);
        UserOpenModelResponse.Builder builder = UserOpenModelResponse.newBuilder();
        return Result.Success(builder.build());
    }

    @Override
    @DynamoDBTransactional
    public Result<UserSetFormationByTypeResponse> userSetFormationByType(UserSetFormationByTypeRequest params) {
        int formationType = params.getFormationType();
        if (formationType < GameConstant.FORMATION_TYPE_CHAPTER || formationType > GameConstant.FORMATION_TYPE_SLG) {
            return Result.Error(ErrorCode.PARAMS_ERROR);
        }

        long userId = RequestContext.getUserId();
        UserExtend userExtend = userExtendDao.getByUserId(userId);

        List<Long> formation = params.getFormation().getLongArrayList();

        if (formation.isEmpty()) {
            return Result.Error(ErrorCode.PARAMS_ERROR);
        }
        if (formation.size() != 5) {
            return Result.Error(ErrorCode.PARAMS_ERROR);
        }
        if (formation.stream().allMatch(v -> v == 0)) {
            return Result.Error(ErrorCode.PARAMS_ERROR);
        }

        Set<Long> seen = new HashSet<>();
        if (formation.stream().filter(num -> num != 0).anyMatch(num -> !seen.add(num))){
            return Result.Error(ErrorCode.PARAMS_ERROR);
        };

        if (formationType == GameConstant.FORMATION_TYPE_EXPEDITION) {
            for (Long id : formation) {
                if (id == 0) {
                    return Result.Error(ErrorCode.EXPEDITION_FORMATION_ERROR);
                }
            }
        }


        List<Long> ids = heroDao.getAllByUserId(userId).stream().map(Hero::getRowId).collect(Collectors.toList());
        for (Long id : formation) {
            if (id != 0 && !ids.contains(id)) {
                return Result.Error(ErrorCode.PARAMS_ERROR);
            }
        }

        userExtend.getFormations().put(formationType, formation);
        userExtendDao.update(userExtend);

        //battleRedisDao.updateFormationHero(userId, pvpHelper.getFormationMap(userExtend.getFormations(), heroService.getHeroByFormations(userId, userExtend.getFormations())));
        battleRedisDao.removeData(userId);


        if (formationType == GameConstant.FORMATION_TYPE_CHAPTER) {
            pvpHelper.syncPower(RequestContext.getUserId(), "SetFormation");

            // 任务
            int totalLevel = heroService.totalFormationHeroLevel(userId, formation);
            taskSupport.triggerTask(userId, TaskType.ACHIEVE_TOTAL_FORMATION_HERO_LEVEL, totalLevel);
        }

        UserSetFormationByTypeResponse.Builder builder = UserSetFormationByTypeResponse.newBuilder();
        builder.setCommonData(CommonHelper.buildCommonData());
        return Result.Success(builder.build());
    }

    @Override
    @DynamoDBTransactional
    public Result<UserGetFormationByTypeResponse> userGetFormationByType(UserGetFormationByTypeRequest params) {
        Long userId = RequestContext.getUserId();
        UserExtend userExtend = userExtendDao.getByUserId(userId);
        int formationType = params.getFormationType();
        Map<Integer, List<Long>> formations = userExtend.getFormations();
        List<Long> formation = new ArrayList<>();
        if (formations.containsKey(formationType)) {
            formation = formations.get(formationType);
        }
        CommonProto.LongArray.Builder longBuilder = CommonProto.LongArray.newBuilder();
        longBuilder.addAllLongArray(formation);
        UserGetFormationByTypeResponse.Builder builder = UserGetFormationByTypeResponse.newBuilder();
        builder.setFormationType(formationType);
        builder.setFormation(longBuilder.build());
        return Result.Success(builder.build());
    }

    @Override
    public double vipPermissionValueCheck(long userId, VipPermission vipPermission) {
        return Double.parseDouble(getVipPermissionValue(userId, vipPermission));
    }

    public int vipPermissionValueIntCheck(long userId, VipPermission vipPermission) {
        return Integer.parseInt(getVipPermissionValue(userId, vipPermission));
    }

    public String getVipPermissionValue(long userId, VipPermission vipPermission) {
        User user = this.getUser(userId);
        int vip = user.getVipLevel();

        if (vip > 0) {
            VipEntity vipEntity = gameConfigManager.getVipConfig().getVipEntity(vip);
            if (Objects.requireNonNull(vipPermission) == VipPermission.PERMISSION_3) {
                return vipEntity.getPower3().isEmpty() ? "0" : vipEntity.getPower3();
            } else if (Objects.requireNonNull(vipPermission) == VipPermission.PERMISSION_8) {
                return vipEntity.getPower8().isEmpty() ? "0" : vipEntity.getPower8();
            } else if (Objects.requireNonNull(vipPermission) == VipPermission.PERMISSION_10) {
                return vipEntity.getPower10().isEmpty() ? "0" : vipEntity.getPower10();
            } else if (Objects.requireNonNull(vipPermission) == VipPermission.PERMISSION_11) {
                return vipEntity.getPower11().isEmpty() ? "0" : vipEntity.getPower11();
            } else if (Objects.requireNonNull(vipPermission) == VipPermission.PERMISSION_12) {
                return vipEntity.getPower12().isEmpty() ? "0" : vipEntity.getPower12();
            } else if (Objects.requireNonNull(vipPermission) == VipPermission.PERMISSION_13) {
                return vipEntity.getPower13().isEmpty() ? "0" : vipEntity.getPower13();
            } else if (Objects.requireNonNull(vipPermission) == VipPermission.PERMISSION_14) {
                return vipEntity.getPower14().isEmpty() ? "0" : vipEntity.getPower14();
            } else if (Objects.requireNonNull(vipPermission) == VipPermission.PERMISSION_15) {
                return vipEntity.getPower15().isEmpty() ? "0" : vipEntity.getPower15();
            } else if (Objects.requireNonNull(vipPermission) == VipPermission.PERMISSION_16) {
                return vipEntity.getPower16().isEmpty() ? "0" : vipEntity.getPower16();
            } else if (Objects.requireNonNull(vipPermission) == VipPermission.PERMISSION_18) {
                return vipEntity.getPower18().isEmpty() ? "0" : vipEntity.getPower18();
            } else if (Objects.requireNonNull(vipPermission) == VipPermission.PERMISSION_19) {
                return vipEntity.getPower19().isEmpty() ? "0" : vipEntity.getPower19();
            }
        }
        return "0";
    }

    @Override
    public Double cardPermissionValueCheck(long userId, CardPermission permission) {
        long now = DateUtils.getUnixTime();

        Shop shop = shopDao.getByUserId(userId);
        if (shop == null) {
            return null;
        }
        Shop.IAPModel iapModel = shop.getIap();
        if (iapModel == null) {
            return null;
        }

        if (permission == CardPermission.PERMISSION_1) {
            double d = 0;

            for (MonthCardEntity entity : gameConfigManager.getIAPConfig().getMonthCard().values()) {
                Shop.IAPMonthCardModel model = iapModel.getMonthCardMap().get(entity.getId());
                if (model == null || now > model.getExpireTime()) {
                    continue;
                }

                d = d + Double.parseDouble(entity.getPower1().isEmpty() ? "0" : entity.getPower1());
            }

            return d;
        }

        if (permission == CardPermission.PERMISSION_2) {
            double d = 0;

            for (MonthCardEntity entity : gameConfigManager.getIAPConfig().getMonthCard().values()) {
                Shop.IAPMonthCardModel model = iapModel.getMonthCardMap().get(entity.getId());
                if (model == null || now > model.getExpireTime()) {
                    continue;
                }

                d = d + Double.parseDouble(entity.getPower2().isEmpty() ? "0" : entity.getPower2());
            }

            return d;
        }

        if (permission == CardPermission.PERMISSION_3) {
            double d = 0;

            for (MonthCardEntity entity : gameConfigManager.getIAPConfig().getMonthCard().values()) {
                Shop.IAPMonthCardModel model = iapModel.getMonthCardMap().get(entity.getId());
                if (model == null || now > model.getExpireTime()) {
                    continue;
                }

                d = d + Double.parseDouble(entity.getPower3().isEmpty() ? "0" : entity.getPower3());
            }

            return d;
        }

        return null;
    }

    /**
     * 查找用户
     *
     * @param commonParams
     * @param accountId2
     * @return
     */
    private Pair<User, Account> queryUserOnLogin(int serverId, CommonParams commonParams, String accountId2) {
        Account account = null;
        User user = null;
        if (!Strings.isNullOrEmpty(commonParams.getAccountId())) {
            account = accountDao.getByAccountId(commonParams.getAccountId().trim());
            if (account != null && !Strings.isNullOrEmpty(commonParams.getDeviceId())) {
                if (Strings.isNullOrEmpty(account.getDeviceId()) || !account.getDeviceId().equals(commonParams.getDeviceId())) {
                    account.setDeviceId(commonParams.getDeviceId().trim());
                    accountDao.updateAccInfo(account);
                }
            }
        }
        if (account == null && !Strings.isNullOrEmpty(commonParams.getDeviceId())) {
            account = accountDao.getByDeviceId(commonParams.getDeviceId().trim());
            if (account != null && !Strings.isNullOrEmpty(account.getAccountId()) && !Strings.isNullOrEmpty(commonParams.getAccountId())
                    && !account.getAccountId().trim().equals(commonParams.getAccountId().trim())) {
                account = null;
            }
        }
        if (account == null) {
            account = new Account();
            account.setPK(UUID.randomUUID().toString().replaceAll("-", ""));
            account.setCreateTime(DateUtils.getUnixTime());
            if (!Strings.isNullOrEmpty(commonParams.getAccountId())) {
                account.setAccountId(commonParams.getAccountId().trim());
            }
            account.setAccountId2(accountId2);
            if (!Strings.isNullOrEmpty(commonParams.getDeviceId())) {
                account.setDeviceId(commonParams.getDeviceId().trim());
            }
            account.setLastLoginUserId(0L);
            accountDao.insert(account);
        }
        if (Strings.isNullOrEmpty(account.getAccountId()) && !Strings.isNullOrEmpty(commonParams.getAccountId())) {
            account.setAccountId(commonParams.getAccountId().trim());
            account.setAccountId2(accountId2);
            accountDao.updateAccInfo(account);
        }
        long userId = account.getLastLoginUserId() == null ? 0 : account.getLastLoginUserId();
        if (serverId != 0) {
            userId = account.getServerUserIdMap().getOrDefault(serverId, 0L);
        }
        if (userId > 0) {
            user = userDao.getByUserId(userId);

        }
        return Pair.of(user, account);
    }

    @Override
    public void vipPermissionTrigger(long userId) {
        User user = this.getUser(userId);
        int vip = user.getVipLevel();
        if (vip > 0) {
            UserExtend userExtend = userExtendDao.getByUserId(userId);
            VipEntity vipEntity = gameConfigManager.getVipConfig().getVipEntity(vip);

            int needSave = 0;

            // 黑市刷新次数
            int power15 = Integer.parseInt(vipEntity.getPower15().isEmpty() ? "0" : vipEntity.getPower15());
            if (power15 > 0) {
                UserExtend.IntegralShopModel shopModel = userExtend.getIntegralShopModels().get(ShopIdEnum.SHOP_BLACK.getId());
                List<Integer> refreshCost = gameConfigManager.getIntegralShopConfig().getDataEntity(ShopIdEnum.SHOP_BLACK.getId()).getRefreshCost();
                int defaultNum = refreshCost == null ? 0 : refreshCost.size();
                shopModel.setMaxNum(defaultNum + power15);

                needSave++;
            }

            if (needSave > 0) {
                userExtendDao.update(userExtend);
            }
        }
    }

    @DynamoDBTransactional
    @Override
    public Result<UserTriggerResponse> userTrigger(UserTriggerRequest params) {
        long userId = RequestContext.getUserId();
        User user = RequestContext.getUser();

        workSupport.dealMailWork(userId);
        serverListService.checkStatus(user.getServerId());

        UserTriggerResponse.Builder response = UserTriggerResponse.newBuilder();
        return Result.Success(response.build());
    }

    @Override
    public Map<String, Object> parseAccessTokenForInternal(HttpHeaders headers, FullHttpRequest fullHttpRequest) {
        String host = fullHttpRequest.headers().get("Host");
        if (!host.startsWith("internal-")) {
            return null;
        }

        String authorization = headers.get("Authorization");

        if (StringUtils.isEmpty(authorization)) {
            return JSONRespHelper.error(JSONRespHelper.FAIL, "Authorization is empty");
        }

        String accessToken = authorization.replace("Bearer ", "");
        if (accessToken.isEmpty()) {
            return JSONRespHelper.error(JSONRespHelper.FAIL, "accessToken is empty");
        }

        String decodeStr = CryptUtil.decode(accessToken);
        if (decodeStr.isEmpty()) {
            return JSONRespHelper.error(JSONRespHelper.FAIL, "accessToken decode error");
        }

        UserToken userToken = JSONObject.parseObject(decodeStr, UserToken.class);
        if (userToken == null) {
            return JSONRespHelper.error(JSONRespHelper.FAIL, "parse accessToken to UserToken error");
        }

        Map<String, Object> data = buildRespData(userToken);
        return JSONRespHelper.success(data);
    }

    private Map<String, Object> buildRespData(UserToken userToken) {
        long userId = userToken.getUserId();
        long loginTM = userToken.getLoginTM();
        String loginDeviceId = userToken.getLoginDeviceId();

        Map<String, Object> gameUser = com.google.api.client.util.Maps.newHashMap();
        gameUser.put("serverId", String.valueOf(userToken.getServerId()));
        gameUser.put("serverIMGroupId", String.valueOf(userToken.getServerIMGroupId()));
        gameUser.put("userId", String.valueOf(userId));
        gameUser.put("loginTM", loginTM);
        // 提供给内网服务使用，其 accountId 对应的是 Game 服的 accountKey，而不是给 GooglePlay 或 GameCenter 的 accountId
        gameUser.put("accountId", userToken.getAccountKey());
        gameUser.put("loginDeviceId", loginDeviceId);

        GuildUser guildUser = guildUserDao.getByUserId(userId);
        if (guildUser != null) {
            Long guildId = guildUser.getGuildId();
            if (guildId != null && guildId > 0) {
                Guild guild = guildDao.getByGuildId(guildId);
                if (guild != null && StringUtils.isNotEmpty(guild.getImGroupId())) {
                    gameUser.put("guildIMGroupId", guild.getImGroupId());
                }
            }
        }

        Map<String, Object> data = com.google.api.client.util.Maps.newHashMap();
        data.put("gameUser", gameUser);
        return data;
    }


    @Override
    public int checkVersion() {
        if (RequestContext.getUserId() == null) {
            return ErrorCode.SUCCESS;
        }
        User user = this.getUser(RequestContext.getUserId());
        if (user == null) {
            return ErrorCode.SUCCESS;
        }

        CommonParams commonParams = RequestContext.getCommonParams();
        int version = user.getBattleVersion() == null ? 0 : user.getBattleVersion();

        if (commonParams.getBattleVersion() < version) {
            return ErrorCode.BATTLE_VERSION_TOO_LOW;
        } else if (commonParams.getBattleVersion() > version) {
            user.setBattleVersion(commonParams.getBattleVersion());
            userDao.update(user);
        }

        return ErrorCode.SUCCESS;
    }

    @Override
    @DynamoDBTransactional
    public Result<UserHabbyMailBindResponse> UserHabbyMailBindRequest(UserHabbyMailBindRequest msg) {
        UserHabbyMailBindResponse.Builder resp = UserHabbyMailBindResponse.newBuilder();
        String accountKey = RequestContext.getUser().getAccountKey();
        Account accountModel = accountDao.getItem(accountKey);
        if (accountModel.getBindStatus() != null && accountModel.getBindStatus()) {
            return Result.Error(ErrorCode.USER_BIND_REPEAT);
        }
        HabbyAuthResult authResult = this.authCode(msg.getBindParamsMapMap());
        if (authResult.getCode() != 0) {
            log.error("habbyAuthError userId:{} resp:{} param:{}", RequestContext.getUserId(), authResult, msg.getBindParamsMapMap());
            return Result.Error(authResult.getCode());
        }
        HabbyBindResult bindResult = this.bind(accountKey, authResult, msg.getBindParamsMapMap());
        if (bindResult.getCode() != 0) {
            log.error("habbyBindError userId:{} resp:{} param:{}", RequestContext.getUserId(), bindResult, msg.getBindParamsMapMap());
            return Result.Error(bindResult.getCode());
        }
        accountModel.setBindStatus(true);
        accountModel.setHabbyEmail(authResult.getData().getHabbyId());
        accountDao.updateBindInfo(accountModel);
        resp.setHabbyId(accountModel.getHabbyEmail());
        return Result.Success(resp.build());
    }

    /**
     * 校验AuthCode
     */
    private HabbyAuthResult authCode(Map<String, String> bindParams) {
        Map<String, String> header = new HashMap<>();
        header.put("Content-Type", "application/json");
        JSONObject postJson = new JSONObject();
        postJson.put("authCode", bindParams.get("authCode"));
        String response = OkHttpClientUtil.postJson(gameConfigManager.getHabbyAuthServerApiUrl(), header, postJson);
        return JSONObject.parseObject(response, HabbyAuthResult.class);
    }

    /**
     * 绑定海比
     */
    private HabbyBindResult bind(String accountPK, HabbyAuthResult authResult, Map<String, String> bindParams) {
        String gameId = gameConfigManager.getHabbyBindServerGameId();
        Map<String, String> header = new HashMap<>();
        header.put("Content-Type", "application/json");
        header.put("Authorization", "Bearer " + authResult.getData().getAccessToken());
        header.put("App-Language", bindParams.get("language"));
        JSONObject postJson = new JSONObject();
        if (StringUtils.isEmpty(bindParams.get("tgaDeviceId")) && StringUtils.isEmpty(bindParams.get("tgaDistinctId")) && (gameConfigManager.isDev() || gameConfigManager.isOB() || gameConfigManager.isDevelop())) {
            postJson.put("tgaDeviceId", generateDeviceID());
            postJson.put("tgaDistinctId", generateDistinguishID());
        } else {
            postJson.put("tgaDeviceId", bindParams.get("tgaDeviceId"));
            postJson.put("tgaDistinctId", bindParams.get("tgaDistinctId"));
        }
        postJson.put("gameId", gameId);
        postJson.put("gameAccountId", accountPK);
        String response = OkHttpClientUtil.postJson(gameConfigManager.getHabbyBindServerApiUrl(), header, postJson);
        return JSONObject.parseObject(response, HabbyBindResult.class);
    }

    /**
     * 生成设备id
     */
    private String generateDeviceID() {
        UUID uuid = UUID.randomUUID(); // 随机生成UUID
        long leastSigBits = uuid.getLeastSignificantBits();
        long mostSigBits = uuid.getMostSignificantBits();

        ByteBuffer buffer = ByteBuffer.allocate(Long.BYTES * 2);
        buffer.putLong(leastSigBits);
        buffer.putLong(mostSigBits);

        CRC32 crc32 = new CRC32();
        crc32.update(buffer.array());

        return String.format("TGA-DEV-%08X", crc32.getValue());
    }

    /**
     * 生成区分ID
     */
    private String generateDistinguishID() {
        long timestamp = System.currentTimeMillis(); // 当前时间戳
        int random = (int) (Math.random() * 10000);  // 生成一个4位随机数

        return String.format("TGA-DIST-%d-%04d", timestamp, random);
    }

    @Override
    @DynamoDBTransactional
    public Result<UserHabbyMailRewardResponse> UserHabbyMailRewardRequest(UserHabbyMailRewardRequest msg) {
      UserHabbyMailRewardResponse.Builder resp = UserHabbyMailRewardResponse.newBuilder();
        String accountKey = RequestContext.getUser().getAccountKey();
        Account accountModel = accountDao.getItem(accountKey);
        if (accountModel.getBindStatus() == null || !accountModel.getBindStatus()) {
            return Result.Error(ErrorCode.USER_BIND_REWARD_NOT_ALLOW);
        }
        if (accountModel.getBindReward() != null && accountModel.getBindReward()) {
            return Result.Error(ErrorCode.USER_BIND_REWARD_REPEAT);
        }
        String rewardsStr = gameConfigManager.getGameConfigConfig().getConfigEntity(911).getValue();
        RewardResultSet rewardResultSet = rewardService.executeRewards(RequestContext.getUserId(),rewardService.stringToRewardList(rewardsStr));
        accountModel.setBindReward(true);
        accountDao.updateBindInfo(accountModel);
        CommonProto.CommonData commonData = CommonHelper.buildCommonData(rewardResultSet);
        resp.setCommonData(commonData);
        return Result.Success(resp.build());
    }

    @Override
    @DynamoDBTransactional
    public int habbyBindStatusReset(String accountPK, String habbyAccountId) {
        Account accountModel = accountDao.getItem(accountPK);
        if (accountModel == null) {
            return ErrorCode.HABBY_BIND_NO_ACCOUNT;
        }
        if (accountModel.getBindStatus() == null || !accountModel.getBindStatus()) {
            return ErrorCode.HABBY_BIND_NOT_BIND;
        }
        if (accountModel.getHabbyEmail() == null || !accountModel.getHabbyEmail().equals(habbyAccountId)) {
            return ErrorCode.HABBY_BIND_NOT_MATCH;
        }
        accountModel.setBindStatus(false);
        accountModel.setHabbyEmail("dis-" + accountModel.getBindStatus());
        accountDao.updateBindInfo(accountModel);
        return ErrorCode.SUCCESS;
    }

}













