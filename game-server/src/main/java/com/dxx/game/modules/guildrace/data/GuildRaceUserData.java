package com.dxx.game.modules.guildrace.data;

import com.amazonaws.xray.spring.aop.XRayEnabled;
import com.alibaba.fastjson.JSON;

/**
 * <AUTHOR>
 * @date 2023/8/8 14:36
 */
@XRayEnabled
public class GuildRaceUserData {
    private int seq;
    private long userId;
    private long guildId;
    private String guildName;
    private String nickName;
    private int avatar;
    private int avatarFrame;
    private int serverId;
    private int score;
    private int power;
    private int ap;
    private int level;
    private boolean mirror;

    private String battleUni;

    public String serialize() {
        return JSON.toJSONString(this);
    }

    public static GuildRaceUserData deserialize(String data) {
        if(data == null) {
            return new GuildRaceUserData();
        }

        return JSON.parseObject(data, GuildRaceUserData.class);
    }

    public int getSeq() {
        return seq;
    }

    public void setSeq(int seq) {
        this.seq = seq;
    }

    public long getUserId() {
        return userId;
    }

    public void setUserId(long userId) {
        this.userId = userId;
    }

    public long getGuildId() {
        return guildId;
    }

    public void setGuildId(long guildId) {
        this.guildId = guildId;
    }

    public String getNickName() {
        return nickName;
    }

    public void setNickName(String nickName) {
        this.nickName = nickName;
    }

    public int getAvatar() {
        return avatar;
    }

    public void setAvatar(int avatar) {
        this.avatar = avatar;
    }

    public int getAvatarFrame() {
        return avatarFrame;
    }

    public void setAvatarFrame(int avatarFrame) {
        this.avatarFrame = avatarFrame;
    }

    public int getServerId() {
        return serverId;
    }

    public void setServerId(int serverId) {
        this.serverId = serverId;
    }

    public int getScore() {
        return score;
    }

    public void setScore(int score) {
        this.score = score;
    }

    public int getPower() {
        return power;
    }

    public void setPower(int power) {
        this.power = power;
    }

    public int getAp() {
        return ap;
    }

    public void setAp(int ap) {
        this.ap = ap;
    }

    public int getLevel() {
        return level;
    }

    public void setLevel(int level) {
        this.level = level;
    }

    public boolean isMirror() {
        return mirror;
    }

    public void setMirror(boolean mirror) {
        this.mirror = mirror;
    }

    public String getGuildName() {
        return guildName;
    }

    public void setGuildName(String guildName) {
        this.guildName = guildName;
    }

    public String getBattleUni() {
        return battleUni;
    }

    public void setBattleUni(String battleUni) {
        this.battleUni = battleUni;
    }
}
