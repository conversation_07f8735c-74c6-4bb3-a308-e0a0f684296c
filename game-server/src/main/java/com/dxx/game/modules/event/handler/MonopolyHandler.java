package com.dxx.game.modules.event.handler;

import com.dxx.game.common.server.annotation.ApiHandler;
import com.dxx.game.common.server.annotation.ApiMethod;
import com.dxx.game.common.server.model.Result;
import com.dxx.game.consts.MsgReqCommand;
import com.dxx.game.dto.MonopolyProto;
import com.dxx.game.modules.event.service.MonopolyService;
import com.google.protobuf.Message;
import org.springframework.beans.factory.annotation.Autowired;

@ApiHandler
public class MonopolyHandler {
    @Autowired
    private MonopolyService monopolyService;

    @ApiMethod(command = MsgReqCommand.MonopolyOnOpenRequest, name = "大富翁活动-打开界面调用")
    public Result<MonopolyProto.MonopolyOnOpenResponse> onOpen(Message msg) {
        MonopolyProto.MonopolyOnOpenRequest params = (MonopolyProto.MonopolyOnOpenRequest)msg;
        return monopolyService.onOpen(params);
    }

    @ApiMethod(command = MsgReqCommand.MonopolyRollDiceRequest, name = "大富翁活动-摇骰子")
    public Result<MonopolyProto.MonopolyRollDiceResponse> roll(Message msg) {
        MonopolyProto.MonopolyRollDiceRequest params = (MonopolyProto.MonopolyRollDiceRequest)msg;
        return monopolyService.rollAction(params);
    }

    @ApiMethod(command = MsgReqCommand.MonopolyBuyDiceRequest, name = "大富翁活动-购买骰子")
    public Result<MonopolyProto.MonopolyBuyDiceResponse> buyDice(Message msg) {
        MonopolyProto.MonopolyBuyDiceRequest params = (MonopolyProto.MonopolyBuyDiceRequest)msg;
        return monopolyService.buyDice(params);
    }
}
