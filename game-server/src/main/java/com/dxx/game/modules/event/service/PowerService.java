package com.dxx.game.modules.event.service;

import com.dxx.game.common.server.model.Result;
import com.dxx.game.dao.dynamodb.model.Event;
import com.dxx.game.dao.dynamodb.model.User;
import com.dxx.game.dao.dynamodb.model.event.Power;
import com.dxx.game.dto.PowerProto;

public interface PowerService {
    Result<PowerProto.PowerOnOpenResponse> onOpen(PowerProto.PowerOnOpenRequest params);

    void syncPower(User user, long maxPower);

    PowerProto.PowerDto buildDto(Power model);

    Power getModel(Event event);

    Result<PowerProto.PowerRewardResponse> reward(PowerProto.PowerRewardRequest params);
}
