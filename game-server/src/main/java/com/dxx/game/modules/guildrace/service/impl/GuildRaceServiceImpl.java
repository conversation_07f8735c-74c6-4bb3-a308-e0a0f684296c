package com.dxx.game.modules.guildrace.service.impl;

import com.amazonaws.xray.spring.aop.XRayEnabled;
import com.alibaba.fastjson.JSONArray;
import com.dxx.game.common.aws.dynamodb.transaction.annotation.DynamoDBTransactional;
import com.dxx.game.common.mq.MqTestService;
import com.dxx.game.common.redis.RedisService;
import com.dxx.game.common.server.context.RequestContext;
import com.dxx.game.common.server.model.Result;
import com.dxx.game.common.utils.DateUtils;
import com.dxx.game.config.GameConfigManager;
import com.dxx.game.config.entity.guildrace.LevelEntity;
import com.dxx.game.config.entity.guildrace.OpentimeEntity;
import com.dxx.game.consts.ErrorCode;
import com.dxx.game.consts.GameConstant;
import com.dxx.game.consts.MqParams;
import com.dxx.game.dao.dynamodb.model.User;
import com.dxx.game.dao.dynamodb.model.UserExtend;
import com.dxx.game.dao.dynamodb.model.guild.Guild;
import com.dxx.game.dao.dynamodb.model.guild.GuildUser;
import com.dxx.game.dao.dynamodb.repository.UserDao;
import com.dxx.game.dao.dynamodb.repository.UserExtendDao;
import com.dxx.game.dao.dynamodb.repository.guild.GuildDao;
import com.dxx.game.dao.dynamodb.repository.guild.GuildUserDao;
import com.dxx.game.dao.redis.BattleUnitCacheRedisDao;
import com.dxx.game.dto.CommonProto;
import com.dxx.game.dto.GuildRaceProto;
import com.dxx.game.modules.common.GroupHelper;
import com.dxx.game.modules.common.service.CommonService;
import com.dxx.game.modules.common.support.CommonHelper;
import com.dxx.game.modules.equip.service.EquipService;
import com.dxx.game.modules.guild.support.GuildSupport;
import com.dxx.game.modules.guildrace.data.GuildRaceGuildData;
import com.dxx.game.modules.guildrace.data.GuildRaceRecordData;
import com.dxx.game.modules.guildrace.data.GuildRaceUserData;
import com.dxx.game.modules.guildrace.service.GuildRaceService;
import com.dxx.game.modules.guildrace.support.GuildRaceConst;
import com.dxx.game.modules.guildrace.support.GuildRaceSupport;
import com.dxx.game.modules.guildrace.support.MqGuildRaceService;
import com.dxx.game.modules.hero.service.HeroService;
import com.dxx.game.modules.mail.MailService;
import com.dxx.game.modules.pvp.support.BattleUnitCache;
import com.dxx.game.modules.pvp.support.PVPHelper;
import com.dxx.game.modules.server.service.ServerListService;
import com.dxx.game.modules.user.service.UserService;
import com.dxx.game.modules.work.support.WorkSupport;
import com.google.protobuf.util.JsonFormat;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.DefaultTypedTuple;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.stream.Collectors;

/**
 *
 * <AUTHOR>
 * @date 2023/8/7 19:23
 */
@Slf4j
@Service
@EnableScheduling
@XRayEnabled
public class GuildRaceServiceImpl implements GuildRaceService, GuildRaceConst {
    @Autowired
    private GuildRaceSupport guildRaceSupport;
    @Autowired
    private RedisService redisService;
    @Autowired
    private GuildDao guildDao;
    @Autowired
    private GuildUserDao guildUserDao;
    @Autowired
    private GameConfigManager gameConfigManager;
    @Autowired
    private UserExtendDao userExtendDao;
    @Autowired
    private EquipService equipService;
    @Autowired
    private HeroService heroService;
    @Autowired
    private PVPHelper pvpHelper;
    @Autowired
    private MailService mailService;
    @Autowired
    private CommonService commonService;
    @Autowired
    private UserService userService;
    @Autowired
    private UserDao userDao;
    @Autowired
    private WorkSupport workSupport;
    @Autowired
    private BattleUnitCacheRedisDao battleRedisDao;
    @Autowired
    private MqGuildRaceService mqGuildRaceService;
    @Autowired
    private MqTestService mqTestService;
    @Autowired
    private GuildSupport guildSupport;
    @Autowired
    private BattleUnitCache formationCache;
    @Autowired
    private GroupHelper groupHelper;
    @Autowired
    private ServerListService serverListService;

//    @PostConstruct
//    public void onStart() {
//        onScheduled();
//    }

    public boolean checkStage(long now, int type, int season, int stage) {
        String key = guildRaceSupport.getStageFlagKey(type, season, stage);
        boolean flag = redisService.setIfAbsent(key, now);
        if(flag) {
            log.info("guild race enter, type={}, season:{}, stage={}, time={}", type, season, stage, now);
        }
        return flag;
    }

    // TODO 换成k8s定时
    @Override
    public void onScheduled() {
        long currentTime = DateUtils.getUnixTime();

        gameConfigManager.getGuildRaceConfig().getOpentime().keySet().forEach(t -> {
            int season = guildRaceSupport.getSeason(t);
            int stage = guildRaceSupport.getStage(t);
            log.info("guild race ticker exec: type={}, season={}, stage={}, currentTime={}",t, season, stage, currentTime);
        });

        gameConfigManager.getGuildRaceConfig().getOpentime().forEach((k, v) -> {
            int season = 1;

            String stageKey = guildRaceSupport.getStageKey(k);

            // 开始公会战活动
            long beginTime = DateUtils.parseToTimestamp(v.getOpenTime()) / 1000;

            for(int i = 0; i < Integer.MAX_VALUE; i++) {
                // 公会报名阶段结束
                long guildApplyEndTime = beginTime + v.getStage1() * 60L;
                // 公会分组阶段结束
                long guildGroupEndTime = guildApplyEndTime + v.getStage2() * 60L;

                // 个人报名阶段结束
                long userApplyEndTime1 = guildGroupEndTime + v.getStage3() * 60L;
                // 准备开战阶段结束
                long prepareBattleEndTime1 = userApplyEndTime1 + v.getStage4() * 60L;
                // 战斗阶段结束
                long battleEndTime1 = prepareBattleEndTime1 + v.getStage5() * 60L;
                // 战斗结束冷却阶段结束
                long battleShowTime1 = battleEndTime1 + v.getStage6() * 60L;

                long userApplyEndTime2 = battleShowTime1 + v.getStage7() * 60L;
                long prepareBattleEndTime2 = userApplyEndTime2 + v.getStage8() * 60L;
                long battleEndTime2 = prepareBattleEndTime2 + v.getStage9() * 60L;
                long battleShowTime2 = battleEndTime2 + v.getStage10() * 60L;

                long userApplyEndTime3 = battleShowTime2 + v.getStage11() * 60L;
                long prepareBattleEndTime3 = userApplyEndTime3 + v.getStage12() * 60L;
                long battleEndTime3 = prepareBattleEndTime3 + v.getStage13() * 60L;
                long battleShowTime3 = battleEndTime3 + v.getStage14() * 60L;

                if(DateUtils.isBetween(currentTime, beginTime, guildApplyEndTime)) {
                    if(!checkStage(currentTime, k, season, STAGE_GUILD_APPLY)) {
                        break;
                    }

                    int lastSeason = season - 1;
                    if(lastSeason > 0) {
                        onStageSeasonEnd(k, lastSeason);
                    }

                    redisService.set(stageKey, STAGE_GUILD_APPLY);
                    break;
                }
                if(DateUtils.isBetween(currentTime, guildApplyEndTime, guildGroupEndTime)) {
                    if(!checkStage(currentTime, k, season, STAGE_GUILD_GROUP)) {
                        break;
                    }

                    onStageGuildGroup(k);
                    redisService.set(stageKey, STAGE_GUILD_GROUP);
                    break;
                }

                if(DateUtils.isBetween(currentTime, guildGroupEndTime, userApplyEndTime1)) {
                    if(!checkStage(currentTime, k, season, STAGE_USER_APPLY1)) {
                        break;
                    }

                    redisService.set(stageKey, STAGE_USER_APPLY1);
                    break;
                }
                if(DateUtils.isBetween(currentTime, userApplyEndTime1, prepareBattleEndTime1)) {
                    if(!checkStage(currentTime, k, season, STAGE_PREPARE_BATTLE1)) {
                        break;
                    }

                    // 提前一个阶段比赛
                    onStageBattleStart(k, season, STAGE_BATTLE_ING1, STAGE_USER_APPLY1);

                    redisService.set(stageKey, STAGE_PREPARE_BATTLE1);
                    break;
                }
                if(DateUtils.isBetween(currentTime, prepareBattleEndTime1, battleEndTime1)) {
                    if(!checkStage(currentTime, k, season, STAGE_BATTLE_ING1)) {
                        break;
                    }

                    redisService.set(stageKey, STAGE_BATTLE_ING1);
                    break;
                }
                if(DateUtils.isBetween(currentTime, battleEndTime1, battleShowTime1)) {
                    if(!checkStage(currentTime, k, season, STAGE_BATTLE_END1)) {
                        break;
                    }

                    redisService.set(stageKey, STAGE_BATTLE_END1);
                    break;
                }

                if(DateUtils.isBetween(currentTime, battleShowTime1, userApplyEndTime2)) {
                    if(!checkStage(currentTime, k, season, STAGE_USER_APPLY2)) {
                        break;
                    }

                    redisService.set(stageKey, STAGE_USER_APPLY2);
                    break;
                }
                if(DateUtils.isBetween(currentTime, userApplyEndTime2, prepareBattleEndTime2)) {
                    if(!checkStage(currentTime, k, season, STAGE_PREPARE_BATTLE2)) {
                        break;
                    }

                    // 提前一个阶段比赛
                    onStageBattleStart(k, season, STAGE_BATTLE_ING2, STAGE_USER_APPLY2);

                    redisService.set(stageKey, STAGE_PREPARE_BATTLE2);
                    break;
                }
                if(DateUtils.isBetween(currentTime, prepareBattleEndTime2, battleEndTime2)) {
                    if(!checkStage(currentTime, k, season, STAGE_BATTLE_ING2)) {
                        break;
                    }

                    redisService.set(stageKey, STAGE_BATTLE_ING2);
                    break;
                }
                if(DateUtils.isBetween(currentTime, battleEndTime2, battleShowTime2)) {
                    if(!checkStage(currentTime, k, season, STAGE_BATTLE_END2)) {
                        break;
                    }

                    redisService.set(stageKey, STAGE_BATTLE_END2);
                    break;
                }

                if(DateUtils.isBetween(currentTime, battleShowTime2, userApplyEndTime3)) {
                    if(!checkStage(currentTime, k, season, STAGE_USER_APPLY3)) {
                        break;
                    }

                    redisService.set(stageKey, STAGE_USER_APPLY3);
                    break;
                }
                if(DateUtils.isBetween(currentTime, userApplyEndTime3, prepareBattleEndTime3)) {
                    if(!checkStage(currentTime, k, season, STAGE_PREPARE_BATTLE3)) {
                        break;
                    }

                    // 提前一个阶段比赛
                    onStageBattleStart(k, season, STAGE_BATTLE_ING3, STAGE_USER_APPLY3);

                    redisService.set(stageKey, STAGE_PREPARE_BATTLE3);
                    break;
                }
                if(DateUtils.isBetween(currentTime, prepareBattleEndTime3, battleEndTime3)) {
                    if(!checkStage(currentTime, k, season, STAGE_BATTLE_ING3)) {
                        break;
                    }

                    redisService.set(stageKey, STAGE_BATTLE_ING3);
                    break;
                }
                if(DateUtils.isBetween(currentTime, battleEndTime3, battleShowTime3)) {
                    if(!checkStage(currentTime, k, season, STAGE_BATTLE_END3)) {
                        break;
                    }

                    redisService.set(stageKey, STAGE_BATTLE_END3);
                    break;
                }

                if(currentTime >= battleShowTime3) {
                    season = season + 1;
                    beginTime = battleShowTime3;
                }
                else {
                    break;
                }
            }

            redisService.set(guildRaceSupport.getSeasonKey(k), season);
        });
    }

    @Override
    public void check(long userId) {
        UserExtend userExtend = userExtendDao.getByUserId(userId);
        Map<Integer, List<Integer>> rewardRecord = userExtend.getGuildRaceRewardRecord();
        if(rewardRecord == null) {
            rewardRecord = new HashMap<>();
            userExtend.setGuildRaceRewardRecord(rewardRecord);
        }

        for(Integer type : gameConfigManager.getGuildRaceConfig().getOpentime().keySet()) {
            int season = guildRaceSupport.getSeason(type);

            // 截止4个赛季
            int off = Math.max(season - 4, 0);

            for(int i = season; i > off; i--) {
                List<Integer> rewardDay = rewardRecord.get(i);
                if(rewardDay == null) {
                    rewardDay = new ArrayList<>();
                    rewardRecord.put(i, rewardDay);
                }

                int stage = (i == season) ? guildRaceSupport.getStage(type) : STAGE_BATTLE_END3;
                for(int j = stage; j > 0; j--) {
                    if(j == STAGE_BATTLE_END1 || j == STAGE_BATTLE_END2 || j == STAGE_BATTLE_END3) {
                        int day = guildRaceSupport.getDay(j);
                        if(rewardDay.contains(day)) {
                            break;
                        }

                        GuildRaceUserData userData = guildRaceSupport.getApplyUserDataOfDay(type, i, day, userId);
                        if(userData == null) {
                            continue;
                        }

                        GuildRaceGuildData guildData = guildRaceSupport.getApplyGuildData(type, i, userData.getGuildId());
                        if(guildData == null || guildData.getGroup() == 0) {
                            continue;
                        }


                        GuildRaceGuildData oppGuildData = null;
                        int ownerScore = 0;
                        int oppScore = 0;
                        if(day == 1) {
                            ownerScore = guildData.getScore1();
                            oppScore = guildData.getOdScore1();
                            oppGuildData = guildRaceSupport.getApplyGuildData(type, i, guildData.getOd1());
                        } else if (day == 2) {
                            ownerScore = guildData.getScore2();
                            oppScore = guildData.getOdScore2();
                            oppGuildData = guildRaceSupport.getApplyGuildData(type, i, guildData.getOd2());
                        } else if (day == 3) {
                            ownerScore = guildData.getScore3();
                            oppScore = guildData.getOdScore3();
                            oppGuildData = guildRaceSupport.getApplyGuildData(type, i, guildData.getOd3());
                        }

                        if(oppGuildData == null) {
                            log.error("reward oppGuildData is null, guildId={}", guildData.getGuildId());
                            return;
                        }

                        LevelEntity entity = gameConfigManager.getGuildRaceConfig().getLevel().get(guildData.getDan());

                        // 发奖
                        if(ownerScore > oppScore) {
                            List<List<Integer>> reward = new ArrayList<>();
                            reward.addAll(entity.getRewards());
                            reward.addAll(entity.getWinRewards());

                            sendWinMail(userData, guildData, oppGuildData, reward);
                        }
                        else {
                            sendNormalMail(userData, guildData, oppGuildData, entity.getRewards());
                        }

                        rewardDay.add(day);
                    }
                }
            }
        }

        userExtendDao.updateGuildRaceReward(userExtend);
    }

    private void sendNormalMail(GuildRaceUserData data, GuildRaceGuildData guild, GuildRaceGuildData oppGuildData, List<List<Integer>> reward) {
        String tid = gameConfigManager.getMailTempId(7514);

        Map<String, String> params = new HashMap<>();
        params.put("ownerGuildName", guild.getGuildName());
        params.put("ownerGuildServer", String.valueOf(guild.getServerId()));
        params.put("oppGuildName", oppGuildData.getGuildName());
        params.put("oppGuildServer", String.valueOf(oppGuildData.getServerId()));

        mailService.createMail(data.getUserId(), tid, params, reward);
    }

    private void sendWinMail(GuildRaceUserData data, GuildRaceGuildData guild, GuildRaceGuildData oppGuildData, List<List<Integer>> reward) {
        String tid = gameConfigManager.getMailTempId(7512);

        Map<String, String> params = new HashMap<>();
        params.put("ownerGuildName", guild.getGuildName());
        params.put("ownerGuildServer", String.valueOf(guild.getServerId()));
        params.put("oppGuildName", oppGuildData.getGuildName());
        params.put("oppGuildServer", String.valueOf(oppGuildData.getServerId()));

        mailService.createMail(data.getUserId(), tid, params, reward);
    }

    @Override
    public void onStageGuildGroup(int type) {
        int season = guildRaceSupport.getSeason(type);

        guildRaceSupport.removeAllGroupList(type, season);

        int groupNum = 10;
        int group = 1;

        for(LevelEntity entity : gameConfigManager.getGuildRaceConfig().getLevel().values()) {
            int dan = entity.getLevel();

            for (Integer gp : guildRaceSupport.getGroupIndex(type, season, dan)) {
                String key = guildRaceSupport.getApplyGuildKey(type, season, dan, gp);

                Set<ZSetOperations.TypedTuple<String>> list = redisService.zReverseRangeWithScores(key, 0, Integer.MAX_VALUE);
                if(list == null || list.isEmpty()) {
                    continue;
                }

                int team = 1;
                Map<Integer, List<ZSetOperations.TypedTuple<String>>> teamList = new HashMap<>();
                // TODO 按照细节规则分组
                for (ZSetOperations.TypedTuple<String> stringTypedTuple : list) {
                    teamList.computeIfAbsent(team, k -> new ArrayList<>());
                    teamList.get(team).add(stringTypedTuple);

                    if (teamList.get(team).size() == groupNum) {
                        team = team + 1;
                    }
                }

                for(Map.Entry<Integer, List<ZSetOperations.TypedTuple<String>>> entry : teamList.entrySet()) {
                    int k = entry.getKey();
                    List<ZSetOperations.TypedTuple<String>> v = entry.getValue();

                    if(v.size() < groupNum) {
                        if(k > 1) {
                            // 镜像
                            for(ZSetOperations.TypedTuple<String> vv : teamList.get(k - 1)) {
                                if(teamList.get(k).size() < groupNum) {
                                    long mirrorId = guildRaceSupport.getMirrorGuildId(Long.parseLong(vv.getValue() == null ? "0" : vv.getValue()));
                                    String score = 0 + "." + mirrorId;
                                    ZSetOperations.TypedTuple<String> zt = new DefaultTypedTuple<>(String.valueOf(mirrorId), Double.parseDouble(score));
                                    teamList.get(k).add(zt);
                                }
                                else {
                                    break;
                                }
                            }
                        }
                        else {
                            // 未入围
                            teamList.remove(k);
                        }
                    }
                }

                for(List<ZSetOperations.TypedTuple<String>> v : teamList.values()) {
                    String glKey = guildRaceSupport.getGroupListKey(type, season, group);
                    redisService.zBatchAdd(glKey, new HashSet<>(v));
                    guildRaceSupport.expireKey(glKey);

                    // TODO 优化 不查
                    Set<String> zset = redisService.zReverseRange(glKey, 0, Integer.MAX_VALUE);
                    List<String> gidList = new ArrayList<>(zset);
                    // 公会 组和排名 对手反向索引
                    for(String s : gidList) {
                        long guildId = s == null ? 0L : Long.parseLong(s);
                        int rank = redisService.zRank(glKey, s).intValue() + 1;

                        String opp1 = gidList.get(guildRaceSupport.getOppRank(1, rank) - 1);
                        String opp2 = gidList.get(guildRaceSupport.getOppRank(2, rank) - 1);
                        String opp3 = gidList.get(guildRaceSupport.getOppRank(3, rank) - 1);
                        long oppGuildId1 = opp1 == null ? 0L : Long.parseLong(opp1);
                        long oppGuildId2 = opp2 == null ? 0L : Long.parseLong(opp2);
                        long oppGuildId3 = opp3 == null ? 0L : Long.parseLong(opp3);

                        GuildRaceGuildData data;

                        String kk = guildRaceSupport.getGuildInfoKey(type, season, guildId);
                        String str = redisService.get(kk);
                        if (str == null || str.isEmpty()) {
                            // 模拟镜像公会报名
                            data = guildRaceSupport.getMirrorData(type, season, guildId);
                            data.setGuildId(guildId);
                        } else {
                            data = GuildRaceGuildData.deserialize(str);
                        }

                        data.setGroup(group);
                        data.setRank(rank);
                        data.setOd1(oppGuildId1);
                        data.setOd2(oppGuildId2);
                        data.setOd3(oppGuildId3);

                        redisService.set(kk, data.serialize());
                        guildRaceSupport.expireKey(kk);
                    }

                    group = group + 1;
                }
            }
        }
    }

    @Override
    public void onStageBattleStart(int type, int season, int currentStage, int applyStage) {
        for(int i = 1; i < Integer.MAX_VALUE; i++) {
            if(!guildRaceSupport.checkGroupExists(type, season, i)) {
                break;
            }

            // mq分发每个组比赛任务
            Map<String, String> params = new HashMap<>();
            params.put(MqParams.GUILD_RACE_TYPE, String.valueOf(type));
            params.put(MqParams.GUILD_RACE_SEASON, String.valueOf(season));
            params.put(MqParams.GUILD_RACE_GROUP, String.valueOf(i));
            params.put(MqParams.GUILD_RACE_CURRENT_STAGE, String.valueOf(currentStage));
            params.put(MqParams.GUILD_RACE_APPLY_STAGE, String.valueOf(applyStage));
            redisService.rmqAdd(mqGuildRaceService.getStream(), params);
        }
    }

    private void onStageSeasonEnd(int type, int season) {
        List<GuildRaceGuildData> first3List = new ArrayList<>();
        List<GuildRaceGuildData> last3List = new ArrayList<>();

        for(int i = 1; i < Integer.MAX_VALUE - 1; i++) {
            Map<Long, Pair<Integer, GuildRaceGuildData>> list = guildRaceSupport.getGroupList(type, season, i);
            if(list == null) {
                break;
            }

            int r = 0;
            for(Map.Entry<Long, Pair<Integer, GuildRaceGuildData>> entry : list.entrySet()) {
                GuildRaceGuildData data = entry.getValue().getRight();

                int dan = guildRaceSupport.getGuildDan(data.getGuildId());
                LevelEntity levelEntity = gameConfigManager.getGuildRaceConfig().getLevelEntity(dan);

                if(r < levelEntity.getUpNum()) {
                    first3List.add(data);
                }
                else if(r > levelEntity.getDownNum()) {
                    last3List.add(data);
                }

                r++;
            }
        }

        // 段位升降
        first3List.forEach(v -> {
            guildRaceSupport.upDan(v.getGuildId());
        });
        last3List.forEach(v -> {
            guildRaceSupport.downDan(v.getGuildId());
        });

        // 保留4赛季数据 20250227通过redis释放
//        int delSeason = season - 4;
//        if(delSeason <= 0) {
//            return;
//        }
//        redisService.deleteKeysByPrefix(guildRaceSupport.getSeasonLikeKey(type, delSeason));
    }

    @DynamoDBTransactional
    @Override
    public Result<GuildRaceProto.GuildRaceGuildApplyResponse> guildApply(GuildRaceProto.GuildRaceGuildApplyRequest params) {
        // TODO 只有会长副会长报名

        User user = RequestContext.getUser();

        long guildId = params.getGuildId();
        int type = params.getType();

        if(!guildRaceSupport.isGuildApplyStage(type)) {
            return Result.Error(ErrorCode.GUILD_RACE_STAGE_ERROR);
        }

        int code = guildApply(guildId, type);
        if(code != 0) {
            return Result.Error(code);
        }

        GuildRaceProto.GuildRaceGuildApplyResponse.Builder response = GuildRaceProto.GuildRaceGuildApplyResponse.newBuilder();
        response.setGuildId(params.getGuildId());
        return Result.Success(response.build());
    }

    @Override
    public int guildApply(long guildId, int type) {
        OpentimeEntity opentimeEntity = gameConfigManager.getGuildRaceConfig().getOpentime().get(type);
        if(opentimeEntity == null) {
            return ErrorCode.PARAMS_ERROR;
        }

        Guild guild = guildDao.getByGuildId(guildId);
        if(guild == null) {
            return ErrorCode.GUILD_NOT_EXIST;
        }

        if(guild.getGuildMembersCount() < 5) {
            return ErrorCode.GUILD_RACE_GUILD_MEMBER_COUNT;
        }

        int guildStatus = guildSupport.guildStatus(guild);
        if (guildStatus != ErrorCode.SUCCESS) {
            return guildStatus;
        }

        int season = guildRaceSupport.getSeason(type);
        int dan = guildRaceSupport.getGuildDan(guildId);

        if(guildRaceSupport.isGuildApply(type, season, guildId)) {
            return ErrorCode.GUILD_RACE_GUILD_APPLY_REPEAT;
        }

        int serverId = guild.getServerId();
        if(serverId < 1) {
            log.error("guild serverId is 0, userId={}, guildId={}, guildSrv={}", RequestContext.getUserId(), guildId, serverId);
            return ErrorCode.GUILD_NOT_EXIST;
        }

        // group
        int interval = gameConfigManager.getGuildRaceConfig().getLevelEntity(dan).getServerNum();
        int serverZone = serverListService.getZoneMark(serverId);

        int group = groupHelper.grouping(serverId, interval);
        group = (serverZone * 1000000) + group;

        String key = guildRaceSupport.getApplyGuildKey(type, season, dan, group);

        // 分组
        String number = 0 + "." + guildId;
        redisService.zAdd(key, String.valueOf(guildId), Double.parseDouble(number));
        guildRaceSupport.expireKey(key);

        GuildRaceGuildData guildRaceGuildData = new GuildRaceGuildData();
        guildRaceGuildData.setGuildId(guildId);
        guildRaceGuildData.setGuildName(guild.getGuildName());
        guildRaceGuildData.setAvatar(guild.getGuildIcon());
        guildRaceGuildData.setAvatarFrame(guild.getGuildIconBg());
        guildRaceGuildData.setServerId(serverId);
        guildRaceGuildData.setDan(dan);

        List<GuildUser> guildUsers = guildUserDao.getAllByGuildId(guildId);
        List<Long> userIds = guildUsers.stream().map(GuildUser::getUserId).collect(Collectors.toList());
        Map<Long, User> users = userDao.getByUserIds(userIds);
        long totalPower = users.values().stream().mapToLong(User::getPower).sum();

        guildRaceGuildData.setPower(totalPower);
        // 未分组之前都是0
        guildRaceGuildData.setRank(0);

        // 存一份公会详细信息
        String gikey = guildRaceSupport.getGuildInfoKey(type, season, guildId);
        redisService.set(gikey, guildRaceGuildData.serialize());
        guildRaceSupport.expireKey(gikey);

        // 存一份公会组索引
        guildRaceSupport.addGroupIndex(type, season, dan, group);

        return 0;
    }

    @DynamoDBTransactional
    @Override
    public Result<GuildRaceProto.GuildRaceUserApplyResponse> userApply(GuildRaceProto.GuildRaceUserApplyRequest params) {
        long userId = RequestContext.getUserId();

        GuildUser guildUser = guildUserDao.getByUserId(userId);
        if(guildUser == null) {
            return Result.Error(ErrorCode.PARAMS_ERROR);
        }

        long guildId = guildUser.getGuildId();
        int type = guildRaceSupport.getGuildType(guildId);

        // 未到报名阶段
        if(!guildRaceSupport.isUserApplyStage(type)) {
            return Result.Error(ErrorCode.GUILD_RACE_STAGE_ERROR);
        }

        int season = guildRaceSupport.getSeason(type);
        int stage = guildRaceSupport.getStage(type);

        int code = userApply(userId, season, stage);
        if(code != ErrorCode.SUCCESS) {
            return Result.Error(code);
        }

        GuildRaceProto.GuildRaceUserApplyResponse.Builder response = GuildRaceProto.GuildRaceUserApplyResponse.newBuilder();
        return Result.Success(response.build());
    }

    @Override
    public int userApply(long userId, int season, int stage) {
        User user = userService.getUser(userId);

        GuildUser guildUser = guildUserDao.getByUserId(userId);
        if(guildUser == null) {
            return ErrorCode.PARAMS_ERROR;
        }

        long guildId = guildUser.getGuildId();
        int type = guildRaceSupport.getGuildType(guildId);

        GuildRaceGuildData raceGuildData = guildRaceSupport.getApplyGuildData(type, season, guildId);
        // 工会未报名
        if(raceGuildData == null) {
            return ErrorCode.GUILD_RACE_GUILD_NOT_APPLY;
        }

        // 工会未分组
        if(raceGuildData.getGroup() == 0) {
            return ErrorCode.GUILD_RACE_GUILD_NOT_GROUP;
        }

        // 退出公会 加入其他公会报名
        if(guildRaceSupport.isUserApply(type, season, stage, userId)) {
            return ErrorCode.GUILD_RACE_GUILD_USER_REPEAT;
        }

        String key = guildRaceSupport.getApplyUserKey(type, season, stage);
        String userKey = String.valueOf(userId);

        if(redisService.hGet(key, userKey) != null) {
            return ErrorCode.GUILD_RACE_GUILD_APPLY_REPEAT;
        }

//        UserExtend userExtend = userExtendDao.getByUserId(userId);

        GuildRaceUserData data = new GuildRaceUserData();
        data.setSeq(0);
        data.setUserId(userId);
        data.setGuildId(guildId);
        data.setGuildName(raceGuildData.getGuildName());
        data.setAvatar(user.getAvatar() == null ? 0 : user.getAvatar());
        data.setAvatarFrame(user.getAvatarFrame() == null ? 0 : user.getAvatarFrame());
        data.setNickName(user.getNickName());
        data.setServerId(user.getServerId());
        data.setLevel(user.getLevel());
        data.setPower(user.getPower().intValue());

        // TODO 看下前一天比赛的积分情况
        data.setScore(0);

        // 读表 前一天体力使用情况
        if(stage == STAGE_USER_APPLY1) {
            data.setAp(commonService.getGameConfigIntValue(7101));
        }
        else {
            GuildRaceUserData lastUserData = guildRaceSupport.getLastApplyUserData(type, season, stage, userId).getRight();
            if(lastUserData != null) {
                int costAp = guildRaceSupport.getSeqConsumeAp(guildRaceSupport.getGuildDan(guildId), lastUserData.getSeq());
                data.setAp(lastUserData.getAp() - costAp);
            }
            else {
                data.setAp(commonService.getGameConfigIntValue(7101));
            }
        }

        // 报名固定战斗信息
//        CommonProto.BattleUnitDto uni = battleRedisDao.getData(userId);
        CommonProto.BattleUnitDto uni = formationCache.getBattleUnit(userId, GameConstant.FORMATION_TYPE_GUILD_RACE);
        if(uni == null) {
            log.error("guildRace BattleUnitDto is null: userId={}", user);
            return ErrorCode.GUILD_RACE_USER_NOT_EXIST;
        }

        try {
            data.setBattleUni(CommonHelper.convertDto2Json(uni));
        } catch (Exception e) {
            log.error("userApply e:", e);
            log.error("parse battle unit json error: {}", e.getMessage());
        }

        // 尝试加入比赛位
        guildRaceSupport.try2BattleList(data, type, season, stage, guildId);

        // 存一份成员详细信息
        redisService.hSet(key, userKey, data.serialize());
        guildRaceSupport.expireKey(key);

        // 添加到工会玩家报名列表
        guildRaceSupport.guildApplyUser(type, season, stage, guildId, userId);

        // 有镜像工会同步
        if(guildRaceSupport.haveMirrorGuild(type, season, guildId)) {
            long mirrorUserId = guildRaceSupport.getMirrorUserId(userId);
            long mirrorGuildId = guildRaceSupport.getMirrorGuildId(guildId);

            data.setMirror(true);
            data.setUserId(mirrorUserId);
            data.setGuildId(mirrorGuildId);
            data.setSeq(0);

            guildRaceSupport.try2BattleList(data, type, season, stage, mirrorGuildId);
            redisService.hSet(key, String.valueOf(mirrorUserId), data.serialize());

            // 添加到镜像工会玩家报名列表
            guildRaceSupport.guildApplyUser(type, season, stage, mirrorGuildId, mirrorUserId);
        }

        return ErrorCode.SUCCESS;
    }

    @DynamoDBTransactional
    @Override
    public Result<GuildRaceProto.GuildRaceEditSeqResponse> editSeq(GuildRaceProto.GuildRaceEditSeqRequest params) {
        // TODO 会长副会长操作

        long userId= RequestContext.getUserId();
        GuildUser guildUser = guildUserDao.getByUserId(userId);
        if(guildUser == null) {
            return Result.Error(ErrorCode.PARAMS_ERROR);
        }

        long guildId = guildUser.getGuildId();

        int type = guildRaceSupport.getGuildType(guildId);

        int season = guildRaceSupport.getSeason(type);
        int stage = guildRaceSupport.getStage(type);

        if(stage != STAGE_USER_APPLY1 && stage != STAGE_USER_APPLY2 && stage != STAGE_USER_APPLY3) {
            return Result.Error(ErrorCode.PARAMS_ERROR);
        }

        int applyStage = guildRaceSupport.getDayByStage(type);

        String battleListKey = guildRaceSupport.getBattleListKey(type, season, applyStage, guildId);
        Map<Integer, Long> battleList = guildRaceSupport.getBattleList(type, season, applyStage, guildId);

        int opSeq = params.getOpSeq();

        long opUserId = battleList.getOrDefault(opSeq, 0L);
        long targetUserId = params.getTargetUserId();

        String key = guildRaceSupport.getApplyUserKey(type, season, applyStage);

        GuildRaceUserData targetUserData = guildRaceSupport.getApplyUserData(type, season, applyStage, targetUserId);
        if(targetUserData == null) {
            return Result.Error(ErrorCode.GUILD_RACE_USER_NOT_EXIST);
        }

        // 出战位更改
//        String opSeq = redisService.hGet(battleListKey, String.valueOf(opUserId));
//        if(opSeq == null) {
//            return Result.Error(ErrorCode.GUILD_RACE_USER_NOT_BATTLE);
//        }

        int dan = guildRaceSupport.getGuildDan(guildId);

        int opAp = guildRaceSupport.getSeqConsumeAp(dan, opSeq);
        int targetAp = guildRaceSupport.getSeqConsumeAp(dan, targetUserData.getSeq());

        if(targetUserData.getAp() < opAp) {
            return Result.Error(ErrorCode.GUILD_RACE_AP_NOT_ENOUGH);
        }

        String targetSeq = redisService.hGet(battleListKey, String.valueOf(targetUserId));
        GuildRaceUserData opUserData = guildRaceSupport.getApplyUserData(type, season, applyStage, opUserId);

        if(opUserData == null) {
            if(targetSeq != null) {
                redisService.hDel(battleListKey, String.valueOf(targetUserId));
                guildRaceSupport.hSetWithExpire(battleListKey, String.valueOf(targetUserId), String.valueOf(opSeq));
            }
            else {
                guildRaceSupport.hSetWithExpire(battleListKey, String.valueOf(targetUserId), String.valueOf(opSeq));
            }
        }
        else {
            if(targetSeq != null) {
                if(opUserData.getAp() < targetAp) {
                    redisService.hDel(battleListKey, String.valueOf(opUserData.getUserId()));
                    guildRaceSupport.hSetWithExpire(battleListKey, String.valueOf(targetUserId), String.valueOf(opSeq));

                    opUserData.setSeq(0);
                }
                else {
                    guildRaceSupport.hSetWithExpire(battleListKey, String.valueOf(targetUserId), String.valueOf(opSeq));
                    guildRaceSupport.hSetWithExpire(battleListKey, String.valueOf(opUserData.getUserId()), targetSeq);

                    opUserData.setSeq(Integer.parseInt(targetSeq));
                }
            }
            else {
                redisService.hDel(battleListKey, String.valueOf(opUserData.getUserId()));
                guildRaceSupport.hSetWithExpire(battleListKey, String.valueOf(targetUserId), String.valueOf(opSeq));

                opUserData.setSeq(0);
            }

            redisService.hSet(key, String.valueOf(opUserId), opUserData.serialize());
        }

        targetUserData.setSeq(opSeq);

        redisService.hSet(key, String.valueOf(targetUserId), targetUserData.serialize());

        GuildRaceProto.GuildRaceEditSeqResponse.Builder response = GuildRaceProto.GuildRaceEditSeqResponse.newBuilder();
        response.setOpUserId(opUserId);
        response.setTargetUserId(targetUserId);
        return Result.Success(response.build());
    }

    @DynamoDBTransactional
    @Override
    public Result<GuildRaceProto.GuildRaceInfoResponse> info(GuildRaceProto.GuildRaceInfoRequest params) {
        long userId= RequestContext.getUserId();

        int type;
        int season, stage;

        check(userId);

        GuildRaceProto.GuildRaceInfoResponse.Builder response = GuildRaceProto.GuildRaceInfoResponse.newBuilder();

        GuildUser guildUser = guildUserDao.getByUserId(userId);
        if(guildUser != null && guildUser.getGuildId() > 0) {
            long guildId = guildUser.getGuildId();
            type = guildRaceSupport.getGuildType(guildId);
            season = guildRaceSupport.getSeason(type);
            stage = guildRaceSupport.getStage(type);

            response.setLastDon(guildRaceSupport.getGuildDan(guildId));

            GuildRaceGuildData data = guildRaceSupport.getApplyGuildData(type, season, guildId);
            if(data != null) {
                GuildRaceProto.RaceGuildDto.Builder ownerDto = GuildRaceProto.RaceGuildDto.newBuilder();
                ownerDto.setGuildId(data.getGuildId());
                ownerDto.setGuildName(data.getGuildName());
                ownerDto.setAvatar(data.getAvatar());
                ownerDto.setAvatarFrame(data.getAvatarFrame());
                ownerDto.setDan(guildRaceSupport.getGuildDan(data.getGuildId()));
                ownerDto.setPower(data.getPower());
                ownerDto.setScore(data.getScore());
                ownerDto.setOppGuildId(guildRaceSupport.getOppId(guildRaceSupport.getDay(stage), data));

                response.setRaceGuild(ownerDto);

                Map<Long, Pair<Integer, GuildRaceGuildData>> list = guildRaceSupport.getGroupList(type, season, data.getGroup());
                if(list != null) {
                    list.forEach((k, v) -> {
                        GuildRaceProto.RaceGuildDto.Builder guildDto = GuildRaceProto.RaceGuildDto.newBuilder();

                        int score = v.getLeft();
                        GuildRaceGuildData d = v.getRight();

                        guildDto.setGuildId(d.getGuildId());
                        guildDto.setGuildName(d.getGuildName());
                        guildDto.setAvatar(d.getAvatar());
                        guildDto.setAvatarFrame(d.getAvatarFrame());
                        guildDto.setDan(guildRaceSupport.getGuildDan(d.getGuildId()));
                        guildDto.setPower(d.getPower());
                        guildDto.setScore(score);
                        guildDto.setOppGuildId(guildRaceSupport.getOppId(guildRaceSupport.getDay(stage), d));

                        response.addGroupRaceGuilds(guildDto);
                    });
                }
            }
        }
        else {
            type = 1;
            season = guildRaceSupport.getSeason(type);
            stage = guildRaceSupport.getStage(type);
        }

        response.setType(type);
        response.setStage(stage);
        response.setInitTime(guildRaceSupport.getTypeInitTime(type));

        response.setUserApply(guildRaceSupport.isUserApply(type, season, guildRaceSupport.getDayByStage(type), userId));

        return Result.Success(response.build());
    }

    @DynamoDBTransactional
    @Override
    public Result<GuildRaceProto.GuildRaceOwnerUserApplyListResponse> ownerUserApplyList(GuildRaceProto.GuildRaceOwnerUserApplyListRequest params) {
        long userId= RequestContext.getUserId();

        GuildUser guildUser = guildUserDao.getByUserId(userId);
        if(guildUser == null) {
            return Result.Error(ErrorCode.PARAMS_ERROR);
        }

        long guildId = guildUser.getGuildId();
        int type = guildRaceSupport.getGuildType(guildId);
        int season = guildRaceSupport.getSeason(type);

        if(!guildRaceSupport.isGuildApply(type, season, guildId)) {
            return Result.Error(ErrorCode.GUILD_RACE_GUILD_NOT_APPLY);
        }

        List<GuildRaceProto.RaceUserDto> dtoList = guildRaceSupport.getGuildUserApplyInfo(type, season, guildId);
        if(dtoList == null) {
            return Result.Error(ErrorCode.GUILD_RACE_STAGE_ERROR);
        }

        GuildRaceProto.GuildRaceOwnerUserApplyListResponse.Builder response = GuildRaceProto.GuildRaceOwnerUserApplyListResponse.newBuilder();
        response.addAllList(dtoList);

        return Result.Success(response.build());
    }

    @DynamoDBTransactional
    @Override
    public Result<GuildRaceProto.GuildRaceOppUserApplyListResponse> oppUserApplyList(GuildRaceProto.GuildRaceOppUserApplyListRequest params) {
        long userId= RequestContext.getUserId();
        GuildUser guildUser = guildUserDao.getByUserId(userId);
        if(guildUser == null) {
            return Result.Error(ErrorCode.PARAMS_ERROR);
        }

        long guildId = guildUser.getGuildId();
        int type = guildRaceSupport.getGuildType(guildId);
        int season = guildRaceSupport.getSeason(type);
        int stage = guildRaceSupport.getStage(type);

        if(!guildRaceSupport.isGuildApply(type, season, guildId)) {
            return Result.Error(ErrorCode.GUILD_RACE_GUILD_NOT_APPLY);
        }

        long oppGuildId = guildRaceSupport.getOppGuildIdByStage(type, season, stage, guildId);
        List<GuildRaceProto.RaceUserDto> dtoList = guildRaceSupport.getGuildUserApplyInfo(type, season, oppGuildId);
        if(dtoList == null) {
            return Result.Error(ErrorCode.GUILD_RACE_STAGE_ERROR);
        }

        GuildRaceProto.GuildRaceOppUserApplyListResponse.Builder response = GuildRaceProto.GuildRaceOppUserApplyListResponse.newBuilder();
        response.addAllList(dtoList);
        return Result.Success(response.build());
    }

    @DynamoDBTransactional
    @Override
    public Result<GuildRaceProto.GuildRaceUserInfoResponse> userInfo(GuildRaceProto.GuildRaceUserInfoRequest params) {
        long userId = params.getUserId();

        GuildUser guildUser = guildUserDao.getByUserId(userId);
        if(guildUser == null) {
            return Result.Error(ErrorCode.PARAMS_ERROR);
        }

        long guildId = guildUser.getGuildId();
        int type = guildRaceSupport.getGuildType(guildId);
        int season = guildRaceSupport.getSeason(type);
        int day = guildRaceSupport.getDayByStage(type);
        if(day == 0) {
            return Result.Error(ErrorCode.GUILD_RACE_STAGE_ERROR);
        }

        String key = guildRaceSupport.getApplyUserKey(type, season, day);
        String data = redisService.hGet(key, String.valueOf(userId));
        if(data == null) {
            return Result.Error(ErrorCode.GUILD_RACE_USER_NOT_EXIST);
        }

        GuildRaceUserData guildRaceUserData = GuildRaceUserData.deserialize(data);

        CommonProto.UserInfoDto.Builder userInfoDto = CommonProto.UserInfoDto.newBuilder();
        userInfoDto.setNickName(guildRaceUserData.getNickName() == null ? "" : guildRaceUserData.getNickName());
        userInfoDto.setAvatar(guildRaceUserData.getAvatar());
        userInfoDto.setAvatarFrame(guildRaceUserData.getAvatarFrame());
        userInfoDto.setLevel(guildRaceUserData.getLevel());

        // TODO 装备....

        GuildRaceProto.GuildRaceUserInfoResponse.Builder response = GuildRaceProto.GuildRaceUserInfoResponse.newBuilder();
        response.setUserId(params.getUserId());
        response.setUserInfoDto(userInfoDto);

        return Result.Success(response.build());
    }

    @DynamoDBTransactional
    @Override
    public Result<GuildRaceProto.GuildRaceGuildRecordResponse> record(GuildRaceProto.GuildRaceGuildRecordRequest params) {
        long userId = RequestContext.getUserId();

        GuildUser guildUser = guildUserDao.getByUserId(userId);
        if(guildUser == null) {
            return Result.Error(ErrorCode.PARAMS_ERROR);
        }

        int day = params.getDay();
        int applyStage;
        if(day == 1) {
            day = STAGE_BATTLE_ING1;
            applyStage = STAGE_USER_APPLY1;
        }
        else if(day == 2) {
            day = STAGE_BATTLE_ING2;
            applyStage = STAGE_USER_APPLY2;
        }
        else if(day == 3) {
            day = STAGE_BATTLE_ING3;
            applyStage = STAGE_USER_APPLY3;
        }
        else {
            return Result.Error(ErrorCode.PARAMS_ERROR);
        }

        long guildId = guildUser.getGuildId();

        int type = guildRaceSupport.getGuildType(guildId);
        int season = guildRaceSupport.getSeason(type);

        String s = redisService.get(guildRaceSupport.getGuildInfoKey(type, season, guildId));
        if(s == null) {
            return Result.Error(ErrorCode.GUILD_RACE_GUILD_NOT_APPLY);
        }

        GuildRaceGuildData guildRaceGuildData = GuildRaceGuildData.deserialize(s);
        int group = guildRaceGuildData.getGroup();

        String key = guildRaceSupport.getRecordKey(type, season, day, group);
        String dataList = redisService.get(key);

        GuildRaceProto.GuildRaceGuildRecordResponse.Builder response = GuildRaceProto.GuildRaceGuildRecordResponse.newBuilder();

        if(dataList == null) {
            return Result.Success(response.build());
        }

        List<String> guildKeys = new ArrayList<>();
        List<Object> userKeys = new ArrayList<>();

        Set<String> guildIds = redisService.zReverseRange(guildRaceSupport.getGroupListKey(type, season, group), 0, Integer.MAX_VALUE);
        guildIds.forEach((v) -> {
            long gid = Long.parseLong(v);
            guildKeys.add(guildRaceSupport.getGuildInfoKey(type, season, gid));
            userKeys.addAll(redisService.hGetAll(guildRaceSupport.getBattleListKey(type, season, applyStage, gid)).keySet());
        });
        List<GuildRaceGuildData> guildDataList = new ArrayList<>();
        redisService.getAll(guildKeys).forEach(v -> {
            guildDataList.add(GuildRaceGuildData.deserialize(v));
        });

        List<GuildRaceUserData> userDataList = new ArrayList<>();
        redisService.hmGetAll(guildRaceSupport.getApplyUserKey(type, season, applyStage), userKeys).forEach(v -> {
            userDataList.add(GuildRaceUserData.deserialize(String.valueOf(v)));
        });

        List<GuildRaceRecordData> recordList = new ArrayList<>();
        JSONArray.parseArray(dataList).forEach(v -> {
            recordList.add(GuildRaceRecordData.deserialize(v.toString()));
        });

        recordList.forEach(v -> {
            GuildRaceProto.UserPVPRecordDto.Builder userPVPRecordDto = GuildRaceProto.UserPVPRecordDto.newBuilder();

            GuildRaceUserData d1 = userDataList.stream().filter(vv -> vv.getUserId() == v.getUserId1()).findFirst().orElse(null);
            GuildRaceUserData d2 = userDataList.stream().filter(vv -> vv.getUserId() == v.getUserId2()).findFirst().orElse(null);

            userPVPRecordDto.setUserId1(v.getUserId1());
            userPVPRecordDto.setUserId2(v.getUserId2());

            if(d1 != null && d1.isMirror()) {
                userPVPRecordDto.setUserId1(v.getUserId1() - 600000000L);
            }
            if(d2 != null && d2.isMirror()) {
                userPVPRecordDto.setUserId2(v.getUserId2() - 600000000L);
            }

            userPVPRecordDto.setResult(v.getResult());
            userPVPRecordDto.setRecordRowId(v.getRecordRowId());

            response.addRecords(userPVPRecordDto);
        });

        guildDataList.forEach(v -> {
            int dan = guildRaceSupport.getGuildDan(v.getGuildId());

            long oppId = guildRaceSupport.getOppId(params.getDay(), v);
            response.addGuilds(v.buildRaceGuildDto(dan, oppId));
        });
        response.addAllUsers(userDataList.stream().map(CommonHelper::buildRaceUserDto).collect(Collectors.toList()));

        return Result.Success(response.build());
    }
}
