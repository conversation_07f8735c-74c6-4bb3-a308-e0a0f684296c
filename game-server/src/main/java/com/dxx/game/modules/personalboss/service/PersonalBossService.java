package com.dxx.game.modules.personalboss.service;

import com.dxx.game.common.server.model.Result;
import com.dxx.game.dto.PersonalBossProto;

public interface PersonalBossService {

    Result<PersonalBossProto.PersonalBossInfoResponse> getInfo(PersonalBossProto.PersonalBossInfoRequest params);

    Result<PersonalBossProto.PersonalBossBattleResponse> battle(PersonalBossProto.PersonalBossBattleRequest params);

    Result<PersonalBossProto.PersonalBossRankResponse> rank(PersonalBossProto.PersonalBossRankRequest params);
}
