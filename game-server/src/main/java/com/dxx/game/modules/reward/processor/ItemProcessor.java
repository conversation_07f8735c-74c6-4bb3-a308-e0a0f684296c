package com.dxx.game.modules.reward.processor;

import com.amazonaws.xray.spring.aop.XRayEnabled;
import com.dxx.game.common.aws.dynamodb.utils.DynamoDBConvertUtil;
import com.dxx.game.common.server.context.RequestContext;
import com.dxx.game.config.GameConfigManager;
import com.dxx.game.config.entity.item.ItemEntity;
import com.dxx.game.consts.ErrorCode;
import com.dxx.game.consts.RewardResourceType;
import com.dxx.game.consts.RewardType;
import com.dxx.game.dao.dynamodb.model.Item;
import com.dxx.game.dao.dynamodb.repository.ItemDao;
import com.dxx.game.modules.event.service.FishingService;
import com.dxx.game.modules.event.service.FlipService;
import com.dxx.game.modules.event.service.MonopolyService;
import com.dxx.game.modules.item.service.ItemService;
import com.dxx.game.modules.reward.action.RewardAction;
import com.dxx.game.modules.reward.model.ItemReward;
import com.dxx.game.modules.reward.model.Reward;
import com.dxx.game.modules.reward.result.RewardResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import software.amazon.awssdk.enhanced.dynamodb.Expression;

/**
 * 道具处理器
 * <AUTHOR>
 * @date 2019-12-17 17:28
 */
@Component
@XRayEnabled
public class ItemProcessor implements RewardProcessor {

    private static final Logger logger = LoggerFactory.getLogger(ItemProcessor.class);

    @Autowired
    private ItemDao itemDao;
    @Autowired
    private ItemService itemService;
    @Autowired
    private GameConfigManager gameConfigManager;
    @Autowired
    private FlipService flipService;
    @Autowired
    private FishingService fishingService;
    @Autowired
    private MonopolyService monopolyService;

    @Override
    public RewardType getType() {
        return RewardType.ITEM;
    }

    @Override
    public RewardResourceType getRewardResourceType() {
        return RewardResourceType.NONE;
    }

    @Override
    public RewardAction tryReward(long userId, Reward reward) {
        int resultCode = ErrorCode.SUCCESS;
        int addCount = reward.getCount();
        if (addCount < 0) {
            Item item = itemDao.getByItemId(userId, reward.getConfigId());
            if (item == null || item.getCount() < Math.abs(addCount)) {
                int itemCount = 0;
                if (item != null) {
                    itemCount = item.getCount();
                }
                logger.error("item count not enough, userId:{}，itemId:{}，realCount:{},needCount;{}",
                        userId, reward.getConfigId(), itemCount, addCount);
                resultCode = ErrorCode.ITEM_IS_NOT_ENOUGH;
            }
        } else {
            // 配置ID是否存在
            ItemEntity itemEntity = gameConfigManager.getItemConfig().getItemEntity(reward.getConfigId());
            if (itemEntity == null) {
                logger.error("item config id not exist:{}", reward.getConfigId());
                resultCode = ErrorCode.CONFIG_NOT_EXIST;
            }
        }
        return simpleRewardAction(reward, resultCode);
    }

    @Override
    public RewardResult<?> executeReward(long userId, RewardAction rewardAction) {
        if (rewardAction.isFailed()) {
            return null;
        }

        ItemReward reward = (ItemReward) rewardAction.getReward();
        Item item = itemDao.getByItemId(userId, reward.getConfigId());

        if (reward.getCount() < 0) {
            if (item == null) {
                logger.error("item is null, userId : {}, configId : {}", RequestContext.getUserId(), reward.getConfigId());
                return null;
            }
            if (item.getCount() < Math.abs(reward.getCount())) {
                logger.error("item count error, userId : {}, equipCount: {}, addCount : {}", userId, item.getCount(), reward.getCount());
                return null;
            }

            Expression expression = Expression.builder().expression("#count = :itemCount")
                    .putExpressionName("#count", "count")
                    .putExpressionValue(":itemCount", DynamoDBConvertUtil.buildAttributeValue(item.getCount()))
                    .build();
            item.addUpdateCondition(expression);

            item.setCount(item.getCount() - Math.abs(reward.getCount()));
            itemDao.update(item);
        } else {
            if (item == null) {
                // 新增道具
                item = itemService.createItem(userId, reward.getConfigId(), reward.getCount());
                if (item == null) {
                    // 插入数据失败
                    logger.error("item : create failed. userId: {}", userId);
                    return null;
                }
            } else {
                Expression expression = Expression.builder().expression("#count = :itemCount")
                        .putExpressionName("#count", "count")
                        .putExpressionValue(":itemCount", DynamoDBConvertUtil.buildAttributeValue(item.getCount()))
                        .build();
                item.addUpdateCondition(expression);

                int count = item.getCount() + reward.getCount();
                item.setCount(count);
                itemDao.update(item);
            }

            hardcode(userId, reward);
        }


        RewardResult<Item> result = new RewardResult<Item>(this.getType());
        result.setActualCount(reward.getCount());
        result.setCurrent(item);

        return result;
    }

    private void hardcode(long userId, ItemReward reward) {
        int itemId = reward.getConfigId();
        int count = reward.getCount();

        // 钓鱼鱼竿
        if (gameConfigManager.getEventFishingConfig().getFishRod().containsKey(itemId)) {
            fishingService.onAddRod(userId, itemId);
        }

        // 翻牌子线索
        if (gameConfigManager.getFlipClueItemId().contains(itemId)) {
            flipService.onAddClue(userId, itemId, count, false);
        }

        // 儿童节大富翁星星
        if (gameConfigManager.getMonopolyBaseStarItemId().contains(itemId)) {
            monopolyService.onAddStars(userId, count, false);
        }
    }

}
















