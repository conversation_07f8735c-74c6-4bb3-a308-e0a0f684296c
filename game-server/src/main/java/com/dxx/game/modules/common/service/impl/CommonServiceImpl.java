package com.dxx.game.modules.common.service.impl;

import com.amazonaws.xray.spring.aop.XRayEnabled;
import com.dxx.game.common.utils.IpUtils;
import com.dxx.game.config.GameConfigManager;
import com.dxx.game.consts.RedisKeys;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.dxx.game.common.redis.RedisService;
import com.dxx.game.modules.common.service.CommonService;

import javax.annotation.PostConstruct;
import java.math.BigInteger;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@XRayEnabled
public class CommonServiceImpl implements CommonService {

	private static final Logger logger = LoggerFactory.getLogger(CommonServiceImpl.class);

	@Autowired
	private GameConfigManager gameConfigManager;

	@Autowired
	RedisService redisService;

	@PostConstruct
	private void init() {
		String userId = redisService.get(RedisKeys.USER_ID_KEY);
		if (userId == null) {
			if (gameConfigManager.isTest()) {
				// 测试服从9开始
				redisService.set(RedisKeys.USER_ID_KEY, 91000000L);
			} else if (gameConfigManager.isDev()) {
				redisService.set(RedisKeys.USER_ID_KEY, 23000000L);
			} else if (gameConfigManager.isOB()) {
				redisService.set(RedisKeys.USER_ID_KEY, 60000000L);
			} else {
				redisService.set(RedisKeys.USER_ID_KEY, 76000000L);
			}
		}

	}

	@Override
	public boolean setGenerateIdStarter(String key, long value) {
		return redisService.setIfAbsent(key, value);
	}

	@Override
	public long generateId(String key) {
		return redisService.incrBy(key);
	}

	@Override
	public long generateId(long userId) {
		return redisService.incrBy(RedisKeys.GENERAL_INCR_ID + this.calcTableIndex(userId));
	}

	@Override
	public long generateUserId() {
		return redisService.incrBy(RedisKeys.USER_ID_KEY);
	}

	@Override
	public long generateUserTransId(long userId) {
		long value = redisService.incrBy(RedisKeys.USER_TRANSID + userId);
		if (value == 1) {
			value = redisService.incrBy(RedisKeys.USER_TRANSID + userId, 100000L);
		}
		return value;
	}

	@Override
	public String getUserCountry(String ip) {
		BigInteger longIp = IpUtils.ipToBigInt(ip);
		String key = RedisKeys.IPV4_COUNTRY;
		if (IpUtils.isIPV6(ip)) {
			key = RedisKeys.IPV6_COUNTRY;
		}

		String value = redisService.queryIp(key, longIp.toString());
		if (value == null) {
			return "-";
		}
		return value.split("_")[1];
	}

	/**
	 * 解析分表ID
	 * @param userId
	 * @return
	 */
	private long calcTableIndex(long userId) {
		return userId % 1000;
	}

	@Override
	public int getGameConfigIntValue(int id) {
		return Integer.parseInt(this.getGameConfigValue(id));
	}

	@Override
	public String getGameConfigValue(int id) {
		return gameConfigManager.getGameConfigConfig().getConfigEntity(id).getValue();
	}

	@Override
	public List<Integer> getGameConfigValue(int id, String split) {
		var value = getGameConfigValue(id);
		String[] val = value.split(split);
		List<Integer> vals = new ArrayList<>();
		for(String str : val) {
			vals.add(Integer.parseInt(str));
		}
		return vals;
	}

	@Override
	public List<List<Integer>> getGameConfigValueSplitOneArr(int id, String split) {
		String[] val = gameConfigManager.getGameConfigConfig().getConfigEntity(id).getValue().split("\\|");
		List<List<Integer>> vals = new ArrayList<>();

		for(String str : val) {
			String[] arr = str.split(",");
			List<Integer> ss = new ArrayList<>();
			for(String strr : arr) {
				ss.add(Integer.parseInt(strr));
			}

			vals.add(ss);
		}

		return vals;
	}

	@Override
	public Map<Integer, Integer> getGameConfigValueSplitOneMap(int id, String split) {
		String[] val = gameConfigManager.getGameConfigConfig().getConfigEntity(id).getValue().split("\\|");
		Map<Integer, Integer> vals = new HashMap<>();
		for(String str : val) {
			String[] arr = str.split(",");
			vals.put(Integer.parseInt(arr[0]), Integer.parseInt(arr[1]));
		}

		return vals;
	}
}













