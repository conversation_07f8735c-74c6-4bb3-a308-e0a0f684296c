package com.dxx.game.modules.im.dto;

import com.amazonaws.xray.spring.aop.XRayEnabled;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/8/26 21:36
 */

@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
@XRayEnabled
public class GetUserOnlineStatusResponse {
    //{
    //    "code": 0,
    //    "message": "Success",
    //    "data": {
    //        "userStatuses": {
    //            "4247571": 1712571131,
    //            "5692937": 1710827415
    //        }
    //    }
    //}

    private int code;
    private String message;
    private Data data;

    @Builder
    @lombok.Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Data {
        private Map<String, Long> userStatuses;
    }
}

