package com.dxx.game.modules.im;

import com.amazonaws.xray.spring.aop.XRayEnabled;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dxx.game.common.JSONRespHelper;
import com.dxx.game.common.aws.dynamodb.transaction.annotation.DynamoDBTransactional;
import com.dxx.game.common.utils.DateUtils;
import com.dxx.game.config.GameConfigManager;
import com.dxx.game.consts.ErrorCode;
import com.dxx.game.dao.dynamodb.model.User;
import com.dxx.game.dao.dynamodb.model.UserExtend;
import com.dxx.game.dao.dynamodb.model.gameplay.UserChapter;
import com.dxx.game.dao.dynamodb.model.guild.GuildUser;
import com.dxx.game.dao.dynamodb.repository.UserDao;
import com.dxx.game.dao.dynamodb.repository.UserExtendDao;
import com.dxx.game.dao.dynamodb.repository.gameplay.UserChapterDao;
import com.dxx.game.dao.dynamodb.repository.guild.GuildUserDao;
import com.dxx.game.modules.chat.service.ChatService;
import com.dxx.game.modules.common.service.SensitiveWordsService;
import com.dxx.game.modules.im.dto.*;
import com.dxx.game.modules.message.service.MessageService;
import com.dxx.game.modules.user.service.UserService;
import com.google.common.collect.Lists;
import io.netty.handler.codec.http.FullHttpRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.*;

import com.dxx.game.modules.message.consts.MessageType;

import static com.dxx.game.consts.FunctionTypeEnum.FUNCTION_CHAT;
import static com.dxx.game.modules.im.IMGroupIdGenerator.getChatGroupOrServerOrGuildId;
import static com.dxx.game.modules.im.IMGroupIdGenerator.getGroupType;


@Slf4j
@Service
@XRayEnabled
public class IMCallbackService {

    @Resource
    private GuildUserDao guildUserDao;
    @Resource
    private SensitiveWordsService sensitiveWordsService;
    @Resource
    private GameConfigManager gameConfigManager;
    @Resource
    private UserService userService;
    @Resource
    private MessageService messageService;
    @Resource
    private ChatService chatService;
    @Resource
    private UserExtendDao userExtendDao;
    @Resource
    private UserChapterDao userChapterDao;
    @Autowired
    private UserDao userDao;

    @DynamoDBTransactional
    public CallbackResponse callback(JSONObject params, FullHttpRequest fullHttpRequest) {
        String host = fullHttpRequest.headers().get("Host");
        if (!host.startsWith("internal-")){
            return null;
        }

        if (params == null) {
            return buildFailResponse("params is null", JSONRespHelper.FAIL);
        }

        CallbackRequest request = params.toJavaObject(CallbackRequest.class);
        String callbackCommand = request.getCallbackCommand();

        if ("Group.CallbackBeforeSendMsg".equals(callbackCommand)) {
            return callbackBeforeSendMsg(params);
        }

        if ("Private.CallbackBeforeSendMsg".equals(callbackCommand)) {
            return callbackPrivateChat(params);
        }

        if ("Group.CallbackBeforeJoin".equals(callbackCommand) ||
                "Group.CallbackBeforeQuit".equals(callbackCommand)) {
            return callbackBeforeJoinQuitGroup(params);
        }

        log.warn("IMService.callback unknown callbackCommand:{}", callbackCommand);
        return buildFailResponse("unknown callbackCommand", JSONRespHelper.FAIL);
    }

    private CallbackResponse callbackBeforeJoinQuitGroup(JSONObject params) {
        BeforeJoinQuitGroupRequest request = params.toJavaObject(BeforeJoinQuitGroupRequest.class);
        String userIdStr = request.getUserId();
        if (userIdStr == null) {
            return buildFailResponse("userId is null", JSONRespHelper.FAIL);
        }

        String groupId = request.getGroupId();
        if (groupId == null) {
            return buildFailResponse("groupId is null", JSONRespHelper.FAIL);
        }

        long userId = Long.parseLong(userIdStr);

        IMGroupIdGenerator.GroupType groupType = getGroupType(groupId);
        BeforeJoinQuitGroupResponse.Data.DataBuilder builder = BeforeJoinQuitGroupResponse.Data.builder()
                .imGroupType(groupType.getIMGroupType())
                .gameGroupType(groupType.toString());

        long chatGroupOrServerId = getChatGroupOrServerOrGuildId(groupId);
        if (groupType == IMGroupIdGenerator.GroupType.SERVER) {
            User user = userService.getUser(userId);
            if (user.getServerId() != chatGroupOrServerId) {
                return buildFailResponse("serverId not match, user.serverId:" + user.getServerId() + ", request.serverId:" + chatGroupOrServerId, JSONRespHelper.FAIL);
            }

            builder.gameServerId(String.valueOf(chatGroupOrServerId));
        } else if (groupType == IMGroupIdGenerator.GroupType.GUILD) {
            return buildFailResponse("guild not support", JSONRespHelper.FAIL);
        }

        return BeforeJoinQuitGroupResponse.builder()
                .code(ErrorCode.SUCCESS)
                .message("success")
                .data(builder
                        .build()
                )
                .build();
    }

    private CallbackResponse callbackBeforeSendMsg(JSONObject params) {
        BeforeSendMsgRequest request = params.toJavaObject(BeforeSendMsgRequest.class);
        MsgContent msgContent = request.getMsgContent();
        if (msgContent == null) {
            return buildFailResponse("msgContent is null", JSONRespHelper.FAIL);
        }

        String content = msgContent.getData();
        if (StringUtils.isEmpty(content)) {
            return buildFailResponse("content is null", ErrorCode.PARAMS_ERROR);
        }

        String msgSender = request.getMsgSender();
        if (StringUtils.isEmpty(msgSender)) {
            return buildFailResponse("msgSender is null", JSONRespHelper.FAIL);
        }

        long userId = Long.parseLong(msgSender);
        if (chatService.isGuildChatFrequencyTooFast(userId)) {
            return buildFailResponse("chat frequency too fast", ErrorCode.CHAT_FREQUENCY_TOO_FAST);
        }

        User user = userService.getUser(userId);
        if (user == null) {
            return buildFailResponse("user not found", ErrorCode.PARAMS_ERROR);
        }

        if (user.getChatBannedTM() != null && user.getChatBannedTM() > DateUtils.getUnixTime()) {
            return buildFailResponse("chat banned", ErrorCode.CHAT_BANNED);
        }

        int maxLength = gameConfigManager.getGuildConfig().getGuildConstEntity(115).getTypeInt();
        int contentLength = content.getBytes(StandardCharsets.UTF_8).length;
        if (contentLength > maxLength) {
            return buildFailResponse("content length error", ErrorCode.CHAT_CONTENT_LENGTH_ERROR);
        }

        content = sensitiveWordsService.replaceWords(content,userId);
        if (StringUtils.isEmpty(content)) {
            return buildFailResponse("content check error", ErrorCode.CHAT_CONTENT_CHECK_ERROR);
        }

        // 保存下次可聊天频率
        chatService.saveGuildChatFrequency(userId);

        String languageId = null;
        String customData = msgContent.getCustomData();
        if (!StringUtils.isEmpty(customData)) {
            JSONObject customDataJson = JSONObject.parseObject(customData);
            languageId = customDataJson.getString("languageId");
        } else {
            log.warn("IMService.review customData is null, languageId is null, request:{}", JSON.toJSONString(request));
        }

        String customType;
        String data;
        String imGroupId = request.getGroupId();
        if (IMGroupIdGenerator.getGroupType(imGroupId) == IMGroupIdGenerator.GroupType.SERVER) {
            // 是在世界服发的消息
            JSONObject jsonObject = new JSONObject(true);

            int serverId = (int) IMGroupIdGenerator.getServerOrGuildId(imGroupId);
            long msgId = messageService.generateServerMsgId(serverId);
            jsonObject.put("msgId", msgId);
            putUserInfo(jsonObject, user);
            jsonObject.put("chatContent", content);
            if (languageId != null) {
                jsonObject.put("languageId", languageId);
            }
            jsonObject.put("timestamp", DateUtils.getUnixTime());
            data = jsonObject.toJSONString();
            customType = String.valueOf(MessageType.CHAT_COMMON);
        } else {
            // 是在公会发的消息
            GuildUser guildUser = guildUserDao.getByUserId(userId);
            if (guildUser == null || guildUser.getGuildId() == 0) {
                return buildFailResponse("guildUser not found", ErrorCode.CHAT_GUILD_NOT_JOIN);
            }

            JSONObject jsonObject = new JSONObject(true);
            long msgId = messageService.generateGuildMsgId(guildUser.getGuildId());
            jsonObject.put("msgId", msgId);
            putUserInfo(jsonObject, user);
            jsonObject.put("chatContent", content);
            if (languageId != null) {
                jsonObject.put("languageId", languageId);
            }
            jsonObject.put("userPosition", guildUser.getPosition());
            jsonObject.put("timestamp", DateUtils.getUnixTime());
            data = jsonObject.toJSONString();
            customType = String.valueOf(MessageType.CHAT_GUILD);
        }

        return BeforeSendMsgResponse.builder()
                .code(JSONRespHelper.SUCCESS)
                .data(BeforeSendMsgResponse.Data.builder()
                        .msgContent(MsgContent.builder()
                                .chatText(content)
                                .data(data)
                                .customType(customType)
                                .customData(customData)
                                .build()
                        )
                        .build()
                ).build();
    }

    private void putUserInfo(JSONObject jsonObject, User user) {
        jsonObject.put("userId", user.getUserId());
        jsonObject.put("nickName", Optional.ofNullable(user.getNickName()).orElse(""));
        jsonObject.put("avatar", Optional.ofNullable(user.getAvatar()).orElse(0));
        jsonObject.put("avatarFrame", Optional.ofNullable(user.getAvatarFrame()).orElse(0));
    }

    private static CallbackResponse buildFailResponse(String errorInfo, int errorCode) {
        return CallbackResponse.builder()
                .code(errorCode)
                .message(errorInfo)
                .build();
    }

    @DynamoDBTransactional
    public CallbackResponse historyMsgCallBack(JSONObject requestJson, FullHttpRequest fullHttpRequest) {
        String host = fullHttpRequest.headers().get("Host");
        if (!host.startsWith("internal-")){
            return null;
        }

        if (requestJson == null) {
            return buildFailResponse("requestJson is null", JSONRespHelper.FAIL);
        }

        BeforeSendHistoryRequest request = requestJson.toJavaObject(BeforeSendHistoryRequest.class);
        List<Msg> msgList = request.getMsgList();
        if (CollectionUtils.isEmpty(msgList)) {
            return buildFailResponse("msgList is empty", JSONRespHelper.FAIL);
        }

        String groupId = request.getGroupId();

        List<Msg> msgListResponse = Lists.newArrayListWithCapacity(msgList.size());
        for (Msg msg : msgList) {
            MsgContent msgContent = msg.getMsgContent();
            if (msgContent == null) {
                log.warn("IMService.historyMsgCallBack msgContent is null, request:{}", requestJson);
                continue;
            }

            String data = msgContent.getData();
            if (StringUtils.isEmpty(data)) {
                log.warn("IMService.historyMsgCallBack data is null, request:{}", requestJson);
                continue;
            }

            String customType = msgContent.getCustomType();
            if (StringUtils.isEmpty(customType)) {
                log.warn("IMService.historyMsgCallBack customType is null, request:{}", requestJson);
                continue;
            }

            msgListResponse.add(Msg.builder()
                    .msgSeq(msg.getMsgSeq())
                    .msgSender(msg.getMsgSender())
                    .msgContent(MsgContent.builder()
                            .data(data)
                            .customType(customType)
                            .chatText(msgContent.getChatText())
                            .customData(msgContent.getCustomData())
                            .build()
                    )
                    .msgServerTime(msg.getMsgServerTime())
                    .build()
            );
        }

        return BeforeSendHistoryResponse.builder()
                .code(ErrorCode.SUCCESS)
                .message("success")
                .data(BeforeSendHistoryResponse.Data.builder()
                        .groupId(groupId)
                        .msgList(msgListResponse)
                        .build()
                )
                .build();
    }

    private CallbackResponse callbackPrivateChat(JSONObject params) {
        BeforeSendMsgRequest request = params.toJavaObject(BeforeSendMsgRequest.class);
        MsgContent msgContent = request.getMsgContent();
        if (msgContent == null) {
            return buildFailResponse("msgContent is null", JSONRespHelper.FAIL);
        }

        String content = msgContent.getData();
        if (StringUtils.isEmpty(content)) {
            return buildFailResponse("content is null", ErrorCode.PARAMS_ERROR);
        }

        String msgSender = request.getMsgSender();
        if (StringUtils.isEmpty(msgSender)) {
            return buildFailResponse("msgSender is null", JSONRespHelper.FAIL);
        }
        String msgTarget = request.getTargetUserId();
        if (StringUtils.isEmpty(msgTarget)) {
            return buildFailResponse("msgTarget is null", JSONRespHelper.FAIL);
        }


        long userId = Long.parseLong(msgSender);
        long targetId = Long.parseLong(msgTarget);
        if (chatService.isPrivateChatFrequencyTooFast(userId, targetId)) {
            return buildFailResponse("chat frequency too fast", ErrorCode.CHAT_FREQUENCY_TOO_FAST);
        }

        User user = userService.getUser(userId);
        if (user == null) {
            return buildFailResponse("user not found", ErrorCode.PARAMS_ERROR);
        }
        if (user.getChatBannedTM() != null && user.getChatBannedTM() > DateUtils.getUnixTime()) {
            return buildFailResponse("chat banned", ErrorCode.CHAT_BANNED);
        }

        UserExtend userExtend = userExtendDao.getByUserId(userId);
        if (userExtend.getChatBlacklist() != null && userExtend.getChatBlacklist().contains(targetId)) {
            return buildFailResponse("in black list", ErrorCode.CHAT_IN_BLACK_LIST);
        }

        if(!userExtend.getOpenModelId().contains(FUNCTION_CHAT.getFunctionKey())) {
            return buildFailResponse("chat not open", ErrorCode.CHAT_NOT_OPEN);
        }

        UserExtend targetUserExtend = userExtendDao.getByUserId(targetId);
        if (targetUserExtend == null) {
            return buildFailResponse("target user not found", ErrorCode.PARAMS_ERROR);
        }
        if (targetUserExtend.getChatBlacklist() != null && targetUserExtend.getChatBlacklist().contains(userId)) {
            return buildFailResponse("in black list", ErrorCode.CHAT_IN_BLACK_LIST);
        }

        UserChapter userChapter = userChapterDao.getByUserId(userId);
        if (userChapter.getMaxChapterId() < userService.getPrivateChatChapterLimit(targetUserExtend.getPrivateChatChapterLimit())) {
            return buildFailResponse("chapter limit", ErrorCode.CHAT_CHAPTER_LIMIT);
        }


        int maxLength = gameConfigManager.getGuildConfig().getGuildConstEntity(115).getTypeInt();
        int contentLength = content.getBytes(StandardCharsets.UTF_8).length;
        if (contentLength > maxLength) {
            return buildFailResponse("content length error", ErrorCode.CHAT_CONTENT_LENGTH_ERROR);
        }

        content = sensitiveWordsService.replaceWords(content,userId);
        if (StringUtils.isEmpty(content)) {
            return buildFailResponse("content check error", ErrorCode.CHAT_CONTENT_CHECK_ERROR);
        }

        // 保存下次可聊天频率
        chatService.savePrivateChatFrequency(userId, targetId);

        String languageId = null;
        String customData = msgContent.getCustomData();
        if (!StringUtils.isEmpty(customData)) {
            JSONObject customDataJson = JSONObject.parseObject(customData);
            languageId = customDataJson.getString("languageId");
        } else {
            log.warn("IMService.review customData is null, languageId is null, request:{}", JSON.toJSONString(request));
        }

        String customType;
        String data;

        JSONObject jsonObject = new JSONObject(true);
        jsonObject.put("msgId", request.getMsgSeq());
        putUserInfo(jsonObject, user);
        jsonObject.put("chatContent", content);
        if (languageId != null) {
            jsonObject.put("languageId", languageId);
        }
        jsonObject.put("timestamp", DateUtils.getUnixTime());
        data = jsonObject.toJSONString();
        customType = String.valueOf(MessageType.CHAT_PRIVATE);


        return BeforeSendMsgResponse.builder().code(0)
                .data(BeforeSendMsgResponse.Data.builder()
                        .msgContent(MsgContent.builder()
                                .chatText(content)
                                .data(data)
                                .customType(customType)
                                .customData(customData)
                                .build()
                        )
                        .build()
                ).build();
    }

    @DynamoDBTransactional
    public GetUserInfoResponse getUserInfo(JSONObject requestJson, FullHttpRequest fullHttpRequest) {
        String host = fullHttpRequest.headers().get("Host");
        if (!host.startsWith("internal-")){
            return null;
        }

        if (requestJson == null) {
            return GetUserInfoResponse.builder()
                    .code(JSONRespHelper.FAIL)
                    .message("requestJson is null")
                    .build();
        }

        GetUserInfoRequest request = requestJson.toJavaObject(GetUserInfoRequest.class);
        List<String> list = request.getUserIds();
        if (CollectionUtils.isEmpty(list)) {
            return GetUserInfoResponse.builder()
                    .code(JSONRespHelper.FAIL)
                    .message("msgList is empty")
                    .build();
        }

        if (list.size() > 100) {
            return GetUserInfoResponse.builder()
                    .code(JSONRespHelper.FAIL)
                    .message("msgList too long")
                    .build();
        }

        List<Long> userIds = new ArrayList<>();
        for (String userId : list) {
            if (StringUtils.isEmpty(userId)) {
                return GetUserInfoResponse.builder()
                        .code(JSONRespHelper.FAIL)
                        .message("userId is null")
                        .build();
            }
            userIds.add(Long.parseLong(userId));
        }

        var userData = userDao.getByUserIds(userIds);
        Map<String, UserData> userDateMap = new HashMap<>();
        userData.forEach((key, value) -> {
            userDateMap.put(String.valueOf(key), UserData.builder()
                    .NickName(value.getNickName() == null ? "" : value.getNickName())
                    .UserId(value.getUserId() )
                    .Avatar(value.getAvatar()== null ? 0 : value.getAvatar())
                    .AvatarFrame(value.getAvatarFrame()== null ? 0 : value.getAvatarFrame())
                    .build()
            );
        });

        return GetUserInfoResponse.builder()
                .code(ErrorCode.SUCCESS)
                .message("success")
                .data(GetUserInfoResponse.Data.builder()
                        .userDate(userDateMap)
                        .build()
                )
                .build();
    }
}
