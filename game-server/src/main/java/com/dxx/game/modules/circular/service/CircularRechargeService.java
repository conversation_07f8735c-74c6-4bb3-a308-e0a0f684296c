package com.dxx.game.modules.circular.service;

import com.dxx.game.common.server.model.Result;
import com.dxx.game.config.entity.iap.PurchaseEntity;
import com.dxx.game.dto.CircularRechargeProto.*;

import java.util.List;


public interface CircularRechargeService {
    Result<DailyRechargeResponse> doDailyRechargeAction(DailyRechargeRequest params);

    Result<TotalConsumtionResponse> doTotalConsumtionAction(TotalConsumtionRequest params);

    boolean checkRecharge(long userId, int productId,  PurchaseEntity  purchaseEntity);

    List<DailyRechargeData> getDailyRechargeData(long userId);

    TotalConsumtionData getTotalConsumptionData(long userId);
}
