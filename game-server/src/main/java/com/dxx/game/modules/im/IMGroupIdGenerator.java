package com.dxx.game.modules.im;

import com.amazonaws.xray.spring.aop.XRayEnabled;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.UUID;


@Service
@Slf4j
@XRayEnabled
public class IMGroupIdGenerator {

    public enum GroupType {
        SERVER, GUILD, SLG;

        public String getIMGroupType() {
            switch (this) {
                case SERVER:
                    return "AVChatRoom";
                case GUILD:
                    return "Public";
                case SLG:
                    return "SLG";
                default:
                    throw new IllegalArgumentException("Unknown group type: " + this);
            }
        }
    }

    public static String generateGroupId(GroupType groupType, long id) {
        String prefix;

        switch (groupType) {
            case SERVER:
                prefix = "SERVER-";
                break;
            case GUILD:
                prefix = "GUILD-";
                break;
            case SLG:
                prefix = "SLG-";
                break;
            default:
                throw new IllegalArgumentException("Unknown group type: " + groupType);
        }

        return prefix + id;
    }

    // Parse the GroupID and return its type
    public static GroupType getGroupType(String groupId) {
        if (groupId.startsWith("SERVER-")) {
            return GroupType.SERVER;
        } else if (groupId.startsWith("GUILD-")) {
            return GroupType.GUILD;
        } else if (groupId.startsWith("SLG-")) {
            return GroupType.SLG;
        } else {
            throw new IllegalArgumentException("Unknown Group ID prefix: " + groupId);
        }
    }


    // Parse the GroupID and return the server/guild ID
    public static long getServerOrGuildId(String groupId) {
        String[] parts = groupId.split("-");
        if (parts.length < 2) {
            throw new IllegalArgumentException("Invalid Group ID format: " + groupId);
        }

        try {
            return Integer.parseInt(parts[1]);
        } catch (NumberFormatException e) {
            throw new IllegalArgumentException("Invalid Server/Guild ID in Group ID: " + groupId, e);
        }
    }

    public static long getChatGroupOrServerOrGuildId(String groupId) {
        String[] parts = groupId.split("-");
        if (parts.length < 2) {
            throw new IllegalArgumentException("Invalid Group ID format: " + groupId);
        }

        try {
            return Integer.parseInt(parts[1]);
        } catch (NumberFormatException e) {
            throw new IllegalArgumentException("Invalid Server/Guild ID in Group ID: " + groupId, e);
        }
    }
}
