package com.dxx.game.modules.reward.model;

import com.amazonaws.xray.spring.aop.XRayEnabled;

import com.dxx.game.consts.RewardResourceType;
import com.dxx.game.consts.RewardType;

/**
 * 道具
 * <AUTHOR>
 * @date 2019-12-13 20:34
 */
@XRayEnabled
public class ItemReward implements Reward {

    /**
     * 奖励类型   {@link RewardType}
     */
    private RewardType type = RewardType.ITEM;

    /**
     * 奖励子类型   {@link RewardResourceType}
     */
    private RewardResourceType resourceType = RewardResourceType.NONE;

    /**
     * 数量
     */
    private int count = 0;

    /**
     * 配置表ID
     */
    private int configId = 0;

    public ItemReward() {

    }

    public ItemReward(int configId, int count) {
        this.configId = configId;
        this.count = count;
    }

    public static ItemReward valueOf(int configId, int count) {
        return new ItemReward(configId, count);
    }


    @Override
    public RewardType getType() {
        return this.type;
    }

    @Override
    public RewardResourceType getResourceType() {
        return this.resourceType;
    }

    @Override
    public int getCount() {
        return this.count;
    }

    @Override
    public Reward increase(int incrCount) {
        this.count += incrCount;
        return this;
    }

    @Override
    public int getConfigId() {
        return this.configId;
    }

    @Override
    public Reward union(Reward reward) {
        if (match(reward)) {
            this.count += reward.getCount();
        }
        return this;
    }

    @Override
    public boolean match(Reward reward) {
        if (!(reward instanceof ItemReward)) {
            return false;
        }
        return this.configId == reward.getConfigId();
    }

}














