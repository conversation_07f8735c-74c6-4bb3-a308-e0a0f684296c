package com.dxx.game.modules.pay.service.impl;

import com.amazonaws.xray.spring.aop.XRayEnabled;
import com.dxx.game.common.aws.dynamodb.transaction.DynamoDBTransactionAspectSupport;
import com.dxx.game.common.aws.dynamodb.transaction.annotation.DynamoDBTransactional;
import com.dxx.game.common.channel.AndroidCallBackService;
import com.dxx.game.common.channel.ChannelFactory;
import com.dxx.game.common.channel.ChannelService;
import com.dxx.game.common.channel.common.config.ChannelConfig;
import com.dxx.game.common.channel.common.consts.AndroidPayCbErrorCode;
import com.dxx.game.common.channel.common.model.PayCbVo;
import com.dxx.game.common.redis.RedisLock;
import com.dxx.game.common.server.context.RequestContext;
import com.dxx.game.common.server.model.Result;
import com.dxx.game.config.GameConfigManager;
import com.dxx.game.config.entity.iap.PurchaseEntity;
import com.dxx.game.consts.ErrorCode;
import com.dxx.game.consts.MsgReqCommand;
import com.dxx.game.dao.dynamodb.model.PreRechargeOrder;
import com.dxx.game.dao.dynamodb.model.User;
import com.dxx.game.dao.dynamodb.repository.PreRechargeOrderDao;
import com.dxx.game.dao.dynamodb.repository.UserDao;
import com.dxx.game.dao.redis.AndroidPayCbResultRedisDao;
import com.dxx.game.dto.PayProto;
import com.dxx.game.modules.common.support.CommonHelper;
import com.dxx.game.modules.pay.service.AndroidService;
import com.dxx.game.modules.pay.service.PayService;
import com.dxx.game.modules.user.service.UserService;
import com.google.protobuf.util.JsonFormat;
import io.netty.handler.codec.http.FullHttpRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.CharEncoding;
import org.apache.commons.codec.Charsets;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2021/12/27 9:56
 */
@Slf4j
@Service
@XRayEnabled
public class AndroidServiceImpl implements AndroidService, AndroidCallBackService {


    @Autowired
    private PayService payService;
    @Autowired
    private GameConfigManager gameConfigManager;
    @Autowired
    private UserService userService;
    @Autowired
    private AndroidPayCbResultRedisDao androidPayCbResultRedisDao;
    @Autowired
    private RedisLock redisLock;
    @Autowired
    private ChannelConfig channelConfig;
    @Autowired
    private UserDao userDao;
    @Autowired
    private PreRechargeOrderDao preRechargeOrderDao;


    @DynamoDBTransactional
    @Override
    public Object payCb(FullHttpRequest fullRequest) {
        String uri = fullRequest.uri();
        // 打印回调参数
//        if (gameConfigManager.isPrintApiLog()) {
        String body = fullRequest.content().toString(Charsets.toCharset(CharEncoding.UTF_8));
        log.info("payCb uri:[{}], method:[{}], headers:[{}], params:[{}]", uri, fullRequest.method().name(), fullRequest.headers(), body);
//        }

        try {
            ChannelService payCbService = ChannelFactory.getService(uri);
            if (payCbService == null) {
                log.error("payCb service not found, uri:{}", uri);
                return null;
            }
            return payCbService.payCb(fullRequest);
        } catch (Exception e) {
            log.error("payCb error, e:", e);
            return null;
        }

        //        PayCbVo payCbVo = new PayCbVo();
//        payCbVo.setUserId(10000001);
//        payCbVo.setExtraInfo("");
//        payCbVo.setOrderId("test3");
//        payCbVo.setPreOrderId(1);
//        payCbVo.setCpOrderId("test3");
//        payCbVo.setPlatformIndex(3);
//        payCbVo.setProductId("com.hlm.yiji_d1");
//        this.doDeliverGoods(payCbVo);
    }

    @Override
    public int doDeliverGoods(PayCbVo payCbVo) {
        if (gameConfigManager.isPrintApiLog()) {
            log.info("doDeliverGoods payCbVo:{}", payCbVo);
        }

        try {

            boolean isWhiteUser = channelConfig.isPayWhiteList(payCbVo.getUserId());
            // 校验充值金额
//            if (!isWhiteUser) {
//                int checkAmount = 0;
//                Map<Integer, ShopEntity> config = gameConfigManager.getShopConfig().getShop();
//                for (Map.Entry<Integer, ShopEntity> entry : config.entrySet()) {
//                    if (entry.getValue().getProduct_id().equals(payCbVo.getProductId())) {
//                        // 检测金额转换成分
//                        checkAmount = (int)(entry.getValue().getPrice() * 100);
//                        break;
//                    }
//                }
//                if (checkAmount == 0 || checkAmount > payCbVo.getAmount()) {
//                    log.error("doDeliverGoods amount error, userId:{}, receive:{}, real:{}", payCbVo.getUserId(), payCbVo.getAmount(), checkAmount);
//                    return AndroidPayCbErrorCode.AMOUNT_ERROR;
//                }
//            }

            RequestContext.setUserId(payCbVo.getUserId());
            User user = userDao.getItemWithoutCache(payCbVo.getUserId());
            if (user == null) {
                log.error("doDeliverGoods user not exist, userId:{}", payCbVo.getUserId());
                return AndroidPayCbErrorCode.OTHER_ERROR;
            }

            CommonHelper.buildCommonParams(user);

            redisLock.lock();

            // 保存自定义数据
            RequestContext.setCommand(MsgReqCommand.PayInAppPurchaseRequest);

            // 处理发货
            Result<PayProto.PayInAppPurchaseResponse> result = payService.transferIAPItems(payCbVo.getUserId(), false, payCbVo.getOrderId(), Integer.parseInt(payCbVo.getProductId()),
                    payCbVo.getChannelId(), payCbVo.getChannelId(), payCbVo.getPreOrderId(), payCbVo.getExtraType(), payCbVo.getExtraInfo(), payCbVo.getCpOrderId());

            if (result.getCode() == ErrorCode.SUCCESS) {
                String commonData = JsonFormat.printer().print(result.getContent());
                AndroidPayCbResultRedisDao.AndroidPayCbModel model = new AndroidPayCbResultRedisDao.AndroidPayCbModel();
                model.setCode(0);
                model.setCommonData(commonData);
                model.setRechargeId(result.getContent().getRechargeId());
//                model.setTotalRechargeAmount(result.getContent().getTotalRechargeAmount());
                androidPayCbResultRedisDao.setPayCbResult(payCbVo.getUserId(), payCbVo.getPreOrderId(), model);
                return AndroidPayCbErrorCode.SUCCESS;
            }

            // 重复订单返回成功的结果
            if (result.getCode() == ErrorCode.IAP_ORDER_EXIST) {
                return AndroidPayCbErrorCode.SUCCESS;
            }

            if (result.getCode() != ErrorCode.SUCCESS) {
                DynamoDBTransactionAspectSupport.setRollBack();
                return result.getCode();
            }

            return AndroidPayCbErrorCode.SUCCESS;
        } catch (Exception e) {
            log.error("doDeliverGoods error", e);
            DynamoDBTransactionAspectSupport.setRollBack();
            return AndroidPayCbErrorCode.OTHER_ERROR;
        }
    }

    @Override
    public int getIapAmount(String productId) {
        PurchaseEntity purchaseEntity = this.getByProductId(Integer.parseInt(productId));
        if (purchaseEntity == null) {
            return 0;
        }
        return (int) purchaseEntity.getChinaPrice();
    }

    private PurchaseEntity getByProductId(int productId) {
        return gameConfigManager.getIAPConfig().getPurchaseEntity(productId);
    }

    @Override
    public Map<String, Object> queryOrder(long userId, Long preOrderId) {
        PreRechargeOrder preRechargeOrder = preRechargeOrderDao.getByOrderId(userId, preOrderId);
        Map<String, Object> result = new HashMap<>();
        result.put("type", 0);
        if (preRechargeOrder != null) {
            if (preRechargeOrder.getSuccess() == 1) {
                result.put("type", 1);
                result.put("cpOrderId", preRechargeOrder.getCpOrderId());
                result.put("orderId", preRechargeOrder.getOrderId());
            }else if(isPayCardLoseEfficacy(preRechargeOrder)){
                result.put("type",2);
            }
        }else{
            log.error("queryOrder preRechargeOrder not exist, userId:{}, preOrderId:{}", userId, preOrderId);
        }
        return result;
    }

    private boolean isPayCardLoseEfficacy(PreRechargeOrder preRechargeOrder) {
        if(!gameConfigManager.getMainConfig().isOrderLockEnabled()){
            return false;
        }
        PreRechargeOrder order = payService.getRedisPreRechargeOrder(preRechargeOrder.getUserId(),preRechargeOrder.getPurchaseId());
        if(Objects.isNull(order)){
            return true;
        }
        return !Objects.equals(order.getPreOrderId(),preRechargeOrder.getPreOrderId());
    }

    @Override
    public int cancelAccount(String accountId, String extra) {
//        User user = userDao.getByPlatformUid(accountId);
//        if (user != null) {
//            user.setAccountId(user.getAccountId() + "-" + DateUtils.getUnixTime());
//            user.setDeviceId(user.getDeviceId() + "-" + DateUtils.getUnixTime());
//            user.setExtra(extra);
//            userDao.update(user);
//        }
        return 0;
    }
}
