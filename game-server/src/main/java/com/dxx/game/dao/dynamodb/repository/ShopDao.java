package com.dxx.game.dao.dynamodb.repository;

import com.dxx.game.common.aws.dynamodb.BaseDynamoDBDao;
import com.dxx.game.config.GameConfigManager;
import com.dxx.game.dao.dynamodb.model.Shop;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbTable;
import software.amazon.awssdk.enhanced.dynamodb.Key;
import software.amazon.awssdk.enhanced.dynamodb.TableSchema;
import software.amazon.awssdk.enhanced.dynamodb.model.GetItemEnhancedRequest;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/11/24 16:58
 */
@Repository
public class ShopDao extends BaseDynamoDBDao<Shop> {
    @Autowired
    private GameConfigManager gameConfigManager;

    public Shop getByUserId(long userId) {
        return super.getItem(userId);
    }

    public Shop queryData(int type, long userId) {
        String tableName = gameConfigManager.getDynamoDBTableName(type, this.tableName);
        DynamoDbTable<Shop> propMappedTable = this.dynamoDbEnhancedClient.table(tableName, TableSchema.fromBean(Shop.class));
        Key key = this.buildKey(userId);
        GetItemEnhancedRequest getItemEnhancedRequest = GetItemEnhancedRequest.builder().key(key).consistentRead(true).build();
        return propMappedTable.getItem(getItemEnhancedRequest);
    }

    public Map<Long, Shop> getByUserIds(List<Long> userIds) {
        Map<Long, Shop> result = new HashMap<>();
        List<Shop> datas = super.batchGetItem(userIds);
        for (Shop data : datas) {
            result.put(data.getUserId(), data);
        }
        return result;
    }

}
