package com.dxx.game.dao.dynamodb.repository;

import com.dxx.game.common.aws.dynamodb.BaseDynamoDBDao;
import com.dxx.game.common.aws.dynamodb.cache.DynamoDBCacheManager;
import com.dxx.game.common.aws.dynamodb.utils.DynamoDBConvertUtil;
import com.dxx.game.config.GameConfigManager;
import com.dxx.game.dao.dynamodb.model.Item;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbTable;
import software.amazon.awssdk.enhanced.dynamodb.Expression;
import software.amazon.awssdk.enhanced.dynamodb.Key;
import software.amazon.awssdk.enhanced.dynamodb.TableSchema;
import software.amazon.awssdk.enhanced.dynamodb.model.PageIterable;
import software.amazon.awssdk.enhanced.dynamodb.model.QueryConditional;
import software.amazon.awssdk.enhanced.dynamodb.model.QueryEnhancedRequest;
import software.amazon.awssdk.services.dynamodb.model.AttributeValue;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/12/17 16:01
 */
@Repository
public class ItemDao extends BaseDynamoDBDao<Item> {
    @Autowired
    private GameConfigManager gameConfigManager;

    @Override
    public void insert(Item item) {
        super.insert(item);
        String cacheKey = this.getCacheKey(item);
        DynamoDBCacheManager.put(cacheKey, item);
    }

    public Item getByRowId(long userId, long rowId) {
        Item item = super.getItem(userId, rowId);
        if (item != null) {
            String cacheKey = this.getCacheKey(item);
            DynamoDBCacheManager.put(cacheKey, item);
        }
        return item;
    }

    public List<Item> getAllByUserId(long userId) {
        return super.getAll(userId);

    }

    public Item getByItemId(long userId, int itemId) {
        Expression expression = Expression.builder()
                .expression("itemId = :itemId")
                .putExpressionValue(":itemId", AttributeValue.builder().n(String.valueOf(itemId)).build()).build();
        String cacheKey = this.getCacheKey(userId, itemId);
        return super.queryOne(userId, expression, cacheKey);
//        return super.queryOne(userId, expression);
    }

    public List<Item> getAllByItemId(long userId, int itemId) {
        Expression expression = Expression.builder()
                .expression("itemId = :itemId")
                .putExpressionValue(":itemId", AttributeValue.builder().n(String.valueOf(itemId)).build()).build();
        return super.query(userId, expression);
    }


    public List<Item> getListByRowIds(long userId, List<Long> rowIds) {
        return super.batchGetItem(userId, rowIds);
    }

    private String getCacheKey(Item item) {
        return this.tableName + ":" + item.getUserId() + ":" + item.getItemId();
    }

    private String getCacheKey(long userId, int itemId) {
        return this.tableName + ":" + userId + ":" + itemId;
    }

    /**
     * 根据道具ID查询数据
     * @param userId
     * @param itemIds
     * @return
     */
    public List<Item> queryByItemIds(long userId, List<Integer> itemIds) {
        List<String> conditions = new ArrayList<>();
        Map<String, AttributeValue> expressionValues = new HashMap<>();
        for (int i = 0, len = itemIds.size(); i < len; i ++) {
            String key = ":itemId" + i;
            conditions.add(key);
            expressionValues.put(key, DynamoDBConvertUtil.buildAttributeValue(itemIds.get(i)));
        }
        String str = StringUtils.join(conditions, ",");
        Expression expression = Expression.builder()
                .expression("itemId in (" + str + ")")
                .expressionValues(expressionValues).build();
        List<Item> items = super.query(userId, expression);
        if (!items.isEmpty()) {
            for (Item item : items) {
                String cacheKey = this.getCacheKey(item);
                DynamoDBCacheManager.put(cacheKey, item);
            }
        }
        return items;
    }

    public List<Item> queryData(int type, long userId){
        String tableName = gameConfigManager.getDynamoDBTableName(type, this.tableName);
        DynamoDbTable<Item> propMappedTable = this.dynamoDbEnhancedClient.table(tableName, TableSchema.fromBean(Item.class));
        Key key = this.buildKey(userId);
        QueryEnhancedRequest queryEnhancedRequest = QueryEnhancedRequest.builder()
                .queryConditional(QueryConditional.keyEqualTo(key))
                .consistentRead(true)
                .build();
        PageIterable<Item> pageIterable = propMappedTable.query(queryEnhancedRequest);
        return this.getAllItemsFromQueryResults(pageIterable);
    }

//////////////////////////////// 测试代码 //////////////////////////////////
//    Item item = itemDao.getByRowId(1, 1);
//    Expression expression = Expression.builder().expression("#count = :itemCount")
//            .putExpressionName("#count", "count")
//            .putExpressionValue(":itemCount", DynamoDBConvertUtil.buildAttributeValue(item.getCount()))
//            .build();
//		item.addUpdateCondition(expression);
//		item.setCount(2);
//    Expression updateExpression = Expression.builder().expression("#itemCount = #itemCount + :itemCount")
//            .putExpressionName("#itemCount", "count")
//            .putExpressionValue(":itemCount", DynamoDBConvertUtil.buildAttributeValue(2))
//            .build();
//		item.addUpdateExpression(updateExpression);
//		itemDao.updateIgnoreNulls(item);

}
