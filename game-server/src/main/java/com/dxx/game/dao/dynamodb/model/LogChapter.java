package com.dxx.game.dao.dynamodb.model;

import com.dxx.game.common.aws.dynamodb.annotation.DynamoDBTableName;
import com.dxx.game.common.aws.dynamodb.model.DynamoDBBaseModel;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbIgnore;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbPartitionKey;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSortKey;

/**
 * <AUTHOR>
 * @date 2021/4/9 16:53
 */
@Getter
@Setter
@ToString
@DynamoDbBean
@DynamoDBTableName("log-chapter")
public class LogChapter extends DynamoDBBaseModel {

    @Getter(onMethod_ = {@DynamoDbPartitionKey})
    private Long userId;            // 用户ID
    @Getter(onMethod_ = {@DynamoDbSortKey})
    private String battleUuid;      // 唯一ID

    private Integer chapterId;          // 章节ID
    private Integer costTime;           // 消耗时间
    private Integer type;               // 作弊类型类型

    private String CreatAt;             // 创建时间
    private Long ttlTime;

    @DynamoDbIgnore
    @Override
    public Object getUniqueKey() {
        return userId + "_" + battleUuid;
    }
}
