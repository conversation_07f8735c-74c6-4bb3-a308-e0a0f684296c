package com.dxx.game.dao.dynamodb.model.event;

import com.alibaba.fastjson.annotation.JSONField;
import com.dxx.game.modules.event.consts.EDiveGrid;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.primitives.Ints;
import lombok.Data;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbAttribute;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbIgnore;

import java.util.List;
import java.util.Map;

/**
 * 潜水
 *
 * <AUTHOR>
 * @date 2023/8/30 16:37
 */
@Data
@DynamoDbBean
public class Dive {
    private int eventId;                                        // 活动id

    @Getter(onMethod_ = {@DynamoDbAttribute("f")})
    private boolean first;                                  // 第一次进入活动
    private boolean mail;                                   // 是否发送了邮件
    @Getter(onMethod_ = {@DynamoDbAttribute("d")})
    private int depth;                                      // 潜水深度(米)
    private List<Integer> acc;                              // 已领取累计奖励
    @Getter(onMethod_ = {@DynamoDbAttribute("racc")})
    private int rewardAcc;                                  // 累计获得泡泡奖励

    // 地图
    @Getter(onMethod_ = {@DynamoDbAttribute("ll")})
    private List<DiveLine> lineList;                        // 所有行
    @Getter(onMethod_ = {@DynamoDbAttribute("sLog")})
    private Map<Integer, Integer> specialLog;               // 泡泡奖励出现历史
    @Getter(onMethod_ = {@DynamoDbAttribute("cLog")})
    private Map<Integer, Integer> continuousLog;            //连续奖励出现历史
    @Getter(onMethod_ = {@DynamoDbAttribute("vLog")})
    private Map<Integer, Integer> varecLog;                 //海藻奖励出现历史
    @Getter(onMethod_ = {@DynamoDbAttribute("cpLog")})
    private Map<Integer, Integer> continuousPLog;           // 连续奖励参数出现历史
    @Getter(onMethod_ = {@DynamoDbAttribute("vpLog")})
    private Map<Integer, Integer> varecPLog;                // 海藻参数出现历史

    @Getter(onMethod_ = {@DynamoDbAttribute("cc")})
    private int consumeCnt;                                 // 累计消耗水母

    @Data
    @DynamoDbBean
    public static class DiveLine {
        @Getter(onMethod_ = {@DynamoDbAttribute("d")})
        private int depeth;                                 // 深度
        //        private String cfg;                                 // 配置
        @Getter(onMethod_ = {@DynamoDbAttribute("gl")})
        private List<DiveGrid> gridList;                    // 所有格
    }

    @DynamoDbBean
    public static class DiveGrid {
        @Getter(onMethod_ = {@DynamoDbAttribute("m")})
        private String meta;
        //        @Getter(onMethod_ = {@DynamoDbAttribute("i")})
//        private int idx;
//        @Getter(onMethod_ = {@DynamoDbAttribute("t")})
        private int type;                                   // 格子类型
        //        @Getter(onMethod_ = {@DynamoDbAttribute("s")})
        private Integer status;                             // 状态
        //        @Getter(onMethod_ = {@DynamoDbAttribute("v")})
        private Integer value;                              // 连续奖励剩余数、海藻剩余奖励数
        //        @Getter(onMethod_ = {@DynamoDbAttribute("k")})
        private Integer shark;                              // 鲨鱼
        //        @Getter(onMethod_ = {@DynamoDbAttribute("r")})
        private List<Integer> reward;                       // 奖励

        @JSONField(serialize = false)
        public EDiveGrid getEDiveGridType() {
            return EDiveGrid.getEnumByType(type);
        }

        public String getMeta() {
            return meta;
        }

        @Override
        public String toString() {
            return "DiveGrid{" +
                    "meta='" + meta + '\'' +
                    '}';
        }

        public void setMeta(String meta) {
            this.meta = meta;

            int i = 0;

            List<String> strings = Splitter.on('|').trimResults().splitToList(meta);
//            int idx = Integer.parseInt(strings.get(i++));
//            this.idx = idx;

            int type = Integer.parseInt(strings.get(i++));
            this.type = type;

            Integer status = Ints.tryParse(strings.get(i++));
            this.status = status;

            Integer value = Ints.tryParse(strings.get(i++));
            this.value = value;

            Integer shark = Ints.tryParse(strings.get(i++));
            this.shark = shark;

            String rewardString = strings.size() > i ? strings.get(i) : null;
            if (StringUtils.isNotBlank(rewardString)) {
                String[] rewardArray = rewardString.split(",");
                int itemId = Integer.parseInt(rewardArray[0]);
                int count = Integer.parseInt(rewardArray[1]);
                this.reward = Lists.newArrayList(itemId, count);
            }


        }

        private void makeMeta() {
            StringBuilder sb = new StringBuilder();
//            sb.append(idx).append("|");
            sb.append(type).append("|");
            sb.append(status == null ? "" : status).append("|");
            sb.append(value == null ? "" : value).append("|");
            sb.append(shark == null ? "" : shark);
            if (reward != null) {
                sb.append("|").append(reward.get(0)).append(",").append(reward.get(1));
            }
            this.meta = sb.toString();
        }

//        @DynamoDbIgnore
//        @JSONField(serialize = false)
//        public int getIdx() {
//            return idx;
//        }
//
//        public void setIdx(int idx) {
//            this.idx = idx;
//            makeMeta();
//        }

        @DynamoDbIgnore
        @JSONField(serialize = false)
        public int getType() {
            return type;
        }

        public void setType(int type) {
            this.type = type;
            makeMeta();
        }

        @DynamoDbIgnore
        @JSONField(serialize = false)
        public Integer getValue() {
            return value;
        }

        public void setValue(Integer value) {
            this.value = value;
            makeMeta();
        }

        @DynamoDbIgnore
        @JSONField(serialize = false)
        public Integer getStatus() {
            return status;
        }

        public void setStatus(Integer status) {
            this.status = status;
            makeMeta();
        }

        @DynamoDbIgnore
        @JSONField(serialize = false)
        public Integer getShark() {
            return shark;
        }

        public void setShark(Integer shark) {
            this.shark = shark;
            makeMeta();
        }

        @DynamoDbIgnore
        @JSONField(serialize = false)
        public List<Integer> getReward() {
            return reward;
        }

        public void setReward(List<Integer> reward) {
            this.reward = reward;
            makeMeta();
        }
    }

}
