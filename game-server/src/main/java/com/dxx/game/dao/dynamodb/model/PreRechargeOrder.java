package com.dxx.game.dao.dynamodb.model;

import com.dxx.game.common.aws.dynamodb.annotation.DynamoDBTableName;
import com.dxx.game.common.aws.dynamodb.model.DynamoDBBaseModel;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbIgnore;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbPartitionKey;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSortKey;

/**
 * <AUTHOR>
 * @date 2021/11/8 15:44
 */
@Getter
@Setter
@ToString
@DynamoDbBean
@DynamoDBTableName("prepare-recharge-order")
public class PreRechargeOrder extends DynamoDBBaseModel {

    public static final long expirationTime = 30 * 86400;

    @Getter(onMethod_ = {@DynamoDbPartitionKey})
    private Long userId;
    @Getter(onMethod_ = {@DynamoDbSortKey})
    private Long preOrderId;        // 预下单ID
    private String deviceId;
    private String extraInfo;   // 额外参数
    private Integer success;    // 是否成功购买
    private Long ttlTime;       // 过期时间(一个月)
    private int purchaseId;
    private String cpOrderId;
    private String orderId;

    @DynamoDbIgnore
    @Override
    public Object getUniqueKey() {
        return userId + "-" + preOrderId;
    }

    @DynamoDbIgnore
    public long getOrderLockExpirationTime() {
        return this.ttlTime - expirationTime + 300;
    }
}
