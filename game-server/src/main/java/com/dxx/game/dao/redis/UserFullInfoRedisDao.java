package com.dxx.game.dao.redis;

import com.dxx.game.common.redis.RedisService;
import com.dxx.game.common.utils.DateUtils;
import com.dxx.game.consts.RedisKeys;
import com.dxx.game.dao.dynamodb.model.User;
import com.dxx.game.dto.CommonProto;
import com.dxx.game.dto.UserProto;
import com.dxx.game.modules.user.model.UserInfoModel;
import com.google.protobuf.InvalidProtocolBufferException;
import com.google.protobuf.util.JsonFormat;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Repository;
import software.amazon.awssdk.services.dynamodb.model.AttributeValue;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * @authoer: lsc
 * @createDate: 2023/3/23
 * @description:
 */
@Slf4j
@Repository
public class UserFullInfoRedisDao {

    @Resource
    private RedisService redisService;
    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    public void updateInfo(UserProto.PlayerInfoDto info) {
        try {
            String js = JsonFormat.printer().print(info);
            redisService.set(getKey(info.getUserId()), js, 5 * DateUtils.MINUTE_PER_SECONDS);
        } catch (Exception e) {
            log.error("UserFullInfoRedisDao updateInfo e:", e);
        }
    }

    public void updateInfos(List<UserProto.PlayerInfoDto> infos) {
        RedisCallback<Object> callback = redisConnection -> {
            infos.forEach((info) -> {
                String key = getKey(info.getUserId());
                try {
                    String js = JsonFormat.printer().print(info);
                    redisConnection.setEx(key.getBytes(StandardCharsets.UTF_8),
                            5 * DateUtils.MINUTE_PER_SECONDS,
                            js.getBytes(StandardCharsets.UTF_8));
                } catch (InvalidProtocolBufferException e) {
                    throw new RuntimeException(e);
                }
            });
            return null;
        };

        redisTemplate.executePipelined(callback);
    }

    public UserProto.PlayerInfoDto getData(long userId) {
        String data = redisService.get(getKey(userId));
        if(data == null) {
            return null;
        }
        UserProto.PlayerInfoDto.Builder dto = UserProto.PlayerInfoDto.newBuilder();

        try {
            JsonFormat.parser().merge(data, dto);
        } catch (Exception e) {
            log.error("UserFullInfoRedisDao getData e:", e);
        }

        return dto.build();
    }

    public Map<Long, UserProto.PlayerInfoDto> getData(List<Long> userIds) {
        RedisCallback<String> callback = redisConnection -> {
            for (Long userId : userIds) {
                String key = getKey(userId);
                redisConnection.get(key.getBytes(StandardCharsets.UTF_8));
            }
            return null;
        };

        List<Object> redisResult = redisTemplate.executePipelined(callback);
        Map<Long, UserProto.PlayerInfoDto> result = new HashMap<>();
        for (int i = 0; i < redisResult.size(); i ++) {
           String redisData = (String) redisResult.get(i);
            if (redisData == null || redisData.isEmpty()) {
                continue;
            }
            UserProto.PlayerInfoDto.Builder dto = UserProto.PlayerInfoDto.newBuilder();

            try {
                JsonFormat.parser().merge(redisData, dto);
            } catch (Exception e) {
                log.error("UserFullInfoRedisDao getData e:", e);
            }

            result.put(dto.getUserId(), dto.build());
        }

        return result;
    }

    private String getKey(long userId) {
        return RedisKeys.USER_FULL_INFO + userId;
    }
}
