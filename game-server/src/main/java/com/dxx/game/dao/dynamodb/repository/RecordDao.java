package com.dxx.game.dao.dynamodb.repository;

import com.dxx.game.common.aws.dynamodb.BaseDynamoDBDao;
import com.dxx.game.dao.dynamodb.model.Record;
import org.springframework.stereotype.Repository;
import software.amazon.awssdk.enhanced.dynamodb.Expression;
import software.amazon.awssdk.services.dynamodb.model.AttributeValue;

import java.util.ArrayList;
import java.util.List;

@Repository
public class RecordDao extends BaseDynamoDBDao<Record> {
    public Record getByRowId(long userId, long rowId) {
        return super.getItem(userId, rowId);
    }

    public List<Record> getAllRecords(long userId, int type) {
        Expression expression = Expression.builder()
                .expression("#type = :type")
                .putExpressionName("#type", "type")
                .putExpressionValue(":type", AttributeValue.builder().n(String.valueOf(type)).build()).build();
        return super.query(userId, expression);
    }
}
