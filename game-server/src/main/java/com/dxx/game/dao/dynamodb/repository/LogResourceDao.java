package com.dxx.game.dao.dynamodb.repository;

import com.dxx.game.common.aws.dynamodb.BaseDynamoDBDao;
import com.dxx.game.dao.dynamodb.model.LogResource;
import org.springframework.stereotype.Repository;
import software.amazon.awssdk.enhanced.dynamodb.Key;
import software.amazon.awssdk.enhanced.dynamodb.model.QueryConditional;
import software.amazon.awssdk.enhanced.dynamodb.model.QueryEnhancedRequest;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/4/9 17:02
 */
@Repository
public class LogResourceDao extends BaseDynamoDBDao<LogResource> {

    public List<LogResource> queryModify(long userId) {
        Key key = this.buildKey(userId);
        QueryEnhancedRequest queryEnhancedRequest = QueryEnhancedRequest.builder()
                .queryConditional(QueryConditional.keyEqualTo(key))
                .consistentRead(true)
                .scanIndexForward(false)
                .limit(50)
                .build();
        return mappedTable.query(queryEnhancedRequest).stream().findFirst().get().items();
    }

    public LogResource getByTransId(long userId, long transId) {
        return super.getItem(userId, transId);
    }
}
