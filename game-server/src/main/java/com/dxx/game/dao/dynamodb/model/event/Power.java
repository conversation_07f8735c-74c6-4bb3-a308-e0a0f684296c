package com.dxx.game.dao.dynamodb.model.event;

import lombok.Data;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean;

import java.util.List;

@Data
@DynamoDbBean
public class Power {
    private int eventId;                                        // 活动id

    private boolean mail;                                   // 是否发送了邮件

    /** 已领奖的id */
    private List<Integer> rewardIds;
}
