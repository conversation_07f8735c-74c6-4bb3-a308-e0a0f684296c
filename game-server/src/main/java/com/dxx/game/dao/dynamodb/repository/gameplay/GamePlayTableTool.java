package com.dxx.game.dao.dynamodb.repository.gameplay;

import com.dxx.game.dao.dynamodb.model.gameplay.*;
import org.springframework.stereotype.Repository;
import software.amazon.awssdk.enhanced.dynamodb.TableSchema;

import java.util.HashMap;

@Repository
public class GamePlayTableTool extends TableTool<Base> {

    @Override
    void initSkMap() {
        skMap = new HashMap<>();
        skMap.put(UserMaze.SK, TableSchema.fromBean(UserMaze.class));
        skMap.put(UserExpedition.SK, TableSchema.fromBean(UserExpedition.class));
        skMap.put(UserChapter.SK, TableSchema.fromBean(UserChapter.class));
        skMap.put(UserChapterBattle.SK, TableSchema.fromBean(UserChapterBattle.class));
        skMap.put(UserFixedFormation.Chapter_SK, TableSchema.fromBean(UserFixedFormation.class));
        skMap.put(UserFixedFormation.Maze_SK, TableSchema.fromBean(UserFixedFormation.class));
        skMap.put(UserFixedFormation.Expedition_SK, TableSchema.fromBean(UserFixedFormation.class));
    }
}
