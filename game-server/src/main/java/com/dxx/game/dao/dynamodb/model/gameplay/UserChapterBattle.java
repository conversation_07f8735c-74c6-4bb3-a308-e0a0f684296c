package com.dxx.game.dao.dynamodb.model.gameplay;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean;

@Getter
@Setter
@ToString
@DynamoDbBean
public class UserChapterBattle extends Base {

    protected Integer curChapterId;
    protected Integer curWaveIndex;
    protected String uuid;
    protected Long startTime;
    protected String clientBattleData;
    protected String serverBattleData;

    public static final String SK = "ChapterBattle";

    public UserChapterBattle(Long userId) {
        super(userId, SK);
    }

    public UserChapterBattle(String userId) {
        super(userId, SK);
    }


    public UserChapterBattle() {
        super();
    }

    public void clear() {
        this.clientBattleData = "";
        this.serverBattleData = "";
        this.startTime = 0L;
        this.uuid = "";
        this.curChapterId = 0;
        this.curWaveIndex = 0;
    }

    public UserChapterBattle clone(){
        UserChapterBattle userChapterBattle = new UserChapterBattle();
        userChapterBattle.setCurChapterId(this.curChapterId);
        userChapterBattle.setCurWaveIndex(this.curWaveIndex);
        userChapterBattle.setUuid(this.uuid);
        userChapterBattle.setStartTime(this.startTime);
        userChapterBattle.setClientBattleData(this.clientBattleData);
        userChapterBattle.setServerBattleData(this.serverBattleData);
        return userChapterBattle;
    }
}
