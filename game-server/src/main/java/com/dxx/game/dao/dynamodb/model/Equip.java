package com.dxx.game.dao.dynamodb.model;

import com.dxx.game.common.aws.dynamodb.annotation.DynamoDBTableName;
import com.dxx.game.common.aws.dynamodb.model.DynamoDBBaseModel;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbIgnore;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbPartitionKey;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSortKey;

/**
 * <AUTHOR>
 * @date 2021/4/7 10:26
 */
@Getter
@Setter
@ToString
@DynamoDbBean
@DynamoDBTableName("equip")
public class Equip extends DynamoDBBaseModel {
    @Getter(onMethod_ = {@DynamoDbPartitionKey})
    private Long userId;            // 用户ID
    @Getter(onMethod_ = {@DynamoDbSortKey})
    private Long rowId;             // 唯一ID
    private Integer equipId;        // 装备配置ID
    private Integer level;          // 等级
    private Integer exp;            // 经验
    private Long heroRowId;            // 穿戴的英雄ID

    @DynamoDbIgnore
    @Override
    public String getUniqueKey() {
        return userId + "_" + rowId;
    }
}
