package com.dxx.game.dao.dynamodb.repository.gameplay;

import com.dxx.game.common.aws.dynamodb.annotation.DynamoDBTableName;
import com.dxx.game.common.aws.dynamodb.cache.DynamoDBCacheManager;
import com.dxx.game.common.aws.dynamodb.model.DynamoDBBaseModel;
import com.dxx.game.common.aws.dynamodb.model.mapper.DynamoDBMapperRegistry;
import com.dxx.game.common.aws.dynamodb.transaction.DynamoDBTransactionAspectSupport;
import com.dxx.game.config.GameConfigManager;
import org.springframework.beans.factory.annotation.Autowired;
import software.amazon.awssdk.enhanced.dynamodb.Key;
import software.amazon.awssdk.enhanced.dynamodb.TableSchema;
import software.amazon.awssdk.services.dynamodb.DynamoDbClient;
import software.amazon.awssdk.services.dynamodb.model.AttributeValue;
import software.amazon.awssdk.services.dynamodb.model.QueryRequest;
import software.amazon.awssdk.services.dynamodb.model.QueryResponse;
import software.amazon.awssdk.services.dynamodb.model.ReturnConsumedCapacity;

import javax.annotation.PostConstruct;
import java.lang.reflect.ParameterizedType;
import java.util.HashMap;
import java.util.Map;

public abstract class TableTool<T extends DynamoDBBaseModel> {
    @Autowired
    private GameConfigManager gameConfigManager;
    @Autowired
    protected DynamoDbClient dynamoDbClient;

    protected String tableName;

    protected Class<T> tClass;

    protected Map<String, TableSchema<?>> skMap;

    abstract void initSkMap();

    @PostConstruct
    private void init() {
        if (!this.getClass().getGenericSuperclass().getTypeName().equals("java.lang.Object")) {
            this.tClass = (Class<T>)((ParameterizedType)getClass().getGenericSuperclass()).getActualTypeArguments()[0];
            this.tableName = DynamoDBMapperRegistry.getRealTableName(tClass.getAnnotation(DynamoDBTableName.class).value());
            initSkMap();
        }
    }

    public void insertData(Map<String, T> data){
        for (var entry : data.entrySet()) {
            DynamoDBTransactionAspectSupport.addPutItem(entry.getValue());
            DynamoDBCacheManager.put(entry.getValue());
        }
    }

    public Map<String, T> queryData(int type, Long userId){
        String tableName = gameConfigManager.getDynamoDBTableName(type, this.tableName);
        return queryAll(tableName, String.valueOf(userId));
    }

    public Map<String, T> queryAll(Long userId){
        return queryAll(tableName, String.valueOf(userId));
    }

    private Map<String, T> queryAll(String tableName, String pkValue){
        var result = new HashMap<String, T>();
        var pkName = "pk";
        var pkAlias = "#pk";

        HashMap<String, String> attrNameAlias = new HashMap<>();
        attrNameAlias.put(pkAlias, pkName);

        HashMap<String, AttributeValue> attrValues = new HashMap<>();
        attrValues.put(":" + pkName, AttributeValue.builder()
                .s(pkValue)
                .build());

        QueryRequest queryReq = QueryRequest.builder()
                .tableName(tableName)
                .keyConditionExpression(pkAlias + " = :" + pkName)
                .expressionAttributeNames(attrNameAlias)
                .expressionAttributeValues(attrValues)
                .consistentRead(true)
                .returnConsumedCapacity(ReturnConsumedCapacity.INDEXES)
                .build();

        var key = buildKey(pkValue);
        QueryResponse response = this.dynamoDbClient.query(queryReq);
        for (var item : response.items()) {
            var sk = item.get("sk").s();
            var schema = skMap.get(sk);
            if (schema == null) {
                continue;
            }
            var obj = schema.mapToItem(item);
            result.put(sk, (T)obj);
        }
        return result;
    }


    /**
     * 构建Key
     * @param partitionKey
     * @return
     */
    public Key buildKey(Object partitionKey) {
        Key.Builder keyBuilder = Key.builder();
        if (partitionKey instanceof Long) {
            keyBuilder.partitionValue((Long) partitionKey);
        } else if (partitionKey instanceof Integer) {
            keyBuilder.partitionValue((Integer) partitionKey);
        } else if (partitionKey instanceof String) {
            keyBuilder.partitionValue(partitionKey.toString());
        }
        return keyBuilder.build();
    }

    /**
     * 构建key 主键和排序键
     * @param partitionKey
     * @param sortKey
     * @return
     */
    public Key buildKey(Object partitionKey, Object sortKey) {
        Key.Builder keyBuilder = Key.builder();
        if (partitionKey instanceof Long) {
            keyBuilder.partitionValue((Long) partitionKey);
        } else if (partitionKey instanceof Integer) {
            keyBuilder.partitionValue((Integer) partitionKey);
        } else if (partitionKey instanceof String) {
            keyBuilder.partitionValue(partitionKey.toString());
        }

        if (sortKey instanceof Long) {
            keyBuilder.sortValue((Long) sortKey);
        } else if (sortKey instanceof Integer) {
            keyBuilder.sortValue((Integer) sortKey);
        } else if (sortKey instanceof String) {
            keyBuilder.sortValue(sortKey.toString());
        }

        return keyBuilder.build();
    }
}
