package com.dxx.game.dao.dynamodb.repository.gameplay;

import com.dxx.game.common.aws.dynamodb.BaseDynamoDBDao;
import com.dxx.game.dao.dynamodb.model.gameplay.Base;


public abstract class BaseDao<T extends Base> extends BaseDynamoDBDao<T> {

    public T getData(String pk, String sk) {
        return super.getItem(pk, sk);
    }

    public T getData(Long pk, String sk) {
        return super.getItem(String.valueOf(pk), sk);
    }
}
