package com.dxx.game.dao.dynamodb.model;

import com.dxx.game.common.aws.dynamodb.annotation.DynamoDBTableName;
import com.dxx.game.common.aws.dynamodb.model.DynamoDBBaseModel;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbIgnore;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbPartitionKey;

/**
 * <AUTHOR>
 * @date 2023/8/17 20:36
 */
@Getter
@Setter
@ToString
@DynamoDbBean
@DynamoDBTableName("report")
public class Report extends DynamoDBBaseModel {
    @Getter(onMethod_ = {@DynamoDbPartitionKey})
    private Long rowId;            // 唯一ID

    private String reportInfo;

    private Long time;              // 战斗时间
    private Long ttlTime;

    @DynamoDbIgnore
    @Override
    public Long getUniqueKey() {
        return this.rowId;
    }
}
