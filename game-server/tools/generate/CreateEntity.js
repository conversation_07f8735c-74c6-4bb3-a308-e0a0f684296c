var fs = require("fs");
var path = require("path");

var args = process.argv.splice(2);
if (args.length <= 1) {
	console.log("please enter dbName and entityName...");
	process.exit();
}

var dbName = args[0];
var entityName = args[1];
var tmpFilePath = path.join(__dirname, "./entityTmp/entityTmp.txt");
var makeFilePath = path.join(__dirname, "../../src/main/java/com/dxx/game/entity");

var classStr = "";

make();

function make() {

	var sql = fs.readFileSync(path.join(__dirname, "../sql/data/game.sql"), 'utf-8');
	if (dbName.toLowerCase() == "global") {
		sql = fs.readFileSync(path.join(__dirname, "../sql/data/global.sql"), 'utf-8');
	}
	sql = clearNotes(sql);

	var tableMatches = sql.match(/CREATE TABLE (IF NOT EXISTS.+?)?.* /g);
	var tables = {};
	tableMatches.forEach(function(tmp) {
		var a = tmp.trim().split(' ');
	    var tableName = a[a.length-1].replace('`', '').replace('`', '');

	    var startPos = sql.indexOf(tmp);
		var endPos = sql.indexOf(';', startPos) + 1;

	    tables[tableName.toLowerCase()] = sql.substring(startPos, endPos);
	});

	if (!tables.hasOwnProperty(entityName.toLowerCase())) {
		console.log('entity ' + entityName + ' not exist');
		process.exit();
	}

	var column = getcolumn(tables[entityName.toLowerCase()]); 

	for (var key in column) {
		
		var isBigInteger = false;
		if (column[key].indexOf('BigInteger') >= 0) {
			isBigInteger = true;
		}
		var type = column[key].split(" ")[0];
		type = type.split("(")[0];
		var fieldType = "";
		if (type == "bigint") {
			if (isBigInteger) {
				fieldType = "BigInteger";
			} else {
				fieldType = "Long";
			}
			
		} else if (type == "int") {
			fieldType = "Integer";
		} else if (type == 'bit') {
			fieldType = "Boolean";
		} else if (type == 'tinyint' || type == 'smallint') {
			fieldType = "Short";
		} else {
			fieldType = "String";
		}

		var fieldName = makeFieldName(key);
		classStr += "\tprivate " + fieldType + " " + fieldName + ";\r\n";
	}

	

	var className  = makeFieldName(ucfirst(entityName));
	var content = fs.readFileSync(tmpFilePath,'utf-8');
	content = content.replace("$0", classStr);
	content = content.replace("$1", className);

	var exist = fs.existsSync(makeFilePath + "/" + className + ".java");
	if (exist) {
		console.log(makeFilePath + "/" + className + ".java" + " is exist");
		process.exit();
	}
	var fd = fs.openSync(makeFilePath + "/" + className + ".java", 'w');
	fs.writeSync(fd, content, 0, "utf-8");
	fs.closeSync(fd);

	console.log("success : " + makeFilePath + "/" + className + ".java");
}

function makeFieldName(name) {
	name = name.split("_");
	var result = "";
	for (var i = 0; i < name.length; i ++) {
		var n = name[i];
		if (i > 0) {
			n = ucfirst(n);
		}
		result += n;
	}
	
	return result;
}

function ucfirst(value) {
	return value.substring(0, 1).toUpperCase() + value.substring(1)
}

function remakesql(value) {
    value = value.trim();
    value = value.replace('`', '').replace('`', '').replace(', ',',').replace(' ,',',').replace('( ', '(').replace(' )',')').replace('mediumtext', 'text');
    return value;
}

function getcolumn(creatsql) {
    var cols = creatsql.split('\n');
    cols.pop();
    cols.shift();
    var newcols = {};
    cols.forEach(function(value){
        value = value.trim();
        if(value.length == 0)
            return;
        value = remakesql(value);
        if(value.substr(value.length - 1, 1) == ',')
            value = value.substr(0, value.length - 1);

        var vs = value.split(' ');
        var cname = vs[0];

        if(cname == 'KEY' || cname == 'INDEX' || cname == 'UNIQUE' || cname == 'PRIMARY') {
        	return;
        } 

        newcols[cname] = value.substr(cname.length).trim();
    });
    return newcols;
}

// 去掉注释
function clearNotes(sql) {
	// 去除如/***/的注释
	sql = sql.replace(/(?:^|\n|\r)\s*\/\*[\s\S]*?\*\/\s*(?:\r|\n|$)/g, "");
	// 去除如--类的注释
	sql = sql.replace(/(?:^|\n|\r)\s*--.*(?:\r|\n|$)/g, "");
	return sql;
}