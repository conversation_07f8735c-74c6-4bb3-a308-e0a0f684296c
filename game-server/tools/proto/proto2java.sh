#!/bin/bash

echo "Proto file generator..."

# Proto files directory (no modification needed)
cur_path="./proto/"
# Class generation directory
java_out_path="../../src/main/java/"

# File modification time check
node ./filetime.js "$cur_path"

# Loop through all .proto files
for file in "$cur_path"*.proto; do

    filename="${file##*/}"          # Get the filename including the extension
    filename="${filename%.*}"       # Remove the extension

    # Check if file has been modified
    node ./checkmodify.js "$filename"
    if [[ $? -eq 1 ]]; then         # Use [[ ]] for conditionals with spaces
        echo "$file"
        # Generate Java classes for the .proto file
        ./tool/protoc --java_out="$java_out_path" --plugin=protoc-gen-grpc-java="./tool/protoc-gen-grpc-java-osx-aarch_64.exe" --grpc-java_out "$java_out_path" --proto_path="$cur_path" "$file"
        echo "Generated Java classes for $filename.proto"
    fi
done

# Convert all .proto files to JSON format
node ../node_modules/protobufjs/bin/pbjs -t json "$cur_path"*.proto > proto.json
mv proto.json ../api/proto/

# Create message IDs
node ./createMsgId.js "$cur_path"

echo "done!"

# Wait for user input before exiting
read -p "Press any key to continue..."