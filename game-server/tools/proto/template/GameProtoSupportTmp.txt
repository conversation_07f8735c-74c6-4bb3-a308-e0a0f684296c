package com.dxx.game.dto.support;

import javax.annotation.PostConstruct;
import com.dxx.game.consts.MsgReqCommand;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.dxx.game.common.server.protocol.MessageProto;

@Component
public class GameProtoSupport {
	
	@Autowired
	private MessageProto messageProto;
	
	@PostConstruct
	private void initialize() {
		$0
	}
}
