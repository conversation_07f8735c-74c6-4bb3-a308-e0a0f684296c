@echo off


REM 跳转到Build.dat目录
cd %~dp0
REM 删除cs文件夹
echo delect c#
if exist .\CSharp\ (
	del /s/q .\CSharp\
) else (
	md .\CSharp\
)
echo delect c# finished
REM 删除Java文件夹
echo delect java
if exist .\Java\ (
	del /s/q .\Java\
) else (
	md .\Java\
)
echo delect java finished
pause

REM 运行CSharpCreator.py
echo run CSharpCreator.py
python .\CSharpCreator.py
echo run CSharpCreator.py finished
pause

REM 跳转到Proto文件目录
cd .\Proto\
REM 打包cs
echo start build c#
REM Proto文件夹查找.proto
for %%i in (*.proto) do (
	echo  %%i -------- .\CSharp\%%i.cs
   ..\tool\protoc.exe --csharp_out=..\CSharp --grpc-csharp_out=..\CSharp %%i 
   )
echo build c# finished... 
REM 打包java
echo start build java
REM Proto文件夹查找.proto
for %%i in (*.proto) do (
	echo  %%i -------- .\Java\%%i.java
   ..\tool\protoc.exe --java_out=..\Java --grpc-java_out=..\Java  %%i 
   )
echo build java finished... 
pause