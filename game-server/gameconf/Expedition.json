{"ExpeditionStage": {"1": {"Id": 1, "power": 0.5, "skillPoolId": [[3001, 1]], "robotWeight": 100, "reward": [[1007, 1], [1041, 1]], "ExpeditionItem": 0, "failSkillPoolId": [[3101, 1]], "UpForGrade": [0, 0.2, 0.2], "PlayerData": 500}, "2": {"Id": 2, "power": 0.65, "skillPoolId": [[3002, 1]], "robotWeight": 70, "reward": [[1007, 2], [1041, 2]], "ExpeditionItem": 0, "failSkillPoolId": [[3101, 1]], "UpForGrade": [0, 0.2, 0.3], "PlayerData": 500}, "3": {"Id": 3, "power": 0.8, "skillPoolId": [[3003, 1]], "robotWeight": 50, "reward": [[1007, 3], [1041, 3]], "ExpeditionItem": 0, "failSkillPoolId": [[3101, 1]], "UpForGrade": [0, 0.2, 0.4], "PlayerData": 500}, "4": {"Id": 4, "power": 0.9, "skillPoolId": [[3004, 1]], "robotWeight": 20, "reward": [[1007, 5], [1041, 5]], "ExpeditionItem": 1, "failSkillPoolId": [[3101, 1]], "UpForGrade": [0, 0.2, 0.5], "PlayerData": 500}, "5": {"Id": 5, "power": 1, "skillPoolId": [[3005, 1]], "robotWeight": 0, "reward": [[1004, 1], [1007, 5], [1041, 6]], "ExpeditionItem": 0, "failSkillPoolId": [[3102, 1]], "UpForGrade": [0, 0.2, 0.6], "PlayerData": 500}, "6": {"Id": 6, "power": 1.17, "skillPoolId": [[3006, 1]], "robotWeight": 0, "reward": [[1004, 2], [1007, 5], [1041, 7]], "ExpeditionItem": 0, "failSkillPoolId": [[3102, 1]], "UpForGrade": [0, 0.2, 0.7], "PlayerData": 500}, "7": {"Id": 7, "power": 1.25, "skillPoolId": [[3007, 1]], "robotWeight": 0, "reward": [[1004, 3], [1007, 5], [1041, 8]], "ExpeditionItem": 0, "failSkillPoolId": [[3102, 1]], "UpForGrade": [0, 0.2, 0.8], "PlayerData": 500}, "8": {"Id": 8, "power": 1.4, "skillPoolId": [[3007, 1]], "robotWeight": 0, "reward": [[1004, 5], [1007, 5], [1041, 10]], "ExpeditionItem": 0, "failSkillPoolId": [[3102, 1]], "UpForGrade": [0, 0.2, 0.9], "PlayerData": 500}}, "ExpeditionItem": {"1": {"Id": 1, "weight": 20}, "2": {"Id": 2, "weight": 20}, "3": {"Id": 3, "weight": 20}, "4": {"Id": 4, "weight": 20}, "5": {"Id": 5, "weight": 20}}, "ExpeditionRobot": {"1": {"Id": 1, "memberData1": 100051, "memberData2": 100081, "memberData3": 100058, "memberData4": 100072, "memberData5": 100056}, "2": {"Id": 2, "memberData1": 100051, "memberData2": 100056, "memberData3": 100063, "memberData4": 100071, "memberData5": 100059}, "3": {"Id": 3, "memberData1": 100051, "memberData2": 100064, "memberData3": 100054, "memberData4": 100057, "memberData5": 100067}, "4": {"Id": 4, "memberData1": 100051, "memberData2": 100072, "memberData3": 100068, "memberData4": 100077, "memberData5": 100055}, "5": {"Id": 5, "memberData1": 100051, "memberData2": 100077, "memberData3": 100066, "memberData4": 100079, "memberData5": 100073}, "6": {"Id": 6, "memberData1": 100051, "memberData2": 100061, "memberData3": 100071, "memberData4": 100068, "memberData5": 100062}, "7": {"Id": 7, "memberData1": 100051, "memberData2": 100054, "memberData3": 100082, "memberData4": 100063, "memberData5": 100057}, "8": {"Id": 8, "memberData1": 100051, "memberData2": 100057, "memberData3": 100052, "memberData4": 100068, "memberData5": 100077}, "9": {"Id": 9, "memberData1": 100051, "memberData2": 100081, "memberData3": 100061, "memberData4": 100054, "memberData5": 100079}, "10": {"Id": 10, "memberData1": 100051, "memberData2": 100061, "memberData3": 100065, "memberData4": 100063, "memberData5": 100052}}, "ExpeditionRobotSkill": {"1": {"Id": 1, "robotStageId": 1, "robotSkillId": []}, "2": {"Id": 2, "robotStageId": 1, "robotSkillId": []}, "3": {"Id": 3, "robotStageId": 1, "robotSkillId": []}, "4": {"Id": 4, "robotStageId": 1, "robotSkillId": []}, "5": {"Id": 5, "robotStageId": 1, "robotSkillId": []}, "6": {"Id": 6, "robotStageId": 2, "robotSkillId": [61001, 61002]}, "7": {"Id": 7, "robotStageId": 2, "robotSkillId": [61002, 61019]}, "8": {"Id": 8, "robotStageId": 2, "robotSkillId": [61017, 61025]}, "9": {"Id": 9, "robotStageId": 2, "robotSkillId": [61022, 61001]}, "10": {"Id": 10, "robotStageId": 2, "robotSkillId": [61001, 61022]}, "11": {"Id": 11, "robotStageId": 3, "robotSkillId": [61001, 61002, 61004, 61005]}, "12": {"Id": 12, "robotStageId": 3, "robotSkillId": [61002, 61019, 61009, 61010]}, "13": {"Id": 13, "robotStageId": 3, "robotSkillId": [61017, 61025, 71001, 61022]}, "14": {"Id": 14, "robotStageId": 3, "robotSkillId": [61022, 61001, 61011, 61004]}, "15": {"Id": 15, "robotStageId": 3, "robotSkillId": [61001, 61022, 71001, 61023]}, "16": {"Id": 16, "robotStageId": 4, "robotSkillId": [61001, 61002, 61004, 61005, 61001, 61004]}, "17": {"Id": 17, "robotStageId": 4, "robotSkillId": [61002, 61019, 61009, 61010, 61002, 61009]}, "18": {"Id": 18, "robotStageId": 4, "robotSkillId": [61017, 61025, 71001, 61022, 61001, 61002]}, "19": {"Id": 19, "robotStageId": 4, "robotSkillId": [61022, 61001, 61011, 61004, 61014, 71011]}, "20": {"Id": 20, "robotStageId": 4, "robotSkillId": [61001, 61022, 71001, 61023, 61001, 61028]}, "21": {"Id": 21, "robotStageId": 5, "robotSkillId": [61001, 61002, 61004, 61005, 61001, 61004, 61006, 61002]}, "22": {"Id": 22, "robotStageId": 5, "robotSkillId": [61002, 61019, 61009, 61010, 61002, 61009, 61012, 61026]}, "23": {"Id": 23, "robotStageId": 5, "robotSkillId": [61017, 61025, 71001, 61022, 61001, 61002, 71002, 61017]}, "24": {"Id": 24, "robotStageId": 5, "robotSkillId": [61022, 61001, 61011, 61004, 61014, 71011, 61015, 61024]}, "25": {"Id": 25, "robotStageId": 5, "robotSkillId": [61001, 61022, 71001, 61023, 61001, 61028, 61019, 61033]}, "26": {"Id": 26, "robotStageId": 6, "robotSkillId": [61001, 61002, 61004, 61005, 61001, 61004, 61006, 61002, 61016, 61002]}, "27": {"Id": 27, "robotStageId": 6, "robotSkillId": [61002, 61019, 61009, 61010, 61002, 61009, 61012, 61026, 61009, 61017]}, "28": {"Id": 28, "robotStageId": 6, "robotSkillId": [61017, 61025, 71001, 61022, 61001, 61002, 71002, 61017, 61026, 61027]}, "29": {"Id": 29, "robotStageId": 6, "robotSkillId": [61022, 61001, 61011, 61004, 61014, 71011, 61015, 61024, 61005, 61031]}, "30": {"Id": 30, "robotStageId": 6, "robotSkillId": [61001, 61022, 71001, 61023, 61001, 61028, 61019, 61033, 61027, 61019]}, "31": {"Id": 31, "robotStageId": 7, "robotSkillId": [61001, 61002, 61004, 61005, 61001, 61004, 61006, 61002, 61016, 61002, 61020, 61029]}, "32": {"Id": 32, "robotStageId": 7, "robotSkillId": [61002, 61019, 61009, 61010, 61002, 61009, 61012, 61026, 61009, 61017, 61011, 61003]}, "33": {"Id": 33, "robotStageId": 7, "robotSkillId": [61017, 61025, 71001, 61022, 61001, 61002, 71002, 61017, 61026, 61027, 61011, 61009]}, "34": {"Id": 34, "robotStageId": 7, "robotSkillId": [61022, 61001, 61011, 61004, 61014, 71011, 61015, 61024, 61005, 61031, 71012, 61016]}, "35": {"Id": 35, "robotStageId": 7, "robotSkillId": [61001, 61022, 71001, 61023, 61001, 61028, 61019, 61033, 61027, 61019, 61001, 61023]}, "36": {"Id": 36, "robotStageId": 8, "robotSkillId": [61001, 61002, 61004, 61005, 61001, 61004, 61006, 61002, 61016, 61002, 61020, 61029, 61007, 61034]}, "37": {"Id": 37, "robotStageId": 8, "robotSkillId": [61002, 61019, 61009, 61010, 61002, 61009, 61012, 61026, 61009, 61017, 61011, 61003, 61035, 61015]}, "38": {"Id": 38, "robotStageId": 8, "robotSkillId": [61017, 61025, 71001, 61022, 61001, 61002, 71002, 61017, 61026, 61027, 61011, 61009, 61035, 61006]}, "39": {"Id": 39, "robotStageId": 8, "robotSkillId": [61022, 61001, 61011, 61004, 61014, 71011, 61015, 61024, 61005, 61031, 71012, 61016, 61032, 61007]}, "40": {"Id": 40, "robotStageId": 8, "robotSkillId": [61001, 61022, 71001, 61023, 61001, 61028, 61019, 61033, 61027, 61019, 61001, 61023, 61034, 61032]}}, "ExpeditionRobotPower": {"1": {"Id": 1, "heroLv": [40, 45], "heroQuality": [0, 2]}, "2": {"Id": 2, "heroLv": [40, 45], "heroQuality": [0, 2]}, "3": {"Id": 3, "heroLv": [45, 50], "heroQuality": [2, 4]}, "4": {"Id": 4, "heroLv": [45, 50], "heroQuality": [4, 5]}, "5": {"Id": 5, "heroLv": [50, 55], "heroQuality": [5, 8]}, "6": {"Id": 6, "heroLv": [55, 60], "heroQuality": [5, 8]}, "7": {"Id": 7, "heroLv": [55, 60], "heroQuality": [5, 8]}, "8": {"Id": 8, "heroLv": [60, 80], "heroQuality": [8, 10]}}}