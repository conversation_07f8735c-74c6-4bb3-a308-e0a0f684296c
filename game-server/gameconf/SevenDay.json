{"SevenDayTask": {"101": {"ID": 101, "Day": 1, "Name": "拥有3个不同英雄", "TaskType": 12, "StatisticsType": 0, "Need": 3, "ProgressType": 1, "Active": 5, "Reward": [[6001, 5], [1, 500]], "Jump": 12, "UnlockNeed": 2}, "102": {"ID": 102, "Day": 1, "Name": "拥有5个不同英雄", "TaskType": 12, "StatisticsType": 0, "Need": 5, "ProgressType": 1, "Active": 5, "Reward": [[6001, 5], [1, 1000]], "Jump": 12, "UnlockNeed": 2}, "103": {"ID": 103, "Day": 1, "Name": "拥有10个不同英雄", "TaskType": 12, "StatisticsType": 0, "Need": 10, "ProgressType": 1, "Active": 5, "Reward": [[6001, 5], [1, 3000]], "Jump": 12, "UnlockNeed": 2}, "104": {"ID": 104, "Day": 1, "Name": "通关战役第3章", "TaskType": 1, "StatisticsType": 0, "Need": 3, "ProgressType": 1, "Active": 10, "Reward": [[6001, 10], [1, 500]], "Jump": 1, "UnlockNeed": 3}, "105": {"ID": 105, "Day": 1, "Name": "通关战役第5章", "TaskType": 1, "StatisticsType": 0, "Need": 5, "ProgressType": 1, "Active": 10, "Reward": [[6001, 10], [1, 1000]], "Jump": 1, "UnlockNeed": 3}, "106": {"ID": 106, "Day": 1, "Name": "通关战役第8章", "TaskType": 1, "StatisticsType": 0, "Need": 8, "ProgressType": 1, "Active": 10, "Reward": [[6001, 10], [1, 3000]], "Jump": 1, "UnlockNeed": 3}, "107": {"ID": 107, "Day": 1, "Name": "通关青铜2塔", "TaskType": 8, "StatisticsType": 0, "Need": 2, "ProgressType": 0, "Active": 10, "Reward": [[6001, 10], [1003, 4]], "Jump": 5, "UnlockNeed": 15}, "108": {"ID": 108, "Day": 1, "Name": "通关青铜3塔", "TaskType": 8, "StatisticsType": 0, "Need": 3, "ProgressType": 0, "Active": 10, "Reward": [[6001, 10], [1003, 6]], "Jump": 5, "UnlockNeed": 15}, "201": {"ID": 201, "Day": 2, "Name": "通关战役第10章", "TaskType": 1, "StatisticsType": 0, "Need": 10, "ProgressType": 1, "Active": 10, "Reward": [[6001, 10], [1, 4000]], "Jump": 1, "UnlockNeed": 3}, "202": {"ID": 202, "Day": 2, "Name": "通关战役第12章", "TaskType": 1, "StatisticsType": 0, "Need": 12, "ProgressType": 1, "Active": 10, "Reward": [[6001, 10], [1, 6000]], "Jump": 1, "UnlockNeed": 3}, "203": {"ID": 203, "Day": 2, "Name": "装备总等级20级", "TaskType": 17, "StatisticsType": 0, "Need": 20, "ProgressType": 1, "Active": 5, "Reward": [[6001, 5], [400, 20]], "Jump": 2, "UnlockNeed": 73}, "204": {"ID": 204, "Day": 2, "Name": "装备总等级50级", "TaskType": 17, "StatisticsType": 0, "Need": 50, "ProgressType": 1, "Active": 5, "Reward": [[6001, 5], [400, 30]], "Jump": 2, "UnlockNeed": 73}, "205": {"ID": 205, "Day": 2, "Name": "装备总等级100级", "TaskType": 17, "StatisticsType": 0, "Need": 100, "ProgressType": 1, "Active": 5, "Reward": [[6001, 5], [400, 50]], "Jump": 2, "UnlockNeed": 73}, "206": {"ID": 206, "Day": 2, "Name": "总战力达到8000", "TaskType": 13, "StatisticsType": 0, "Need": 8000, "ProgressType": 1, "Active": 10, "Reward": [[6001, 10], [51, 1]], "Jump": 2, "UnlockNeed": 5}, "207": {"ID": 207, "Day": 2, "Name": "总战力达到10000", "TaskType": 13, "StatisticsType": 0, "Need": 10000, "ProgressType": 1, "Active": 10, "Reward": [[6001, 10], [51, 1]], "Jump": 2, "UnlockNeed": 5}, "208": {"ID": 208, "Day": 2, "Name": "总战力达到12000", "TaskType": 13, "StatisticsType": 0, "Need": 12000, "ProgressType": 1, "Active": 10, "Reward": [[6001, 10], [51, 1]], "Jump": 2, "UnlockNeed": 5}, "301": {"ID": 301, "Day": 3, "Name": "通关战役第14章", "TaskType": 1, "StatisticsType": 0, "Need": 14, "ProgressType": 1, "Active": 10, "Reward": [[6001, 10], [1, 8000]], "Jump": 1, "UnlockNeed": 3}, "302": {"ID": 302, "Day": 3, "Name": "通关战役第16章", "TaskType": 1, "StatisticsType": 0, "Need": 16, "ProgressType": 1, "Active": 10, "Reward": [[6001, 10], [1, 10000]], "Jump": 1, "UnlockNeed": 3}, "303": {"ID": 303, "Day": 3, "Name": "开启5次宝箱", "TaskType": 19, "StatisticsType": 1, "Need": 5, "ProgressType": 1, "Active": 5, "Reward": [[6001, 5], [52, 1]], "Jump": 8, "UnlockNeed": 51}, "304": {"ID": 304, "Day": 3, "Name": "开启10次宝箱", "TaskType": 19, "StatisticsType": 1, "Need": 10, "ProgressType": 1, "Active": 5, "Reward": [[6001, 5], [52, 1]], "Jump": 8, "UnlockNeed": 51}, "305": {"ID": 305, "Day": 3, "Name": "开启20次宝箱", "TaskType": 19, "StatisticsType": 1, "Need": 20, "ProgressType": 1, "Active": 5, "Reward": [[6001, 5], [52, 1]], "Jump": 8, "UnlockNeed": 51}, "306": {"ID": 306, "Day": 3, "Name": "通关白银1塔", "TaskType": 8, "StatisticsType": 0, "Need": 4, "ProgressType": 0, "Active": 10, "Reward": [[6001, 10], [1003, 4]], "Jump": 5, "UnlockNeed": 15}, "307": {"ID": 307, "Day": 3, "Name": "通关白银2塔", "TaskType": 8, "StatisticsType": 0, "Need": 5, "ProgressType": 0, "Active": 10, "Reward": [[6001, 10], [1003, 6]], "Jump": 5, "UnlockNeed": 15}, "308": {"ID": 308, "Day": 3, "Name": "通关白银3塔", "TaskType": 8, "StatisticsType": 0, "Need": 6, "ProgressType": 0, "Active": 10, "Reward": [[6001, 10], [1004, 2]], "Jump": 5, "UnlockNeed": 15}, "401": {"ID": 401, "Day": 4, "Name": "通关战役第18章", "TaskType": 1, "StatisticsType": 0, "Need": 18, "ProgressType": 1, "Active": 10, "Reward": [[6001, 10], [1, 12000]], "Jump": 1, "UnlockNeed": 3}, "402": {"ID": 402, "Day": 4, "Name": "通关战役第19章", "TaskType": 1, "StatisticsType": 0, "Need": 19, "ProgressType": 1, "Active": 10, "Reward": [[6001, 10], [1, 15000]], "Jump": 1, "UnlockNeed": 3}, "403": {"ID": 403, "Day": 4, "Name": "英雄最高星级达到2星", "TaskType": 23, "StatisticsType": 0, "Need": 2, "ProgressType": 0, "Active": 10, "Reward": [[6001, 10], [400, 30]], "Jump": 2, "UnlockNeed": 2}, "404": {"ID": 404, "Day": 4, "Name": "英雄最高星级达到3星", "TaskType": 23, "StatisticsType": 0, "Need": 3, "ProgressType": 0, "Active": 10, "Reward": [[6001, 10], [400, 50]], "Jump": 2, "UnlockNeed": 2}, "405": {"ID": 405, "Day": 4, "Name": "英雄最高星级达到4星", "TaskType": 23, "StatisticsType": 0, "Need": 4, "ProgressType": 0, "Active": 10, "Reward": [[6001, 10], [400, 80]], "Jump": 2, "UnlockNeed": 2}, "406": {"ID": 406, "Day": 4, "Name": "空中夺宝夺取成功1次", "TaskType": 22, "StatisticsType": 1, "Need": 1, "ProgressType": 1, "Active": 10, "Reward": [[6001, 10], [405, 10]], "Jump": 14, "UnlockNeed": 70}, "407": {"ID": 407, "Day": 4, "Name": "空中夺宝夺取成功2次", "TaskType": 22, "StatisticsType": 1, "Need": 2, "ProgressType": 1, "Active": 10, "Reward": [[6001, 10], [405, 20]], "Jump": 14, "UnlockNeed": 70}, "408": {"ID": 408, "Day": 4, "Name": "空中夺宝夺取成功3次", "TaskType": 22, "StatisticsType": 1, "Need": 3, "ProgressType": 1, "Active": 10, "Reward": [[6001, 10], [405, 30]], "Jump": 14, "UnlockNeed": 70}, "501": {"ID": 501, "Day": 5, "Name": "通关战役第20章", "TaskType": 1, "StatisticsType": 0, "Need": 20, "ProgressType": 1, "Active": 10, "Reward": [[6001, 10], [1, 20000]], "Jump": 1, "UnlockNeed": 3}, "502": {"ID": 502, "Day": 5, "Name": "通关战役第21章", "TaskType": 1, "StatisticsType": 0, "Need": 21, "ProgressType": 1, "Active": 10, "Reward": [[6001, 10], [1, 25000]], "Jump": 1, "UnlockNeed": 3}, "503": {"ID": 503, "Day": 5, "Name": "黑市累计购买5次", "TaskType": 10, "StatisticsType": 1, "Need": 5, "ProgressType": 1, "Active": 10, "Reward": [[6001, 10], [2, 100]], "Jump": 9, "UnlockNeed": 53}, "504": {"ID": 504, "Day": 5, "Name": "黑市累计购买10次", "TaskType": 10, "StatisticsType": 1, "Need": 10, "ProgressType": 1, "Active": 10, "Reward": [[6001, 10], [2, 200]], "Jump": 9, "UnlockNeed": 53}, "505": {"ID": 505, "Day": 5, "Name": "黑市累计购买15次", "TaskType": 10, "StatisticsType": 1, "Need": 15, "ProgressType": 1, "Active": 10, "Reward": [[6001, 10], [2, 300]], "Jump": 9, "UnlockNeed": 53}, "506": {"ID": 506, "Day": 5, "Name": "通关黄金1塔", "TaskType": 8, "StatisticsType": 0, "Need": 7, "ProgressType": 0, "Active": 10, "Reward": [[6001, 10], [1003, 4]], "Jump": 5, "UnlockNeed": 15}, "507": {"ID": 507, "Day": 5, "Name": "通关黄金2塔", "TaskType": 8, "StatisticsType": 0, "Need": 8, "ProgressType": 0, "Active": 10, "Reward": [[6001, 10], [1003, 6]], "Jump": 5, "UnlockNeed": 15}, "508": {"ID": 508, "Day": 5, "Name": "通关黄金3塔", "TaskType": 8, "StatisticsType": 0, "Need": 9, "ProgressType": 0, "Active": 10, "Reward": [[6001, 10], [1004, 2]], "Jump": 5, "UnlockNeed": 15}, "601": {"ID": 601, "Day": 6, "Name": "通关战役第22章", "TaskType": 1, "StatisticsType": 0, "Need": 22, "ProgressType": 1, "Active": 10, "Reward": [[6001, 10], [1, 30000]], "Jump": 1, "UnlockNeed": 3}, "602": {"ID": 602, "Day": 6, "Name": "通关战役第23章", "TaskType": 1, "StatisticsType": 0, "Need": 23, "ProgressType": 1, "Active": 10, "Reward": [[6001, 10], [1, 35000]], "Jump": 1, "UnlockNeed": 3}, "603": {"ID": 603, "Day": 6, "Name": "迷宫通关1次", "TaskType": 14, "StatisticsType": 1, "Need": 1, "ProgressType": 1, "Active": 10, "Reward": [[6001, 10], [405, 10]], "Jump": 11, "UnlockNeed": 17}, "604": {"ID": 604, "Day": 6, "Name": "迷宫通关2次", "TaskType": 14, "StatisticsType": 1, "Need": 2, "ProgressType": 1, "Active": 10, "Reward": [[6001, 10], [405, 20]], "Jump": 11, "UnlockNeed": 17}, "605": {"ID": 605, "Day": 6, "Name": "迷宫通关3次", "TaskType": 14, "StatisticsType": 1, "Need": 3, "ProgressType": 1, "Active": 10, "Reward": [[6001, 10], [405, 30]], "Jump": 11, "UnlockNeed": 17}, "606": {"ID": 606, "Day": 6, "Name": "拥有2个不同藏品", "TaskType": 20, "StatisticsType": 0, "Need": 2, "ProgressType": 1, "Active": 10, "Reward": [[6001, 10], [53, 1]], "Jump": 13, "UnlockNeed": 17}, "607": {"ID": 607, "Day": 6, "Name": "拥有3个不同藏品", "TaskType": 20, "StatisticsType": 0, "Need": 3, "ProgressType": 1, "Active": 10, "Reward": [[6001, 10], [53, 1]], "Jump": 13, "UnlockNeed": 17}, "608": {"ID": 608, "Day": 6, "Name": "拥有5个不同藏品", "TaskType": 20, "StatisticsType": 0, "Need": 5, "ProgressType": 1, "Active": 10, "Reward": [[6001, 10], [53, 2]], "Jump": 13, "UnlockNeed": 17}, "701": {"ID": 701, "Day": 7, "Name": "通关战役第24章", "TaskType": 1, "StatisticsType": 0, "Need": 24, "ProgressType": 1, "Active": 10, "Reward": [[6001, 10], [1, 40000]], "Jump": 1, "UnlockNeed": 3}, "702": {"ID": 702, "Day": 7, "Name": "通关战役第25章", "TaskType": 1, "StatisticsType": 0, "Need": 25, "ProgressType": 1, "Active": 10, "Reward": [[6001, 10], [1, 50000]], "Jump": 1, "UnlockNeed": 3}, "703": {"ID": 703, "Day": 7, "Name": "通关黄金4塔", "TaskType": 8, "StatisticsType": 0, "Need": 10, "ProgressType": 0, "Active": 10, "Reward": [[6001, 10], [1003, 8]], "Jump": 5, "UnlockNeed": 15}, "704": {"ID": 704, "Day": 7, "Name": "通关黄金5塔", "TaskType": 8, "StatisticsType": 0, "Need": 11, "ProgressType": 0, "Active": 10, "Reward": [[6001, 10], [1004, 2]], "Jump": 5, "UnlockNeed": 15}, "705": {"ID": 705, "Day": 7, "Name": "英雄最高星级达到5星", "TaskType": 23, "StatisticsType": 0, "Need": 5, "ProgressType": 0, "Active": 10, "Reward": [[6001, 10], [1025, 1]], "Jump": 2, "UnlockNeed": 2}, "706": {"ID": 706, "Day": 7, "Name": "英雄最高星级达到6星", "TaskType": 23, "StatisticsType": 0, "Need": 6, "ProgressType": 0, "Active": 10, "Reward": [[6001, 10], [1030, 1]], "Jump": 2, "UnlockNeed": 2}, "707": {"ID": 707, "Day": 7, "Name": "总战力达到30000", "TaskType": 13, "StatisticsType": 0, "Need": 30000, "ProgressType": 1, "Active": 10, "Reward": [[6001, 10], [1007, 10]], "Jump": 2, "UnlockNeed": 5}, "708": {"ID": 708, "Day": 7, "Name": "总战力达到40000", "TaskType": 13, "StatisticsType": 0, "Need": 40000, "ProgressType": 1, "Active": 10, "Reward": [[6001, 10], [1004, 6]], "Jump": 2, "UnlockNeed": 5}}, "SevenDayActiveReward": {"1": {"ID": 1, "NeedActive": 100, "Reward": [[2, 500]], "IfEquip": 0}, "2": {"ID": 2, "NeedActive": 200, "Reward": [[2, 1000]], "IfEquip": 0}, "3": {"ID": 3, "NeedActive": 300, "Reward": [[2, 1500]], "IfEquip": 0}, "4": {"ID": 4, "NeedActive": 400, "Reward": [[2, 1500]], "IfEquip": 0}, "5": {"ID": 5, "NeedActive": 500, "Reward": [[51, 10]], "IfEquip": 0}}}