{"fishingBase": {"1001": {"ID": 1001, "FishBaitItem": 9100101, "FishPointItem": 9100102, "FishLineItem": 9100103, "FishBaitPrice": 100, "BuyLimit": [[0, 6000, 10], [6001, 12000, 20], [12001, 18000, 30], [18001, 24000, 40], [24001, 999999999, 50]], "FishFailRevive": [[1, 1, 50], [2, 2, 100], [3, 999, 100]], "ReviveStrenth": 100, "FishUp": [105, 110], "FishUseMulti": [[0, 9, 1], [10, 29, 2], [30, 49, 5], [50, 9999999, 10]], "DistanceDefault": 70, "DistanceFail": 100, "Point": 100, "DefaultFishBait": 5, "GalleryReward": 1, "MailItems": [[9100101, 2000], [9100102, 1], [9100103, 1], [9100301, 1000], [9100302, 2000], [9100303, 3000], [9100304, 3000], [9100305, 5000], [9100306, 5000]], "MailItemsId": ["t-20241125125459"], "FishType": 1, "Bait": 0, "FishRod": 9100301}, "2001": {"ID": 2001, "FishBaitItem": 9100101, "FishPointItem": 9100102, "FishLineItem": 9100103, "FishBaitPrice": 100, "BuyLimit": [[0, 6000, 10], [6001, 12000, 20], [12001, 18000, 30], [18001, 24000, 40], [24001, 999999999, 50]], "FishFailRevive": [[1, 1, 50], [2, 2, 100], [3, 999, 100]], "ReviveStrenth": 100, "FishUp": [105, 110], "FishUseMulti": [[0, 9, 1], [10, 29, 2], [30, 49, 5], [50, 9999999, 10]], "DistanceDefault": 70, "DistanceFail": 100, "Point": 100, "DefaultFishBait": 5, "GalleryReward": 2, "MailItems": [[9100101, 2000], [9100102, 1], [9100103, 1], [9100301, 1000], [9100302, 2000], [9100303, 3000], [9100304, 3000], [9100305, 5000], [9100306, 5000]], "MailItemsId": ["t-20241125125459"], "FishType": 2, "Bait": 0, "FishRod": 9100302}, "2002": {"ID": 2002, "FishBaitItem": 9100101, "FishPointItem": 9100102, "FishLineItem": 9100103, "FishBaitPrice": 100, "BuyLimit": [[0, 6000, 10], [6001, 12000, 20], [12001, 18000, 30], [18001, 24000, 40], [24001, 999999999, 50]], "FishFailRevive": [[1, 1, 50], [2, 2, 100], [3, 999, 100]], "ReviveStrenth": 100, "FishUp": [105, 110], "FishUseMulti": [[0, 9, 1], [10, 29, 2], [30, 49, 5], [50, 9999999, 10]], "DistanceDefault": 70, "DistanceFail": 100, "Point": 100, "DefaultFishBait": 5, "GalleryReward": 2, "MailItems": [[9100101, 2000], [9100102, 1], [9100103, 1], [9100301, 1000], [9100302, 2000], [9100303, 3000], [9100304, 3000], [9100305, 5000], [9100306, 5000]], "MailItemsId": ["t-20241125125459"], "FishType": 2, "Bait": 0, "FishRod": 9100303}, "2003": {"ID": 2003, "FishBaitItem": 9100101, "FishPointItem": 9100102, "FishLineItem": 9100103, "FishBaitPrice": 100, "BuyLimit": [[0, 6000, 10], [6001, 12000, 20], [12001, 18000, 30], [18001, 24000, 40], [24001, 999999999, 50]], "FishFailRevive": [[1, 1, 50], [2, 2, 100], [3, 999, 100]], "ReviveStrenth": 100, "FishUp": [105, 110], "FishUseMulti": [[0, 9, 1], [10, 29, 2], [30, 49, 5], [50, 9999999, 10]], "DistanceDefault": 70, "DistanceFail": 100, "Point": 100, "DefaultFishBait": 5, "GalleryReward": 2, "MailItems": [[9100101, 2000], [9100102, 1], [9100103, 1], [9100301, 1000], [9100302, 2000], [9100303, 3000], [9100304, 3000], [9100305, 5000], [9100306, 5000]], "MailItemsId": ["t-20241125125459"], "FishType": 2, "Bait": 0, "FishRod": 9100304}, "2004": {"ID": 2004, "FishBaitItem": 9100101, "FishPointItem": 9100102, "FishLineItem": 9100103, "FishBaitPrice": 100, "BuyLimit": [[0, 6000, 10], [6001, 12000, 20], [12001, 18000, 30], [18001, 24000, 40], [24001, 999999999, 50]], "FishFailRevive": [[1, 1, 50], [2, 2, 100], [3, 999, 100]], "ReviveStrenth": 100, "FishUp": [105, 110], "FishUseMulti": [[0, 9, 1], [10, 29, 2], [30, 49, 5], [50, 9999999, 10]], "DistanceDefault": 70, "DistanceFail": 100, "Point": 100, "DefaultFishBait": 5, "GalleryReward": 2, "MailItems": [[9100101, 2000], [9100102, 1], [9100103, 1], [9100301, 1000], [9100302, 2000], [9100303, 3000], [9100304, 3000], [9100305, 5000], [9100306, 5000]], "MailItemsId": ["t-20241125125459"], "FishType": 2, "Bait": 0, "FishRod": 9100305}, "2005": {"ID": 2005, "FishBaitItem": 9100101, "FishPointItem": 9100102, "FishLineItem": 9100103, "FishBaitPrice": 100, "BuyLimit": [[0, 6000, 10], [6001, 12000, 20], [12001, 18000, 30], [18001, 24000, 40], [24001, 999999999, 50]], "FishFailRevive": [[1, 1, 50], [2, 2, 100], [3, 999, 100]], "ReviveStrenth": 100, "FishUp": [105, 110], "FishUseMulti": [[0, 9, 1], [10, 29, 2], [30, 49, 5], [50, 9999999, 10]], "DistanceDefault": 70, "DistanceFail": 100, "Point": 100, "DefaultFishBait": 5, "GalleryReward": 2, "MailItems": [[9100101, 2000], [9100102, 1], [9100103, 1], [9100301, 1000], [9100302, 2000], [9100303, 3000], [9100304, 3000], [9100305, 5000], [9100306, 5000]], "MailItemsId": ["t-20241125125459"], "FishType": 2, "Bait": 0, "FishRod": 9100306}}, "fish": {"9100201": {"id": 9100201, "notes": "鲭鱼", "fishType": 1, "type": 1, "number": 15, "weight": 1500, "weightFloat": [75, 125], "initialDamage": 0, "strength": 1250, "speed": 400}, "9100202": {"id": 9100202, "notes": "小丑鱼", "fishType": 1, "type": 1, "number": 10, "weight": 3000, "weightFloat": [75, 125], "initialDamage": 0, "strength": 1250, "speed": 400}, "9100203": {"id": 9100203, "notes": "金枪鱼", "fishType": 2, "type": 1, "number": 15, "weight": 2400, "weightFloat": [75, 125], "initialDamage": 0, "strength": 1450, "speed": 900}, "9100204": {"id": 9100204, "notes": "豚鱼", "fishType": 2, "type": 1, "number": 10, "weight": 4800, "weightFloat": [75, 125], "initialDamage": 0, "strength": 1450, "speed": 900}, "9100205": {"id": 9100205, "notes": "石斑鱼", "fishType": 3, "type": 1, "number": 15, "weight": 4000, "weightFloat": [75, 125], "initialDamage": 0, "strength": 1650, "speed": 1300}, "9100206": {"id": 9100206, "notes": "彩虹水母", "fishType": 1, "type": 2, "number": 5, "weight": 4500, "weightFloat": [75, 125], "initialDamage": 0, "strength": 1250, "speed": 400}, "9100207": {"id": 9100207, "notes": "虹鳟", "fishType": 3, "type": 2, "number": 10, "weight": 6000, "weightFloat": [75, 125], "initialDamage": 0, "strength": 1650, "speed": 1300}, "9100208": {"id": 9100208, "notes": "比目鱼", "fishType": 3, "type": 1, "number": 5, "weight": 12000, "weightFloat": [75, 125], "initialDamage": 0, "strength": 1650, "speed": 1300}, "9100209": {"id": 9100209, "notes": "多宝鱼", "fishType": 4, "type": 1, "number": 30, "weight": 8000, "weightFloat": [75, 125], "initialDamage": 0, "strength": 1850, "speed": 1750}, "9100210": {"id": 9100210, "notes": "鲳鱼", "fishType": 4, "type": 1, "number": 20, "weight": 12000, "weightFloat": [75, 125], "initialDamage": 0, "strength": 1850, "speed": 1750}, "9100211": {"id": 9100211, "notes": "箱鲀鱼", "fishType": 4, "type": 2, "number": 10, "weight": 24000, "weightFloat": [75, 125], "initialDamage": 0, "strength": 1850, "speed": 1750}, "9100212": {"id": 9100212, "notes": "海鳗", "fishType": 4, "type": 2, "number": 12, "weight": 40000, "weightFloat": [75, 125], "initialDamage": 0, "strength": 1900, "speed": 1800}, "9100213": {"id": 9100213, "notes": "绿颌鱼", "fishType": 1, "type": 2, "number": 6, "weight": 7500, "weightFloat": [75, 125], "initialDamage": 0, "strength": 1300, "speed": 450}, "9100214": {"id": 9100214, "notes": "黄鳍鱼", "fishType": 2, "type": 2, "number": 5, "weight": 7200, "weightFloat": [75, 125], "initialDamage": 0, "strength": 1450, "speed": 900}, "9100215": {"id": 9100215, "notes": "马鲛鱼", "fishType": 3, "type": 2, "number": 6, "weight": 20000, "weightFloat": [75, 125], "initialDamage": 0, "strength": 1700, "speed": 1350}, "9100216": {"id": 9100216, "notes": "旗鱼", "fishType": 1, "type": 2, "number": 3, "weight": 15000, "weightFloat": [75, 125], "initialDamage": 0, "strength": 1300, "speed": 450}, "9100217": {"id": 9100217, "notes": "翻车鱼", "fishType": 2, "type": 2, "number": 6, "weight": 12000, "weightFloat": [75, 125], "initialDamage": 0, "strength": 1500, "speed": 950}, "9100218": {"id": 9100218, "notes": "鳄龟", "fishType": 3, "type": 2, "number": 3, "weight": 32000, "weightFloat": [75, 125], "initialDamage": 0, "strength": 1700, "speed": 1350}, "9100219": {"id": 9100219, "notes": "鮟鱇鱼", "fishType": 2, "type": 2, "number": 3, "weight": 24000, "weightFloat": [75, 125], "initialDamage": 0, "strength": 1500, "speed": 950}, "9100220": {"id": 9100220, "notes": "魔鬼鱼", "fishType": 4, "type": 2, "number": 6, "weight": 64000, "weightFloat": [75, 125], "initialDamage": 0, "strength": 1900, "speed": 1800}, "9100221": {"id": 9100221, "notes": "食人鱼王", "fishType": 3, "type": 3, "number": 1, "weight": 72000, "weightFloat": [75, 125], "initialDamage": 0, "strength": 1750, "speed": 1400}, "9100222": {"id": 9100222, "notes": "银带鱼王", "fishType": 2, "type": 3, "number": 1, "weight": 48000, "weightFloat": [75, 125], "initialDamage": 0, "strength": 1550, "speed": 1000}, "9100223": {"id": 9100223, "notes": "大王乌贼", "fishType": 1, "type": 3, "number": 1, "weight": 30000, "weightFloat": [75, 125], "initialDamage": 0, "strength": 1350, "speed": 550}, "9100224": {"id": 9100224, "notes": "独角鲸", "fishType": 4, "type": 3, "number": 2, "weight": 144000, "weightFloat": [75, 125], "initialDamage": 0, "strength": 1950, "speed": 1850}}, "fishRod": {"9100301": {"id": 9100301, "type": 1, "hp": 100, "hpRestore": 30, "strength": 1200, "speed": 1100, "tiresSpeed": 3000}, "9100302": {"id": 9100302, "type": 2, "hp": 120, "hpRestore": 40, "strength": 1300, "speed": 1200, "tiresSpeed": 3000}, "9100303": {"id": 9100303, "type": 3, "hp": 150, "hpRestore": 50, "strength": 1400, "speed": 1300, "tiresSpeed": 3000}, "9100304": {"id": 9100304, "type": 3, "hp": 170, "hpRestore": 70, "strength": 1500, "speed": 1400, "tiresSpeed": 3000}, "9100305": {"id": 9100305, "type": 4, "hp": 180, "hpRestore": 85, "strength": 1600, "speed": 1500, "tiresSpeed": 3000}, "9100306": {"id": 9100306, "type": 4, "hp": 200, "hpRestore": 100, "strength": 1700, "speed": 1500, "tiresSpeed": 3000}}, "fishMove": {"1": {"id": 1, "strongTime": [90, 110], "struggleTime": [90, 110], "tireTime": [90, 110]}, "2": {"id": 2, "strongTime": [90, 110], "struggleTime": [90, 110], "tireTime": [90, 110]}, "3": {"id": 3, "strongTime": [60, 130], "struggleTime": [60, 130], "tireTime": [60, 130]}, "4": {"id": 4, "strongTime": [60, 130], "struggleTime": [60, 130], "tireTime": [60, 130]}, "5": {"id": 5, "strongTime": [40, 180], "struggleTime": [80, 180], "tireTime": [60, 130]}, "6": {"id": 6, "strongTime": [40, 180], "struggleTime": [80, 180], "tireTime": [100, 100]}, "7": {"id": 7, "strongTime": [40, 180], "struggleTime": [80, 180], "tireTime": [100, 100]}, "8": {"id": 8, "strongTime": [40, 180], "struggleTime": [80, 180], "tireTime": [100, 100]}}}