export class ProdConfig {

    // aws 后台 iam 身份提供商 获取
    static openIdConnectProviderArn:string = "arn:aws-cn:iam::359604426403:oidc-provider/oidc.eks.cn-north-1.amazonaws.com.cn/id/E5FEB8EBBC0C2DE60EA929981F1C0CBE";
    // 创建eks cloudformation 中 输出中获取
    static kubectlRoleArn:string = "arn:aws-cn:iam::359604426403:role/Prod-Juedisanguo-Eks-ProdCreationRoleF2BA34B3-woY90fhfznXk";

    // eks 集群名称
    static eksClusterName:string = "Prod-Juedisanguo";

    // ----------- 准正式服配置 --------- //
    // 域名
    static preDomainName:string = "prevoyagerpg.lebi4.com";
    // 内网域名
    static preInternalDomainName:string = "internal-prevoyagerpg.lebi4.com";
    // 初始节点数量
    static preReplicas:number = 1;
    // 最小节点数量
    static preMinReplicas:number = 1;
    // 凌晨定时扩容节点数量
    static preTargetReplicas:number = 1;

    // ----------- 正式服配置 --------- //
    // 域名
    static prodDomainName:string = "prodvoyagerpg.lebi4.com";
    // 内网域名
    static prodInternalDomainName:string = "internal-prodvoyagerpg.lebi4.com";
    // 初始节点数量
    static prodReplicas:number = 1;
    // 最小节点数量
    static prodMinReplicas:number = 1;
    // 凌晨定时扩容节点数量
    static prodTargetReplicas:number = 1;
}