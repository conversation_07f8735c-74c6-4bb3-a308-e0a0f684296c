export class TestConfig {
    
    // aws 后台 iam 身份提供商 获取
    static openIdConnectProviderArn:string = "arn:aws-cn:iam::359604426403:oidc-provider/oidc.eks.cn-north-1.amazonaws.com.cn/id/4AD5AF2EFA4AFF7E7495ECEEAE33AF14";

    // 创建eks cloudformation 中 输出中获取
    static kubectlRoleArn:string = "arn:aws-cn:iam::359604426403:role/Test-Juedisanguo-Eks-TestCreationRoleE90BFD6D-2d9o6KNEVhWc";

    // eks 集群名称
    static eksClusterName:string = "Test-Juedisanguo";
    // 域名
    static domainName:string = "testvoyagerpg.lebi4.com";
    // 内网域名
    static internaldomainName:string = "internal-testvoyagerpg.lebi4.com";
    // 初始节点数量
    static replicas:number = 1;
    // 最小节点数量
    static minReplicas:number = 1;
    // 凌晨定时扩容节点数量
    static targetReplicas:number = 1;

    
}