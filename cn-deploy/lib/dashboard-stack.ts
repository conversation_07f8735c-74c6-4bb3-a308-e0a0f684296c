import {Stack, StackProps} from "aws-cdk-lib";
import {Construct} from "constructs";
import {
    ServiceNestedStack,
    ServiceNestedStackProps
} from "hs-cdk-dashboards"
import {AlarmRule, CompositeAlarm} from "aws-cdk-lib/aws-cloudwatch";
import {SnsAction} from "aws-cdk-lib/aws-cloudwatch-actions";
import {Topic} from "aws-cdk-lib/aws-sns";
import {DashboardServiceNestedStack, DashboardServiceNestedStackProps} from "./dashboard-service-nested-stack";

export interface DashboardStackProps extends StackProps {
    appName: string
    appEnv: string
    namespace: string

    topicArn: string,

    gameNestedStackProps: ServiceNestedStackProps
    cronNestedStackProps: DashboardServiceNestedStackProps
    battles: ServiceNestedStackProps[]

    enableAlarm?: boolean;

}

export class DashboardStack extends Stack {
    constructor(scope: Construct, id: string, props: DashboardStackProps) {
        super(scope, id, props);

        const s1 =  new ServiceNestedStack(this, "service", props.gameNestedStackProps)

        const c1 =  new DashboardServiceNestedStack(this, "cron", props.cronNestedStackProps)

        const topicArn = props.topicArn;
        const topic = Topic.fromTopicArn(this, "topic", topicArn);

        if(props.enableAlarm === undefined || props.enableAlarm) {
            console.log(`${props.appEnv} enable alarm`)

            const compositeAlarm = new CompositeAlarm(this, "AllAlarms", {
                compositeAlarmName: `${props.appEnv}-${props.appName}-AllAlarms`,
                alarmRule: AlarmRule.anyOf(
                    ...s1.alarms,
                )
            })

            compositeAlarm.addAlarmAction(new SnsAction(topic))
            compositeAlarm.addOkAction(new SnsAction(topic))



            for (const prop of props.battles) {
                const bs =  new ServiceNestedStack(this, prop.appName, prop)
                const compositeBattleAlarm = new CompositeAlarm(this, `${prop.appName}BattleAlarms`, {
                    compositeAlarmName: `${prop.appName}Alarms`,
                    alarmRule: AlarmRule.anyOf(
                        ...bs.alarms,
                    )
                })
                compositeBattleAlarm.addAlarmAction(new SnsAction(topic))
                compositeBattleAlarm.addOkAction(new SnsAction(topic))
            }


            const compositeCronAlarm = new CompositeAlarm(this, "CronAlarms", {
                compositeAlarmName: `${props.appEnv}-${props.appName}-CronAlarms`,
                alarmRule: AlarmRule.anyOf(
                    ...c1.alarms,
                )
            })


            compositeCronAlarm.addAlarmAction(new SnsAction(topic))
            compositeCronAlarm.addOkAction(new SnsAction(topic))

        } else {
            console.log(`${props.appEnv} disable alarm`)
        }

    }
}