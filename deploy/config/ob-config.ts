export class ObConfig {
    
    // aws 后台 iam 身份提供商 获取
    static openIdConnectProviderArn:string = "arn:aws:iam::905418351021:oidc-provider/oidc.eks.eu-central-1.amazonaws.com/id/A35E3B6679D0EA45137723BE8D4916A1";

    // 创建eks cloudformation 中 输出中获取
    static kubectlRoleArn:string = "arn:aws:iam::905418351021:role/Test-Badguy-Eks-TestCreationRoleE90BFD6D-C9nJN6QVdKt7";

    // eks 集群名称
    static eksClusterName:string = "Test-Badguy";
    // 域名
    static domainName:string = "ob-monster-rpg-game.badguysvc.com";
    // 内网域名
    static internaldomainName:string = "internal-ob-monster-rpg-game.badguysvc.com";
    // 初始节点数量
    static replicas:number = 1;
    // 最小节点数量
    static minReplicas:number = 1;
    // 凌晨定时扩容节点数量
    static targetReplicas:number = 1;

    
}